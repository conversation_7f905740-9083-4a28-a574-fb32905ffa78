<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit;
}

// Get database connection
$conn = connectDB();

// Get branch name from session
$branch_name = $_SESSION['branch_name'] ?? 'Main Branch';

// Initialize filters
$search = $_GET['search'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$payment_filter = $_GET['payment'] ?? '';

// Build base query for fetching tuition payments
$query = "SELECT o.id, o.order_number, o.invoice_number, o.customer_name, o.customer_email, 
          o.customer_phone, o.payment_type, o.total_amount, o.created_at, o.status, u.username as created_by
          FROM orders o 
          LEFT JOIN users u ON o.created_by = u.id
          LEFT JOIN order_items oi ON o.id = oi.order_id
          LEFT JOIN products p ON oi.product_id = p.id
          WHERE o.branch_name = ? AND p.name LIKE '%tuition%'";

// Add search condition if provided
if (!empty($search)) {
    $query .= " AND (o.customer_name LIKE ? OR o.customer_email LIKE ? OR o.customer_phone LIKE ?)";
}

// Add date range conditions if provided
if (!empty($date_from)) {
    $query .= " AND DATE(o.created_at) >= ?";
}

if (!empty($date_to)) {
    $query .= " AND DATE(o.created_at) <= ?";
}

// Add payment type filter if provided
if (!empty($payment_filter)) {
    $query .= " AND o.payment_type = ?";
}

// Group by order to avoid duplicates
$query .= " GROUP BY o.id";

// Order by created_at descending
$query .= " ORDER BY o.created_at DESC";

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameters
$bind_types = "s"; // branch_name
$bind_values = [$branch_name];

if (!empty($search)) {
    $search_term = "%{$search}%";
    $bind_types .= "sss"; // customer_name, customer_email, customer_phone
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
}

if (!empty($date_from)) {
    $bind_types .= "s"; // date_from
    $bind_values[] = $date_from;
}

if (!empty($date_to)) {
    $bind_types .= "s"; // date_to
    $bind_values[] = $date_to;
}

if (!empty($payment_filter)) {
    $bind_types .= "s"; // payment_type
    $bind_values[] = $payment_filter;
}

// Execute statement
$stmt->bind_param($bind_types, ...$bind_values);
$stmt->execute();
$result = $stmt->get_result();
$ledger_entries = $result->fetch_all(MYSQLI_ASSOC);

// Set headers for Excel download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="tuition_ledger_' . date('Y-m-d') . '.csv"');

// Create a file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add UTF-8 BOM to fix Excel encoding issues
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Set column headers
fputcsv($output, [
    'Invoice #',
    'Date',
    'Student Name',
    'Teacher/Mentor',
    'Lesson/Activity',
    'Payment Type',
    'Amount',
    'Created By',
    'Status'
]);

// Add data rows
foreach ($ledger_entries as $entry) {
    fputcsv($output, [
        $entry['invoice_number'] ?? $entry['order_number'],
        date('Y-m-d H:i:s', strtotime($entry['created_at'])),
        $entry['customer_name'],
        $entry['customer_email'] ?? 'N/A',
        $entry['customer_phone'] ?? 'N/A',
        $entry['payment_type'],
        $entry['total_amount'],
        $entry['created_by'] ?? 'N/A',
        ucfirst($entry['status'])
    ]);
}

// Close database connection
closeDB($conn);

// Close the file pointer
fclose($output);
exit;