<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit;
}

// Check if export parameter is set
if (!isset($_GET['export']) || $_GET['export'] !== 'csv') {
    header('Location: sales_list_v2.php');
    exit;
}

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$date_from = date('Y-m-d', strtotime('-7 days')); // Default to last 7 days
$date_to = date('Y-m-d');   // Default to today
$payment_filter = '';
$status_filter = '';
$customer_filter = '';

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    $date_from = $_GET['date_from'];
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    $date_to = $_GET['date_to'];
}

if (isset($_GET['payment']) && !empty($_GET['payment'])) {
    $payment_filter = $_GET['payment'];
}

if (isset($_GET['status']) && !empty($_GET['status'])) {
    $status_filter = $_GET['status'];
}

if (isset($_GET['customer']) && !empty($_GET['customer'])) {
    $customer_filter = $_GET['customer'];
}

// Build base query for fetching orders
$query = "SELECT o.*, u.username 
          FROM orders o 
          LEFT JOIN users u ON o.created_by = u.id 
          WHERE o.branch_name = ?";

// Prepare bind parameters array
$bind_types = "s"; // Start with branch_name
$bind_values = array($branch_name);

// Add date range condition
$query .= " AND DATE(o.created_at) BETWEEN ? AND ?";
$bind_types .= "ss"; // Add two string parameters
$bind_values[] = $date_from;
$bind_values[] = $date_to;

// Add search condition if provided
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (o.order_number LIKE ? OR o.invoice_number LIKE ? OR 
                o.customer_name LIKE ? OR o.customer_phone LIKE ? OR 
                o.customer_email LIKE ?)";
    $bind_types .= "sssss"; // Add five string parameters
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
}

// Add payment filter if provided
if (!empty($payment_filter)) {
    $query .= " AND o.payment_type = ?";
    $bind_types .= "s"; // Add one string parameter
    $bind_values[] = $payment_filter;
}

// Add status filter if provided
if (!empty($status_filter)) {
    $query .= " AND o.status = ?";
    $bind_types .= "s"; // Add one string parameter
    $bind_values[] = $status_filter;
}

// Add customer filter if provided
if (!empty($customer_filter)) {
    $customer_term = "%{$customer_filter}%";
    $query .= " AND o.customer_name LIKE ?";
    $bind_types .= "s"; // Add one string parameter
    $bind_values[] = $customer_term;
}

// Order by created_at descending
$query .= " ORDER BY o.created_at DESC";

// Prepare and execute the statement
$stmt = $conn->prepare($query);
$stmt->bind_param($bind_types, ...$bind_values);
$stmt->execute();
$result = $stmt->get_result();
$orders = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="sales_report_' . date('Y-m-d') . '.csv"');

// Create a file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add UTF-8 BOM to fix Excel encoding issues
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Set column headers
fputcsv($output, [
    'Order #',
    'Invoice #',
    'Date',
    'Customer Name',
    'Customer Phone',
    'Customer Email',
    'Payment Type',
    'Status',
    'Total Amount',
    'Created By',
    'Branch'
]);

// Add data rows
foreach ($orders as $order) {
    fputcsv($output, [
        $order['order_number'],
        $order['invoice_number'] ?? 'N/A',
        $order['created_at'],
        $order['customer_name'],
        $order['customer_phone'] ?? 'N/A',
        $order['customer_email'] ?? 'N/A',
        $order['payment_type'],
        $order['status'],
        $order['total_amount'],
        $order['username'] ?? 'N/A',
        $order['branch_name']
    ]);
}

// Close database connection
closeDB($conn);

// Close the file pointer
fclose($output);
exit;
?>
