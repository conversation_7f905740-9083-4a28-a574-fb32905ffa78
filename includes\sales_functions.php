<?php

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * Fetches products for the sales page with pagination and search.
 * Uses prepared statements.
 */
function getSalesProducts(mysqli $conn, string $branch_name, ?string $search, int $page, int $items_per_page): array
{
    $offset = ($page - 1) * $items_per_page;
    $products = [];
    $params = [];
    $types = "";

    // Base query
    $base_query = "SELECT p.*, c.name as category_name, b.name as brand_name
                   FROM products p
                   LEFT JOIN categories c ON p.category_id = c.id
                   LEFT JOIN brands b ON p.brand_id = b.id
                   WHERE p.branch_name = ?";
    $params[] = $branch_name;
    $types .= "s";

    // Add search condition
    if (!empty($search)) {
        $search_term = "%{$search}%";
        $base_query .= " AND (p.name LIKE ? OR p.sku LIKE ?)";
        $params[] = $search_term;
        $params[] = $search_term;
        $types .= "ss";
    }

    // Count query
    $count_query = "SELECT COUNT(*) as total FROM products p WHERE p.branch_name = ?";
    $count_params = [$branch_name];
    $count_types = "s";
    if (!empty($search)) {
        $search_term = "%{$search}%";
        $count_query .= " AND (p.name LIKE ? OR p.sku LIKE ?)";
        $count_params[] = $search_term;
        $count_params[] = $search_term;
        $count_types .= "ss";
    }

    // Get total count
    $stmt_count = $conn->prepare($count_query);
    $stmt_count->bind_param($count_types, ...$count_params);
    $stmt_count->execute();
    $count_result = $stmt_count->get_result();
    $total_items = $count_result->fetch_assoc()['total'] ?? 0;
    $stmt_count->close();

    $total_pages = ceil($total_items / $items_per_page);

    // Add ordering and pagination to main query
    $products_query = $base_query . " ORDER BY p.name ASC LIMIT ? OFFSET ?";
    $params[] = $items_per_page;
    $params[] = $offset;
    $types .= "ii";

    // Get products
    $stmt_products = $conn->prepare($products_query);
    $stmt_products->bind_param($types, ...$params);
    $stmt_products->execute();
    $products_result = $stmt_products->get_result();
    while ($row = $products_result->fetch_assoc()) {
        $products[] = $row;
    }
    $stmt_products->close();

    return [
        'products' => $products,
        'total_items' => $total_items,
        'total_pages' => $total_pages
    ];
}

/**
 * Adds a product to the session cart.
 */
function addToCart(mysqli $conn, int $product_id, int $quantity): bool
{
    $product_query = "SELECT id, name, unit_price, branch_name FROM products WHERE id = ?";
    $stmt = $conn->prepare($product_query);
    $stmt->bind_param("i", $product_id);
    $stmt->execute();
    $product_result = $stmt->get_result();

    if ($product_result->num_rows > 0) {
        $product = $product_result->fetch_assoc();
        $stmt->close();

        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = [];
        }

        $found = false;
        foreach ($_SESSION['cart'] as $key => $item) {
            if ($item['id'] == $product_id) {
                // Note: Consider checking available quantity before increasing
                $_SESSION['cart'][$key]['quantity'] += $quantity;
                // Recalculate total based on potentially updated price later if needed
                $_SESSION['cart'][$key]['total'] = $_SESSION['cart'][$key]['quantity'] * $_SESSION['cart'][$key]['price'];
                $found = true;
                break;
            }
        }

        if (!$found) {
            $_SESSION['cart'][] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'price' => $product['unit_price'], // Store initial price
                'quantity' => $quantity,
                'total' => $product['unit_price'] * $quantity,
                'branch_name' => $product['branch_name']
            ];
        }
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

/**
 * Removes an item from the session cart by its index.
 */
function removeFromCart(int $index): bool
{
    if (isset($_SESSION['cart'][$index])) {
        unset($_SESSION['cart'][$index]);
        $_SESSION['cart'] = array_values($_SESSION['cart']); // Reindex
        return true;
    }
    return false;
}

/**
 * Clears all items from the session cart.
 */
function clearCart(): void
{
    $_SESSION['cart'] = [];
}

/**
 * Processes the checkout, creates an order, and updates stock.
 * Uses a transaction.
 */
function processCheckout(mysqli $conn, array $customer_details, array $cart_items, float $total_amount, string $branch_name, ?int $user_id): array
{
    $conn->begin_transaction();

    try {
        // Generate order number
        $order_number = 'ORD-' . date('Ymd') . '-' . rand(1000, 9999);

        // Verify user_id exists (optional, could be handled by foreign key constraint too)
        if ($user_id) {
            $user_check = "SELECT id FROM users WHERE id = ?";
            $user_stmt = $conn->prepare($user_check);
            $user_stmt->bind_param("i", $user_id);
            $user_stmt->execute();
            if ($user_stmt->get_result()->num_rows == 0) {
                $user_id = null; // User not found, set to NULL
            }
            $user_stmt->close();
        }

        // Insert order
        $order_query = "INSERT INTO orders (order_number, invoice_number, customer_name, customer_email, customer_phone, total_amount, status, created_by, payment_type, branch_name)
                        VALUES (?, ?, ?, ?, ?, ?, 'completed', ?, ?, ?)";
        $stmt = $conn->prepare($order_query);
        // Note: Binding NULL requires careful handling or separate queries if strict types are enforced
        $stmt->bind_param(
            "sssssdiss",
            $order_number,
            $customer_details['invoice_number'],
            $customer_details['customer_name'],
            $customer_details['customer_email'],
            $customer_details['customer_phone'],
            $total_amount,
            $user_id, // This will bind NULL if $user_id is null
            $customer_details['payment_type'],
            $branch_name
        );

        if (!$stmt->execute()) {
            throw new Exception("Error creating order: " . $stmt->error);
        }
        $order_id = $stmt->insert_id;
        $stmt->close();

        // Prepare statements for order items and stock update
        $item_query = "INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)";
        $item_stmt = $conn->prepare($item_query);

        $update_query = "UPDATE products SET quantity = quantity - ? WHERE id = ? AND branch_name = ?";
        $update_stmt = $conn->prepare($update_query);

        foreach ($cart_items as $item) {
            // Insert order item
            $item_stmt->bind_param("iiddd", $order_id, $item['id'], $item['quantity'], $item['price'], $item['total']);
            if (!$item_stmt->execute()) {
                throw new Exception("Error adding order item (Product ID: {$item['id']}): " . $item_stmt->error);
            }

            // Update product quantity - Ensure sufficient stock check might be needed here or via DB constraint
            $update_stmt->bind_param("iis", $item['quantity'], $item['id'], $branch_name);
             if (!$update_stmt->execute()) {
                 // Check affected rows. If 0, it might mean stock went below zero or product/branch mismatch.
                 if ($conn->affected_rows === 0) {
                     // Attempt to fetch current quantity to provide a better error message
                     $check_stock_stmt = $conn->prepare("SELECT quantity FROM products WHERE id = ? AND branch_name = ?");
                     $check_stock_stmt->bind_param("is", $item['id'], $branch_name);
                     $check_stock_stmt->execute();
                     $current_stock = $check_stock_stmt->get_result()->fetch_assoc()['quantity'] ?? null;
                     $check_stock_stmt->close();

                     if ($current_stock !== null && $current_stock < $item['quantity']) {
                         throw new Exception("Insufficient stock for product ID {$item['id']} (Available: {$current_stock}, Requested: {$item['quantity']}).");
                     } else {
                         // Generic error if stock check didn't clarify
                         throw new Exception("Error updating stock for product ID {$item['id']} (Affected Rows: 0). Possible mismatch or concurrency issue.");
                     }
                 } else {
                     // Other update error
                     throw new Exception("Error updating stock for product ID {$item['id']}: " . $update_stmt->error);
                 }
             }
        }
        $item_stmt->close();
        $update_stmt->close();

        // Commit transaction
        $conn->commit();

        // Clear only the current branch's items from the cart after successful checkout
        $_SESSION['cart'] = array_filter($_SESSION['cart'], function($cart_item) use ($branch_name) {
            return (isset($cart_item['branch_name']) && $cart_item['branch_name'] != $branch_name);
        });
        $_SESSION['cart'] = array_values($_SESSION['cart']); // Reindex

        return ['success' => true, 'order_id' => $order_id];

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Calculates the total for the cart items belonging to the current branch.
 * Also returns the filtered list of items for display.
 */
function getCartDetailsForBranch(string $branch_name): array
{
    $cart_total = 0;
    $current_branch_cart_display = [];
    if (isset($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as $index => $item) {
            // Ensure item belongs to the current branch
            if (!isset($item['branch_name']) || $item['branch_name'] == $branch_name) {
                // Optional: Fetch current price from DB here for accuracy if desired
                // $current_price = getCurrentProductPrice($conn, $item['id']);
                // $item['price'] = $current_price;
                // $item['total'] = $item['quantity'] * $current_price;

                $cart_total += $item['total'];
                $current_branch_cart_display[$index] = $item; // Keep index for removal/update links
            }
        }
    }
    return [
        'items' => $current_branch_cart_display,
        'total' => $cart_total
    ];
}

?>