<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: auth/login.php");
    exit();
}

// Include database configuration
require_once 'config/database.php';

// Set the page title
$page_title = "Backup Database";

// Include header
include 'includes/header.php';

// Function to backup the database
function backupDatabase() {
    // Database credentials from config
    $host = DB_HOST;
    $user = DB_USER;
    $pass = DB_PASS;
    $dbname = DB_NAME;
    
    // Generate filename with timestamp
    $date = date("Y-m-d-H-i-s");
    $filename = $dbname . "_backup_" . $date . ".sql";
    
    // Command to execute mysqldump
    // Use the full path to mysqldump in XAMPP
    $mysqldump_path = "D:\xampp\mysql\bin\mysqldump.exe";
    $command = "\"{$mysqldump_path}\" --host={$host} --user={$user}" . (empty($pass) ? "" : " --password={$pass}") . " {$dbname} > {$filename}";
    
    // Execute the command
    $output = [];
    $return_var = 0;
    exec($command, $output, $return_var);
    
    // Check if backup was successful
    if ($return_var === 0) {
        // If successful, force download the file
        if (file_exists($filename)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($filename));
            readfile($filename);
            
            // Delete the file after download
            unlink($filename);
            exit;
        } else {
            return ["success" => false, "message" => "Backup file was created but could not be found."];
        }
    } else {
        return ["success" => false, "message" => "Database backup failed. Error code: {$return_var}"];
    }
}

// Process backup request
$message = null;
if (isset($_POST['backup'])) {
    $result = backupDatabase();
    if (isset($result) && !$result["success"]) {
        $message = $result["message"];
    }
}
?>

<div class="container-fluid pt-4 px-4">
    <div class="row g-4">
        <div class="col-12">
            <div class="bg-light rounded h-100 p-4">
                <h5 class="mb-4"><i class="fas fa-database me-2"></i>Database Backup</h5>
                
                <?php if ($message): ?>
                <div class="alert alert-danger" role="alert">
                    <?php echo $message; ?>
                </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Backup Inventory Management Database</h5>
                        <p class="card-text">
                            This will create a backup of the entire inventory_management database as an SQL file.
                            The backup file will be automatically downloaded to your computer.
                        </p>
                        <form method="post" action="">
                            <button type="submit" name="backup" class="btn btn-primary">
                                <i class="fas fa-download me-2"></i>Create Backup
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Important:</strong> Regular database backups are essential for data protection. 
                        It is recommended to create backups before making significant changes to the system.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>