<?php
require_once '../config/database.php';

// Get database connection
$conn = connectDB();

// Check if company_settings table exists
$result = $conn->query("SHOW TABLES LIKE 'company_settings'");
if ($result->num_rows == 0) {
    echo "Creating company_settings table...\n";
    
    // Create company_settings table
    $create_table = "CREATE TABLE company_settings (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL DEFAULT 'Musar Music Corporation',
        address TEXT,
        phone VARCHAR(20),
        email VARCHAR(100),
        website VARCHAR(100),
        tax_id VARCHAR(50),
        logo VARCHAR(255),
        footer_text TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    )";
    
    if ($conn->query($create_table)) {
        echo "Company settings table created successfully.\n";
        
        // Insert default company settings
        $insert_default = "INSERT INTO company_settings 
            (name, address, phone, email, website, tax_id) 
            VALUES 
            ('Musar Music Corporation', '123 Main Street, City', '************', 
             '<EMAIL>', 'www.musarmusic.com', '123-45-6789')";
        
        if ($conn->query($insert_default)) {
            echo "Default company settings inserted successfully.\n";
        } else {
            echo "Error inserting default company settings: " . $conn->error . "\n";
        }
    } else {
        echo "Error creating company_settings table: " . $conn->error . "\n";
    }
} else {
    echo "Company settings table already exists.\n";
}

// Close connection
closeDB($conn);

echo "Table check complete.\n";
?>