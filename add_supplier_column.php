<?php
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Add supplier_id column to products table if it doesn't exist
$check_column = "SHOW COLUMNS FROM products LIKE 'supplier_id'";
$result = $conn->query($check_column);

if ($result->num_rows == 0) {
    // Column doesn't exist, add it
    $add_column = "ALTER TABLE products ADD COLUMN supplier_id INT AFTER brand_id";
    if ($conn->query($add_column) === TRUE) {
        echo "<p>supplier_id column added successfully.</p>";
        
        // Add foreign key constraint
        $add_constraint = "ALTER TABLE products ADD CONSTRAINT fk_product_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL";
        if ($conn->query($add_constraint) === TRUE) {
            echo "<p>Foreign key constraint added successfully.</p>";
        } else {
            echo "<p>Error adding foreign key constraint: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Error adding supplier_id column: " . $conn->error . "</p>";
    }
} else {
    echo "<p>supplier_id column already exists.</p>";
}

// Close connection
closeDB($conn);

echo "<p><a href='sales.php'>Go back to Sales</a></p>";
?>