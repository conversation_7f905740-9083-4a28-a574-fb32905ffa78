<?php
// API endpoint to get the next invoice number
header('Content-Type: application/json');

require_once '../config/database.php';
require_once '../includes/functions.php';

// Get branch name from request
$branch_name = $_GET['branch'] ?? 'Main Branch';

// Get database connection
$conn = connectDB();

// Get the last invoice number from the database
$last_invoice_query = "SELECT invoice_number FROM orders 
                      WHERE branch_name = ? 
                      ORDER BY id DESC LIMIT 1";

$stmt = $conn->prepare($last_invoice_query);
if (!$stmt) {
    echo json_encode(['error' => 'Database error', 'invoice_number' => '00001']);
    exit;
}

$stmt->bind_param("s", $branch_name);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    // Extract the numeric portion and increment
    $last_invoice = $result->fetch_assoc()['invoice_number'];
    
    // If the invoice number is numeric, simply increment it
    if (is_numeric($last_invoice)) {
        $new_number = intval($last_invoice) + 1;
        $next_invoice = sprintf('%05d', $new_number); // Format with leading zeros
    }
    // If it has a specific format like "INV-00123", extract and increment the number part
    else if (preg_match('/^([A-Za-z\-]*)(\d+)$/', $last_invoice, $matches)) {
        $prefix = $matches[1];
        $number = intval($matches[2]);
        $new_number = $number + 1;
        $next_invoice = $prefix . sprintf('%0' . strlen($matches[2]) . 'd', $new_number);
    }
    // If we can't parse it, just return the last invoice number with "-1" appended
    else {
        $next_invoice = $last_invoice . "-1";
    }
} else {
    // No invoice yet, start with 00001
    $next_invoice = "00001";
}

// Close database connection
$conn->close();

// Return the next invoice number
echo json_encode(['invoice_number' => $next_invoice]);