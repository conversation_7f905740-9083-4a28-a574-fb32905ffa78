<?php
/**
 * Cart Controller
 * Handles all cart-related operations
 */

/**
 * Add product to cart
 * 
 * @param object $conn Database connection
 */
function addToCart($conn) {
    // Get product ID and quantity from POST
    $product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
    $branch_name = $_SESSION['branch_name'] ?? 'Main Branch';
    
    // Debug log
    error_log("Adding to cart - Product ID: $product_id, Quantity: $quantity, Branch: $branch_name");
    
    // Validate quantity
    if ($quantity <= 0) {
        $_SESSION['error_message'] = "Invalid quantity selected.";
        return;
    }
    
    // Get product details
    $query = "SELECT * FROM products WHERE id = ?";
    $stmt = $conn->prepare($query);
    
    if ($stmt === false) {
        $_SESSION['error_message'] = "Database error: " . $conn->error;
        return;
    }
    
    $stmt->bind_param("i", $product_id);
    
    if (!$stmt->execute()) {
        $_SESSION['error_message'] = "Database error: " . $stmt->error;
        $stmt->close();
        return;
    }
    
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $_SESSION['error_message'] = "Product not found.";
        $stmt->close();
        return;
    }
    
    $product = $result->fetch_assoc();
    $stmt->close();
    
    // Check if product belongs to current branch
    if ($product['branch_name'] !== $branch_name) {
        $_SESSION['error_message'] = "This product belongs to another branch and cannot be added to your cart.";
        return;
    }
    
    // Check if quantity is available
    if ($quantity > $product['quantity']) {
        $_SESSION['error_message'] = "Requested quantity exceeds available stock. Only {$product['quantity']} available.";
        return;
    }
    
    // Initialize cart if not exists
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    // Check if product is already in cart
    $found = false;
    
    // Debug log before cart update
    error_log("Current cart before update: " . json_encode($_SESSION['cart']));
    
    foreach ($_SESSION['cart'] as $key => $item) {
        if ($item['id'] == $product_id && (!isset($item['branch_name']) || $item['branch_name'] == $branch_name)) {
            // Update quantity
            $new_quantity = $item['quantity'] + $quantity;
            
            // Check if new quantity exceeds stock
            if ($new_quantity > $product['quantity']) {
                $_SESSION['error_message'] = "Total quantity exceeds available stock. Only {$product['quantity']} available.";
                return;
            }
            
            $_SESSION['cart'][$key]['quantity'] = $new_quantity;
            $_SESSION['cart'][$key]['total'] = $new_quantity * $item['price'];
            $found = true;
            error_log("Updated existing product in cart - Key: $key, New Quantity: $new_quantity");
            break;
        }
    }
    
    // If not found, add to cart
    if (!$found) {
        $new_item = [
            'id' => $product['id'],
            'name' => $product['name'],
            'price' => $product['unit_price'],
            'quantity' => $quantity,
            'total' => $product['unit_price'] * $quantity,
            'branch_name' => $product['branch_name']
        ];
        
        $_SESSION['cart'][] = $new_item;
        error_log("Added new product to cart: " . json_encode($new_item));
    }
    
    // Debug log after cart update
    error_log("Current cart after update: " . json_encode($_SESSION['cart']));
    
    $_SESSION['success_message'] = "{$product['name']} added to cart.";
}

/**
 * Remove product from cart
 */
function removeFromCart() {
    if (isset($_POST['remove_item']) && isset($_POST['item_index'])) {
        $index = (int)$_POST['item_index'];
        
        if (isset($_SESSION['cart'][$index])) {
            $product_name = $_SESSION['cart'][$index]['name'];
            
            // Remove the item
            array_splice($_SESSION['cart'], $index, 1);
            
            // Debug log
            error_log("Removed item from cart - Index: $index, Product: $product_name");
            
            $_SESSION['success_message'] = "{$product_name} removed from cart.";
        }
    }
}

/**
 * Update cart item quantity
 */
function updateCartQuantity($conn) {
    if (isset($_POST['update_quantity']) && isset($_POST['item_index']) && isset($_POST['quantity'])) {
        $index = (int)$_POST['item_index'];
        $quantity = (int)$_POST['quantity'];
        
        if ($quantity <= 0) {
            $_SESSION['error_message'] = "Invalid quantity selected.";
            return;
        }
        
        if (isset($_SESSION['cart'][$index])) {
            $product_id = $_SESSION['cart'][$index]['id'];
            
            // Get product details to check available quantity
            $query = "SELECT quantity FROM products WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $product_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $product = $result->fetch_assoc();
                
                if ($quantity > $product['quantity']) {
                    $_SESSION['error_message'] = "Requested quantity exceeds available stock. Only {$product['quantity']} available.";
                    $stmt->close();
                    return;
                }
                
                // Update quantity and total
                $_SESSION['cart'][$index]['quantity'] = $quantity;
                $_SESSION['cart'][$index]['total'] = $quantity * $_SESSION['cart'][$index]['price'];
                
                $_SESSION['success_message'] = "Cart updated successfully.";
            } else {
                $_SESSION['error_message'] = "Product not found.";
            }
            
            $stmt->close();
        }
    }
}

/**
 * Clear cart
 */
function clearCart() {
    if (isset($_POST['clear_cart'])) {
        $_SESSION['cart'] = [];
        $_SESSION['success_message'] = "Cart cleared successfully.";
        
        // Debug log
        error_log("Cart cleared");
    }
}

/**
 * Get cart items for the current branch
 * 
 * @param string $branch_name The branch name
 * @return array Cart data including items and total
 */
function getCurrentBranchCart($branch_name) {
    // Initialize return array
    $cart_data = [
        'items' => [],
        'total' => 0
    ];
    
    // Debug log
    error_log("Getting cart for branch: $branch_name");
    
    // Check if cart exists
    if (isset($_SESSION['cart'])) {
        // Filter cart items to only include products from the current branch
        $filtered_items = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
            return (!isset($item['branch_name']) || $item['branch_name'] == $branch_name);
        });
        
        // Reindex array to ensure sequential keys
        $cart_data['items'] = array_values($filtered_items);
        error_log("Found " . count($cart_data['items']) . " items in cart for branch");
    }
    
    // Calculate cart total
    foreach ($cart_data['items'] as $item) {
        $cart_data['total'] += $item['total'];
    }
    
    // Debug log cart contents
    foreach ($cart_data['items'] as $index => $item) {
        error_log("Cart item #{$index}: ID={$item['id']}, Name={$item['name']}, Quantity={$item['quantity']}");
    }
    
    return $cart_data;
}

function processCheckout($conn) {
    $branch_name = $_SESSION['branch_name'] ?? 'Main Branch';
    
    // Filter cart items to only include products from the current branch
    $current_branch_cart = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
        return (!isset($item['branch_name']) || $item['branch_name'] == $branch_name);
    });
    
    if (empty($current_branch_cart)) {
        $_SESSION['error_message'] = "Cart is empty. Please add products before checkout.";
        return;
    }
    
    // Get form data
    $customer_name = $_POST['customer_name'] ?? '';
    $customer_email = $_POST['customer_email'] ?? ''; // This is now Teacher Name
    $customer_phone = $_POST['customer_phone'] ?? ''; // This is now Lesson/Activity
    $invoice_number = $_POST['invoice_number'] ?? '';
    $payment_type = $_POST['payment_type'] ?? 'Cash';
    $cart_total = isset($_POST['cart_total']) ? (float)$_POST['cart_total'] : 0;
    
    // Validate required fields - only customer_name and invoice_number are required
    if (empty($customer_name) || empty($invoice_number)) {
        $_SESSION['error_message'] = "Please fill in all required fields.";
        return;
    }
    
    // Remove email validation - no longer needed since this is now Teacher Name
    // if (!empty($customer_email) && !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
    //     $_SESSION['error_message'] = "Please enter a valid email address.";
    //     return;
    // }
    
    // Check if invoice number already exists
    $check_query = "SELECT id FROM orders WHERE invoice_number = ?";
    $check_stmt = $conn->prepare($check_query);
    if ($check_stmt === false) {
        $_SESSION['error_message'] = "Database error: " . $conn->error;
        return;
    }
    
    $check_stmt->bind_param("s", $invoice_number);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        $_SESSION['error_message'] = "Invoice number already exists. Please use a different one.";
        $check_stmt->close();
        return;
    }
    $check_stmt->close();
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Create order
        $order_query = "INSERT INTO orders (
            customer_name, customer_email, customer_phone, 
            invoice_number, payment_type, total_amount, 
            branch_name, created_by, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $order_stmt = $conn->prepare($order_query);
        if ($order_stmt === false) {
            throw new Exception("Database error: " . $conn->error);
        }
        
        $user_id = $_SESSION['user_id'] ?? null;
        
        $order_stmt->bind_param(
            "sssssdsi",
            $customer_name, 
            $customer_email, 
            $customer_phone,
            $invoice_number, 
            $payment_type, 
            $cart_total,
            $branch_name, 
            $user_id
        );
        
        if (!$order_stmt->execute()) {
            throw new Exception("Error creating order: " . $order_stmt->error);
        }
        
        $order_id = $conn->insert_id;
        $order_stmt->close();
        
        // Insert order items and update inventory
        foreach ($current_branch_cart as $item) {
            // Insert order item
            $item_query = "INSERT INTO order_items (
                order_id, product_id, quantity, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?)";
            
            $item_stmt = $conn->prepare($item_query);
            if ($item_stmt === false) {
                throw new Exception("Database error: " . $conn->error);
            }
            
            $item_stmt->bind_param(
                "iiddd",
                $order_id, $item['id'], $item['quantity'], $item['price'], $item['total']
            );
            
            if (!$item_stmt->execute()) {
                throw new Exception("Error adding order item: " . $item_stmt->error);
            }
            
            $item_stmt->close();
            
            // Update inventory
            $update_query = "UPDATE products SET 
                quantity = quantity - ? 
                WHERE id = ?";
            
            $update_stmt = $conn->prepare($update_query);
            if ($update_stmt === false) {
                throw new Exception("Database error: " . $conn->error);
            }
            
            $update_stmt->bind_param("di", $item['quantity'], $item['id']);
            
            if (!$update_stmt->execute()) {
                throw new Exception("Error updating inventory: " . $update_stmt->error);
            }
            
            $update_stmt->close();
        }
        
        // Commit transaction
        $conn->commit();
        
        // Clear cart
        $_SESSION['cart'] = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
            return (isset($item['branch_name']) && $item['branch_name'] != $branch_name);
        });
        $_SESSION['cart'] = array_values($_SESSION['cart']); // Reindex
        
        // Set success message
        $_SESSION['checkout_success_message'] = "Sale completed successfully! Invoice #" . $invoice_number;
        
        // Return the order ID for receipt generation
        return $order_id;
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $_SESSION['error_message'] = "Error processing checkout: " . $e->getMessage();
        return false;
    }
}










