<?php
require_once 'config/database.php';
require_once 'includes/header.php'; // Includes session check and header HTML

// Check if user has permission (e.g., admin or specific role)
// Add role check if necessary:
// if (!isset($_SESSION['role']) || ($_SESSION['role'] != 'admin' && $_SESSION['role'] != 'manager')) {
//     $_SESSION['error_message'] = "You don't have permission to access this page.";
//     header("Location: index.php");
//     exit();
// }


$conn = connectDB();

// Fetch Suppliers for dropdown
$suppliers_query = "SELECT id, name FROM suppliers ORDER BY name ASC";
$suppliers_result = $conn->query($suppliers_query);
$suppliers = [];
if ($suppliers_result->num_rows > 0) {
    while ($row = $suppliers_result->fetch_assoc()) {
        $suppliers[] = $row;
    }
}

// Generate a default PO Number (e.g., based on timestamp or sequence)
$default_po_number = 'PO-' . date('YmdHis');

closeDB($conn);
?>

<div class="container-fluid py-4">
    <h1 class="h3 mb-4 text-gray-800">Create New Purchase Order (v2)</h1>

    <form action="process_create_po.php" method="POST" id="create-po-form">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Purchase Order Details</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="po_number" class="form-label">PO Number</label>
                        <input type="text" class="form-control" id="po_number" name="po_number" value="<?php echo htmlspecialchars($default_po_number); ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="supplier_id" class="form-label">Supplier</label>
                        <select class="form-select" id="supplier_id" name="supplier_id" required>
                            <option value="" selected disabled>Select Supplier</option>
                            <?php foreach ($suppliers as $supplier): ?>
                                <option value="<?php echo $supplier['id']; ?>"><?php echo htmlspecialchars($supplier['name']); ?></option>
                            <?php endforeach; ?>
                            <?php if (empty($suppliers)): ?>
                                <option value="" disabled>No suppliers found. Add suppliers first.</option>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="order_date" class="form-label">Order Date</label>
                        <input type="date" class="form-control" id="order_date" name="order_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                </div>
                 <div class="row mb-3">
                     <div class="col-md-4">
                        <label for="expected_delivery_date" class="form-label">Expected Delivery Date</label>
                        <input type="date" class="form-control" id="expected_delivery_date" name="expected_delivery_date">
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Pending" selected>Pending</option>
                            <option value="Ordered">Ordered</option>
                            <option value="Partial">Partial</option>
                            <option value="Received">Received</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>
                 </div>
                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
                <!-- Product Search Input -->
                 <div class="w-50">
                    <label for="product_search" class="form-label visually-hidden">Scan SKU or Search Products</label>
                    <input type="text" id="product_search" class="form-control" placeholder="Search products by name or SKU...">
                    <div id="product_suggestions" class="list-group position-absolute w-50" style="z-index: 1000;"></div>
                 </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="po-items-table" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>SKU</th>
                                <th>Quantity</th>
                                <th>Unit Cost</th>
                                <th>Total</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="po-items-body">
                            <!-- PO items will be added here dynamically -->
                             <tr id="no-items-row">
                                <td colspan="6" class="text-center">No items added yet. Search for products to add.</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-end"><strong>Subtotal:</strong></td>
                                <td id="po-subtotal" class="text-end">0.00</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end">
                                    <label for="shipping_cost" class="form-label mb-0 me-2">Shipping Cost:</label>
                                </td>
                                <td>
                                    <input type="number" step="0.01" class="form-control form-control-sm text-end" id="shipping_cost" name="shipping_cost" value="0.00">
                                </td>
                                <td></td>
                            </tr>
                             <tr>
                                <td colspan="4" class="text-end">
                                    <label for="discount" class="form-label mb-0 me-2">Discount:</label>
                                </td>
                                <td>
                                    <input type="number" step="0.01" class="form-control form-control-sm text-end" id="discount" name="discount" value="0.00">
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>Grand Total:</strong></td>
                                <td id="po-grandtotal" class="text-end fw-bold">0.00</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="text-end mb-4">
             <a href="purchase_order.php" class="btn btn-secondary me-2">Cancel</a>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-save me-2"></i> Save Purchase Order
            </button>
        </div>
    </form>
</div>

<?php require_once 'includes/footer.php'; ?>

<!-- Add JavaScript for product search, adding items, and calculations -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSearchInput = document.getElementById('product_search');
    const productSuggestionsDiv = document.getElementById('product_suggestions');
    const poItemsTableBody = document.getElementById('po-items-body');
    const noItemsRow = document.getElementById('no-items-row');
    const subtotalCell = document.getElementById('po-subtotal');
    const grandtotalCell = document.getElementById('po-grandtotal');
    const shippingCostInput = document.getElementById('shipping_cost');
    const discountInput = document.getElementById('discount');
    const supplierSelect = document.getElementById('supplier_id');

    let poItems = {}; // Store added product data { product_id: { name, sku, qty, cost } }

    // --- Product Search ---
    productSearchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim();
        const supplierId = supplierSelect.value; // Get selected supplier ID
        productSuggestionsDiv.innerHTML = ''; // Clear previous suggestions

        if (searchTerm.length < 2) {
            productSuggestionsDiv.style.display = 'none';
            return;
        }

        // Fetch products via AJAX using the search_products.php script
        // Pass both term and supplier_id
        fetch(`search_products.php?term=${encodeURIComponent(searchTerm)}&supplier_id=${supplierId}`)
            .then(response => response.json())
            .then(products => {
                if (products.length > 0) {
                    products.forEach(product => {
                        const div = document.createElement('a');
                        div.href = '#';
                        div.classList.add('list-group-item', 'list-group-item-action');
                        // Display name, SKU, and maybe current stock or price
                        div.innerHTML = `${product.name} <small class="text-muted">(${product.sku}) - Price: ${parseFloat(product.unit_price).toFixed(2)}</small>`;
                        div.dataset.productId = product.id;
                        div.dataset.productName = product.name;
                        div.dataset.productSku = product.sku;
                        div.dataset.productCost = product.unit_price; // Use unit_price as default cost

                        div.addEventListener('click', function(e) {
                            e.preventDefault();
                            addProductToPO(product.id, product.name, product.sku, product.unit_price);
                            productSearchInput.value = ''; // Clear search input
                            productSuggestionsDiv.style.display = 'none'; // Hide suggestions
                        });
                        productSuggestionsDiv.appendChild(div);
                    });
                    productSuggestionsDiv.style.display = 'block';
                } else {
                    productSuggestionsDiv.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error fetching products:', error);
                productSuggestionsDiv.style.display = 'none';
            });
    });

     // Hide suggestions when clicking outside
    document.addEventListener('click', function(event) {
        if (!productSearchInput.contains(event.target) && !productSuggestionsDiv.contains(event.target)) {
            productSuggestionsDiv.style.display = 'none';
        }
    });


    // --- Add Product to PO Table ---
    function addProductToPO(id, name, sku, cost) {
        if (poItems[id]) {
            // If product already exists, maybe just focus its quantity input or alert user
            alert('Product already added. You can adjust the quantity.');
            const existingQtyInput = document.getElementById(`qty_${id}`);
            if(existingQtyInput) existingQtyInput.focus();
            return;
        }

         // Hide the "No items" row if it exists
        if (noItemsRow) {
            noItemsRow.style.display = 'none';
        }


        const productId = id;
        const productName = name;
        const productSku = sku; // Get the SKU
        const unitCost = parseFloat(cost) || 0.00;
        const quantity = 1; // Default quantity

        poItems[productId] = { name: productName, sku: productSku, qty: quantity, cost: unitCost };

        const newRow = document.createElement('tr');
        newRow.id = `row_${productId}`;
        newRow.innerHTML = `
            <td>
                ${productName}
                <input type="hidden" name="products[${productId}][id]" value="${productId}">
                <input type="hidden" name="products[${productId}][name]" value="${productName}">
                <input type="hidden" name="products[${productId}][sku]" value="${productSku}"> <!-- Add this hidden input for SKU -->
            </td>
            <td>${productSku}</td>
            <td>
                <input type="number" class="form-control form-control-sm quantity-input" id="qty_${productId}" name="products[${productId}][quantity]" value="${quantity}" min="1" step="1" data-product-id="${productId}" style="width: 80px;">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm cost-input" name="products[${productId}][cost]" value="${unitCost.toFixed(2)}" min="0" step="0.01" data-product-id="${productId}" style="width: 100px;">
            </td>
            <td class="text-end item-total" id="total_${productId}">${(quantity * unitCost).toFixed(2)}</td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-item-btn" data-product-id="${productId}">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;
        poItemsTableBody.appendChild(newRow);
        updateTotals();
    }

    // --- Update Item Total, Subtotal, Grand Total ---
    function updateTotals() {
        let subtotal = 0;
        poItemsTableBody.querySelectorAll('tr').forEach(row => {
            const productId = row.id.split('_')[1];
            if (!productId) return; // Skip if it's not a product row

            const qtyInput = row.querySelector('.quantity-input');
            const costInput = row.querySelector('.cost-input');
            const itemTotalCell = row.querySelector('.item-total');

            const qty = parseInt(qtyInput.value) || 0;
            const cost = parseFloat(costInput.value) || 0;
            const itemTotal = qty * cost;

            itemTotalCell.textContent = itemTotal.toFixed(2);
            subtotal += itemTotal;

            // Update the main poItems object
            if (poItems[productId]) {
                 poItems[productId].qty = qty;
                 poItems[productId].cost = cost;
            }
        });

        subtotalCell.textContent = subtotal.toFixed(2);

        const shipping = parseFloat(shippingCostInput.value) || 0;
        const discount = parseFloat(discountInput.value) || 0;
        const grandTotal = subtotal + shipping - discount;

        grandtotalCell.textContent = grandTotal.toFixed(2);
    }

    // --- Event Listeners for Quantity/Cost Changes and Removal ---
    poItemsTableBody.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input') || e.target.classList.contains('cost-input')) {
            updateTotals();
        }
    });

     poItemsTableBody.addEventListener('click', function(e) {
        // Use closest to handle clicks on the icon inside the button
        const removeButton = e.target.closest('.remove-item-btn');
        if (removeButton) {
            const productId = removeButton.dataset.productId;
            const rowToRemove = document.getElementById(`row_${productId}`);
            if (rowToRemove) {
                rowToRemove.remove();
                delete poItems[productId]; // Remove from our tracking object
                 // Show "No items" row if table is empty
                if (poItemsTableBody.children.length === 0 && noItemsRow) {
                    noItemsRow.style.display = 'table-row';
                }
                updateTotals();
            }
        }
    });

    // Update grand total when shipping or discount changes
    shippingCostInput.addEventListener('input', updateTotals);
    discountInput.addEventListener('input', updateTotals);

    // Initial calculation in case of default values
    updateTotals();

     // --- Form Submission Validation ---
    const form = document.getElementById('create-po-form');
    form.addEventListener('submit', function(e) {
        // Check if any items have been added
        if (Object.keys(poItems).length === 0) {
            e.preventDefault(); // Prevent form submission
            alert('Please add at least one product to the purchase order.');
            productSearchInput.focus();
        }
        // Add other validations if needed (e.g., supplier selected)
        if (!supplierSelect.value) {
             e.preventDefault();
             alert('Please select a supplier.');
             supplierSelect.focus();
        }
    });

});
</script>

