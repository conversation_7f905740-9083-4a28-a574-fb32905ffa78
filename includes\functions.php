<?php
/**
 * Common functions for the POS system
 */

// Format currency
function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

// Generate a unique invoice number
function generateInvoiceNumber() {
    $prefix = 'INV';
    $date = date('Ymd');
    $random = mt_rand(1000, 9999);
    return $prefix . '-' . $date . '-' . $random;
}

// Get user display name
function getUserDisplayName() {
    if (isset($_SESSION['user_firstname']) && isset($_SESSION['user_lastname'])) {
        return $_SESSION['user_firstname'] . ' ' . $_SESSION['user_lastname'];
    } elseif (isset($_SESSION['username'])) {
        return $_SESSION['username'];
    } else {
        return 'User';
    }
}

// Check if user has specific permission
function hasPermission($permission) {
    if (!isset($_SESSION['user_permissions'])) {
        return false;
    }
    
    return in_array($permission, $_SESSION['user_permissions']);
}

// Log activity
function logActivity($conn, $action, $description = '', $user_id = null) {
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    if ($user_id) {
        $query = "INSERT INTO activity_logs (user_id, action, description, ip_address, created_at)
                  VALUES (?, ?, ?, ?, NOW())";
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
        
        $stmt = $conn->prepare($query);
        
        // Check if prepare was successful
        if ($stmt === false) {
            // Handle the error - could log to error file or return false
            error_log("Database prepare error in logActivity: " . $conn->error);
            return false;
        }
        
        $stmt->bind_param("isss", $user_id, $action, $description, $ip_address);
        
        // Check if execution was successful
        $result = $stmt->execute();
        if ($result === false) {
            error_log("Database execute error in logActivity: " . $stmt->error);
        }
        
        $stmt->close();
        return $result;
    }
    
    return false;
}

// Get alert HTML
function getAlert($type, $message) {
    $icon = '';
    switch ($type) {
        case 'success':
            $icon = '<i class="fas fa-check-circle me-2"></i>';
            break;
        case 'danger':
            $icon = '<i class="fas fa-exclamation-circle me-2"></i>';
            break;
        case 'warning':
            $icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
            break;
        case 'info':
            $icon = '<i class="fas fa-info-circle me-2"></i>';
            break;
    }
    
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . $icon . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

// ... existing code ...
