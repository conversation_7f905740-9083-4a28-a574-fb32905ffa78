<?php
require_once '../config/database.php';

// Get database connection
$conn = connectDB();

// Check if barcode column exists
$check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'barcode'");

if ($check_column->num_rows == 0) {
    // Add barcode column if it doesn't exist
    $alter_query = "ALTER TABLE products ADD COLUMN barcode VARCHAR(100) DEFAULT NULL AFTER sku";
    
    if ($conn->query($alter_query)) {
        echo "Successfully added barcode column to products table.<br>";
    } else {
        echo "Error adding barcode column: " . $conn->error . "<br>";
    }
} else {
    echo "Barcode column already exists in products table.<br>";
}

// Check if last_scanned column exists
$check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'last_scanned'");

if ($check_column->num_rows == 0) {
    // Add last_scanned column if it doesn't exist
    $alter_query = "ALTER TABLE products ADD COLUMN last_scanned TIMESTAMP NULL DEFAULT NULL";
    
    if ($conn->query($alter_query)) {
        echo "Successfully added last_scanned column to products table.<br>";
    } else {
        echo "Error adding last_scanned column: " . $conn->error . "<br>";
    }
} else {
    echo "Last_scanned column already exists in products table.<br>";
}

// Close connection
$conn->close();

echo "<p>Database update completed.</p>";
echo "<p><a href='../products.php'>Return to Products</a></p>";
?>