<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo '<div class="alert alert-danger">You must be logged in to view this report</div>';
    exit;
}

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$date_from = date('Y-m-d', strtotime('-7 days')); // Default to last 7 days
$date_to = date('Y-m-d');   // Default to today
$payment_filter = '';
$status_filter = '';
$customer_filter = '';

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_POST['search'])) {
    $search = trim($_POST['search']);
}

if (isset($_POST['date_from']) && !empty($_POST['date_from'])) {
    $date_from = $_POST['date_from'];
}

if (isset($_POST['date_to']) && !empty($_POST['date_to'])) {
    $date_to = $_POST['date_to'];
}

if (isset($_POST['payment']) && !empty($_POST['payment'])) {
    $payment_filter = $_POST['payment'];
}

if (isset($_POST['status']) && !empty($_POST['status'])) {
    $status_filter = $_POST['status'];
}

if (isset($_POST['customer']) && !empty($_POST['customer'])) {
    $customer_filter = $_POST['customer'];
}

// Build base query for fetching orders
$query = "SELECT o.*, u.username 
          FROM orders o 
          LEFT JOIN users u ON o.created_by = u.id 
          WHERE o.branch_name = ?";

// Prepare bind parameters array
$bind_types = "s"; // Start with branch_name
$bind_values = array($branch_name);

// Add date range condition
$query .= " AND DATE(o.created_at) BETWEEN ? AND ?";
$bind_types .= "ss"; // Add two string parameters
$bind_values[] = $date_from;
$bind_values[] = $date_to;

// Add search condition if provided
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (o.order_number LIKE ? OR o.invoice_number LIKE ? OR 
                o.customer_name LIKE ? OR o.customer_phone LIKE ? OR 
                o.customer_email LIKE ?)";
    $bind_types .= "sssss"; // Add five string parameters
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
}

// Add payment filter if provided
if (!empty($payment_filter)) {
    $query .= " AND o.payment_type = ?";
    $bind_types .= "s"; // Add one string parameter
    $bind_values[] = $payment_filter;
}

// Add status filter if provided
if (!empty($status_filter)) {
    $query .= " AND o.status = ?";
    $bind_types .= "s"; // Add one string parameter
    $bind_values[] = $status_filter;
}

// Add customer filter if provided
if (!empty($customer_filter)) {
    $customer_term = "%{$customer_filter}%";
    $query .= " AND o.customer_name LIKE ?";
    $bind_types .= "s"; // Add one string parameter
    $bind_values[] = $customer_term;
}

// Order by created_at descending
$query .= " ORDER BY o.created_at DESC";

// Prepare and execute the statement
$stmt = $conn->prepare($query);
$stmt->bind_param($bind_types, ...$bind_values);
$stmt->execute();
$result = $stmt->get_result();
$orders = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Calculate totals
$total_sales = 0;
$total_orders = count($orders);
$payment_totals = [];
$status_totals = [];

foreach ($orders as $order) {
    $total_sales += $order['total_amount'];
    
    // Count by payment type
    if (!isset($payment_totals[$order['payment_type']])) {
        $payment_totals[$order['payment_type']] = [
            'count' => 0,
            'amount' => 0
        ];
    }
    $payment_totals[$order['payment_type']]['count']++;
    $payment_totals[$order['payment_type']]['amount'] += $order['total_amount'];
    
    // Count by status
    if (!isset($status_totals[$order['status']])) {
        $status_totals[$order['status']] = [
            'count' => 0,
            'amount' => 0
        ];
    }
    $status_totals[$order['status']]['count']++;
    $status_totals[$order['status']]['amount'] += $order['total_amount'];
}

// Get company information
$company_query = "SELECT * FROM company_settings LIMIT 1";
$company_result = $conn->query($company_query);
$company = $company_result->fetch_assoc();

// Close database connection
closeDB($conn);
?>

<div class="container-fluid">
    <!-- Report Header -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2><?php echo htmlspecialchars($company['name'] ?? 'Company Name'); ?></h2>
            <p class="mb-0"><?php echo htmlspecialchars($company['address'] ?? 'Company Address'); ?></p>
            <p class="mb-0">Phone: <?php echo htmlspecialchars($company['phone'] ?? 'N/A'); ?> | Email: <?php echo htmlspecialchars($company['email'] ?? 'N/A'); ?></p>
            <h4 class="mt-3">Sales Report</h4>
            <p>
                Branch: <strong><?php echo htmlspecialchars($branch_name); ?></strong> | 
                Period: <strong><?php echo date('M d, Y', strtotime($date_from)); ?></strong> to 
                <strong><?php echo date('M d, Y', strtotime($date_to)); ?></strong>
            </p>
            <?php if (!empty($search) || !empty($payment_filter) || !empty($status_filter) || !empty($customer_filter)): ?>
                <p class="small text-muted">
                    Filters: 
                    <?php if (!empty($search)): ?>Search: "<?php echo htmlspecialchars($search); ?>"<?php endif; ?>
                    <?php if (!empty($payment_filter)): ?> | Payment: <?php echo htmlspecialchars($payment_filter); ?><?php endif; ?>
                    <?php if (!empty($status_filter)): ?> | Status: <?php echo htmlspecialchars($status_filter); ?><?php endif; ?>
                    <?php if (!empty($customer_filter)): ?> | Customer: <?php echo htmlspecialchars($customer_filter); ?><?php endif; ?>
                </p>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Summary Section -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Sales Summary</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>Total Orders:</th>
                            <td><?php echo $total_orders; ?></td>
                        </tr>
                        <tr>
                            <th>Total Sales:</th>
                            <td>₱<?php echo number_format($total_sales, 2); ?></td>
                        </tr>
                        <tr>
                            <th>Average Order Value:</th>
                            <td>₱<?php echo $total_orders > 0 ? number_format($total_sales / $total_orders, 2) : '0.00'; ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Payment Methods</h5>
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Orders</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payment_totals as $payment_type => $data): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($payment_type); ?></td>
                                    <td><?php echo $data['count']; ?></td>
                                    <td>₱<?php echo number_format($data['amount'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Order Status</h5>
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Orders</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($status_totals as $status => $data): ?>
                                <tr>
                                    <td>
                                        <?php 
                                        $status_display = ucfirst(str_replace('_', ' ', $status));
                                        echo htmlspecialchars($status_display); 
                                        ?>
                                    </td>
                                    <td><?php echo $data['count']; ?></td>
                                    <td>₱<?php echo number_format($data['amount'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Orders Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Order Details</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Invoice #</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Payment</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($orders) > 0): ?>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                            <td><?php echo htmlspecialchars($order['invoice_number'] ?? 'N/A'); ?></td>
                                            <td><?php echo date('M d, Y h:i A', strtotime($order['created_at'])); ?></td>
                                            <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                            <td><?php echo htmlspecialchars($order['payment_type']); ?></td>
                                            <td>
                                                <?php 
                                                $status_display = ucfirst(str_replace('_', ' ', $order['status']));
                                                echo htmlspecialchars($status_display); 
                                                ?>
                                            </td>
                                            <td class="text-end">₱<?php echo number_format($order['total_amount'], 2); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No orders found for the selected criteria</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="6" class="text-end">Total:</th>
                                    <th class="text-end">₱<?php echo number_format($total_sales, 2); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Report Footer -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <p class="small text-muted">
                Report generated on <?php echo date('F d, Y h:i A'); ?> by <?php echo htmlspecialchars($_SESSION['username'] ?? 'System'); ?>
            </p>
        </div>
    </div>
echo json_encode($response);
