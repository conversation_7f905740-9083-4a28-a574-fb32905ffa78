<?php
include 'includes/header.php';
include 'config/database.php';

// Connect to database
$conn = connectDB();
$branch_name = $_SESSION['branch_name'] ?? '';

// Initialize filters
$search = $_GET['search'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$payment_filter = $_GET['payment'] ?? '';

// Build base query for fetching tuition payments
$query = "SELECT o.id, o.order_number, o.invoice_number, o.customer_name, o.customer_email, 
          o.customer_phone, o.payment_type, o.total_amount, o.created_at, o.status, u.username as created_by
          FROM orders o 
          LEFT JOIN users u ON o.created_by = u.id
          LEFT JOIN order_items oi ON o.id = oi.order_id
          LEFT JOIN products p ON oi.product_id = p.id
          WHERE o.branch_name = ? AND p.name LIKE '%tuition%'";

// Add search condition if provided
if (!empty($search)) {
    $query .= " AND (o.customer_name LIKE ? OR o.customer_email LIKE ? OR o.customer_phone LIKE ?)";
}

// Add date range conditions if provided
if (!empty($date_from)) {
    $query .= " AND DATE(o.created_at) >= ?";
}

if (!empty($date_to)) {
    $query .= " AND DATE(o.created_at) <= ?";
}

// Add payment type filter if provided
if (!empty($payment_filter)) {
    $query .= " AND o.payment_type = ?";
}

// Group by order to avoid duplicates
$query .= " GROUP BY o.id";

// Order by created_at descending
$query .= " ORDER BY o.created_at DESC";

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameters
$bind_types = "s"; // branch_name
$bind_values = [$branch_name];

if (!empty($search)) {
    $search_term = "%{$search}%";
    $bind_types .= "sss"; // customer_name, customer_email, customer_phone
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
    $bind_values[] = $search_term;
}

if (!empty($date_from)) {
    $bind_types .= "s"; // date_from
    $bind_values[] = $date_from;
}

if (!empty($date_to)) {
    $bind_types .= "s"; // date_to
    $bind_values[] = $date_to;
}

if (!empty($payment_filter)) {
    $bind_types .= "s"; // payment_type
    $bind_values[] = $payment_filter;
}

// Execute statement
$stmt->bind_param($bind_types, ...$bind_values);
$stmt->execute();
$result = $stmt->get_result();
$ledger_entries = $result->fetch_all(MYSQLI_ASSOC);

// Get all payment types for filter dropdown
$payment_types_query = "SELECT DISTINCT payment_type FROM orders WHERE branch_name = ? ORDER BY payment_type ASC";
$payment_stmt = $conn->prepare($payment_types_query);
$payment_stmt->bind_param("s", $branch_name);
$payment_stmt->execute();
$payment_types_result = $payment_stmt->get_result();
$payment_stmt->close();
?>

<div class="container-fluid">
    <h1 class="mt-4 mb-4">Customer/Student Ledger</h1>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filters
        </div>
        <div class="card-body">
            <form method="GET" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Customer name, email, phone" value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-2">
                    <label for="payment" class="form-label">Payment Type</label>
                    <select class="form-select" id="payment" name="payment">
                        <option value="">All Payment Types</option>
                        <?php while ($payment_type = $payment_types_result->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($payment_type['payment_type']); ?>" 
                                <?php echo ($payment_filter == $payment_type['payment_type']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($payment_type['payment_type']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">Apply Filters</button>
                    <a href="customer_ledger.php" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Ledger Table -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-table me-1"></i>
                    Tuition Payment Ledger
                </div>
                <div>
                    <a href="export_tuition_ledger.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : ''; ?>" 
                       class="btn btn-sm btn-success me-2">
                        <i class="fas fa-file-excel me-1"></i> Export to Excel
                    </a>
                    <button id="printLedgerBtn" class="btn btn-sm btn-primary">
                        <i class="fas fa-print me-1"></i> Print Ledger
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Invoice #</th>
                            <th>Date</th>
                            <th>Student Name</th>
                            <th>Teacher/Mentor</th>
                            <th>Lesson/Activity</th>
                            <th>Payment Type</th>
                            <th>Amount</th>
                            <th>Created By</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($ledger_entries) > 0): ?>
                            <?php foreach ($ledger_entries as $entry): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($entry['invoice_number'] ?? $entry['order_number']); ?></td>
                                    <td><?php echo date('M d, Y h:i A', strtotime($entry['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($entry['customer_name']); ?></td>
                                    <td>
                                        <?php if (!empty($entry['customer_email'])): ?>
                                            <div><i class="fas fa-envelope me-1"></i> <?php echo htmlspecialchars($entry['customer_email']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($entry['customer_phone'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($entry['payment_type']); ?></td>
                                    <td class="text-end">₱<?php echo number_format($entry['total_amount'], 2); ?></td>
                                    <td><?php echo htmlspecialchars($entry['created_by'] ?? 'N/A'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo ($entry['status'] == 'completed') ? 'success' : 
                                                (($entry['status'] == 'pending') ? 'warning' : 
                                                (($entry['status'] == 'cancelled') ? 'danger' : 'secondary')); 
                                        ?>">
                                            <?php echo ucfirst(htmlspecialchars($entry['status'])); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">No tuition payment records found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Close database connection
closeDB($conn);
include 'includes/footer.php';
?>

<style>
    @media print {
        body * {
            visibility: hidden;
        }
        
        .container-fluid, .card-body, .table-responsive, .table, 
        .container-fluid *, .card-body *, .table-responsive *, .table * {
            visibility: visible;
        }
        
        .container-fluid {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 0.5cm;
            overflow: visible;
        }
        
        .card-header, .btn, form, .no-print {
            display: none !important;
        }
        
        .card {
            border: none;
            box-shadow: none;
        }
        
        /* Remove scrollbars */
        .table-responsive {
            overflow: visible !important;
        }
        
        /* Ensure table fits on page */
        .table {
            width: 100% !important;
            font-size: 10pt !important;
            table-layout: fixed;
        }
        
        /* Adjust column widths for better fit */
        .table th, .table td {
            padding: 4px !important;
            word-wrap: break-word;
        }
        
        /* Set page margins */
        @page {
            margin: 1cm;
            size: portrait;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Print ledger button click event
        const printLedgerBtn = document.getElementById('printLedgerBtn');
        if (printLedgerBtn) {
            printLedgerBtn.addEventListener('click', function() {
                window.print();
            });
        }
    });
</script>



