<?php
// Start the session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Get the branch name from the URL parameter
if (isset($_GET['branch']) && !empty($_GET['branch'])) {
    // Store the branch name in the session
    $_SESSION['branch_name'] = $_GET['branch'];
    
    // Set a success message
    $_SESSION['success_message'] = "Store changed to " . htmlspecialchars($_GET['branch']);
} else {
    // If no branch is specified, set an error message
    $_SESSION['error_message'] = "No store was selected.";
}

// Determine where to redirect the user
$redirect = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';

// Redirect back to the previous page or to the index page
header("Location: $redirect");
exit();