<?php
// Only start session if one doesn't already exist
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Restrict staff users to only access sales.php
if (isset($_SESSION['role']) && $_SESSION['role'] == 'staff' && $current_page != 'sales.php') {
    header("Location: sales.php");
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Management System</title>
    <link rel="icon" href="franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/print-styles.css">
    <link rel="stylesheet" href="assets/css/product-list.css">
    <style>
        /* Increase dropdown width for products menu */
        #productsDropdown + .dropdown-menu {
            min-width: 250px;
            width: auto;
        }
        
        /* Ensure dropdown items have enough padding */
        #productsDropdown + .dropdown-menu .dropdown-item {
            padding: 0.5rem 1.5rem;
            white-space: normal;
        }
    </style>
</head>
<body> <?php // Or wherever your main body starts ?>
    <!-- Add this block for displaying session messages -->
    <div class="container pt-3"> <?php // Adjust container/styling as needed for your layout ?>
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['success_message']); // Clear message after displaying ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['error_message']); // Clear message after displaying ?>
        <?php endif; ?>
    </div>
    <!-- End message display block -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <div class="d-flex justify-content-center align-items-center">
                            <img src="logo.png" alt="InvenTech Logo" style="height: 72px; margin-right: 10px;">
                            <img src="FranzSys.png" alt="FranzSys Logo" style="height: 72px;">
                        </div>
                        <h5 class="text-white mt-2"><?php if(isset($_SESSION['branch_name'])) { echo $_SESSION['branch_name']; } else { echo 'Select Store'; }?></h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo ($current_page == 'products.php' || $current_page == 'categories.php' || $current_page == 'brands.php') ? 'active' : ''; ?>" href="#" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-box me-2"></i> Products
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="productsDropdown">
                                <li><a class="dropdown-item <?php echo ($current_page == 'products.php') ? 'active' : ''; ?>" href="products.php"><i class="fas fa-box me-2"></i> All Products</a></li>
                                <li><a class="dropdown-item <?php echo ($current_page == 'categories.php') ? 'active' : ''; ?>" href="categories.php"><i class="fas fa-tags me-2"></i> Categories</a></li>
                                <li><a class="dropdown-item <?php echo ($current_page == 'brands.php') ? 'active' : ''; ?>" href="brands.php"><i class="fas fa-copyright me-2"></i> Brands</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'sales.php') ? 'active' : ''; ?>" href="sales.php">
                                <i class="fas fa-cash-register me-2"></i> POS / Sales
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'purchase_order.php') ? 'active' : ''; ?>" href="purchase_order.php">
                                <i class="fas fa-shopping-cart me-2"></i> Purchase Orders
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'suppliers.php') ? 'active' : ''; ?>" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i> Suppliers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'expenses.php') ? 'active' : ''; ?>" href="expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i> Expenses
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'transfers.php') ? 'active' : ''; ?>" href="transfers.php">
                                <i class="fas fa-money-bill-wave me-2"></i> Transfer Stocks
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'online_store.php') ? 'active' : ''; ?>" href="online_store.php" target="_blank">
                                <i class="fas fa-globe me-2"></i> Open Online Store
                            </a>
                        </li>
                        
                        <?php if ($_SESSION['role'] == 'admin'): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'users.php') ? 'active' : ''; ?>" href="users.php">
                                <i class="fas fa-users me-2"></i> Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'customer_ledger.php') ? 'active' : ''; ?>" href="customer_ledger.php">
                                <i class="fas fa-book me-2"></i> Customer Ledger
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-tools me-2"></i> Tools
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="toolsDropdown">
                                <li><a class="dropdown-item" href="backup_database.php"><i class="fas fa-database me-2"></i> Backup Database</a></li>
                                <li><a class="dropdown-item" href="import_form.php"><i class="fas fa-file-import me-2"></i> Database Import</a></li>
                                <li><a class="dropdown-item" href="database_tools.php"><i class="fas fa-file-import me-2"></i> Database Tools</a></li>
                            </ul>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i> Reports
                            </a>
                        </li>
                    </ul>
                    <hr class="text-white"> <!-- this is where the user profile image is -->
                    <div class="dropdown pb-4 px-3">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php 
                            // Get profile image from session or use default
                            $profile_image = isset($_SESSION['profile_image']) && !empty($_SESSION['profile_image']) ? $_SESSION['profile_image'] : 'default.png';
                            
                            // Construct the full path to the image
                            $image_path = 'uploads/users/' . $profile_image;
                            
                            // Check if the image file exists, if not use default
                            if (!file_exists($image_path)) {
                                $profile_image = 'default.png';
                                $image_path = 'uploads/users/' . $profile_image;
                            }
                            ?>
                            <img src="<?php echo $image_path; ?>" alt="Profile" class="rounded-circle me-2" width="32" height="32">
                            <span><?php echo isset($_SESSION['full_name']) ? htmlspecialchars($_SESSION['full_name']) : 'User'; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php">Sign out</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-shop me-1"></i> <?php if(isset($_SESSION['branch_name'])) { echo htmlspecialchars($_SESSION['branch_name']);} else { echo "Select Store"; } ?>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="select-store.php?branch=Musar Brower">Musar Brower</a></li>
                                <li><a class="dropdown-item" href="select-store.php?branch=Musar Maharlika">Musar Maharlika</a></li>
                                <li><a class="dropdown-item" href="select-store.php?branch=Musar Parking">Musar Parking</a></li>
                                <li><a class="dropdown-item" href="select-store.php?branch=Musar SM Sn Fdo">Musar SM Sn Fdo</a></li>
                                <li><a class="dropdown-item" href="select-store.php?branch=Musar SM Sn Fdo">Musar SM</a></li>
                                <li><a class="dropdown-item" href="select-store.php?branch=Online Store">Online Store</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
        <!-- The actual page content will be included/rendered after this point -->
        <!-- Navbar -->
        <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" data-scroll="true">
           <!-- ... existing navbar ... -->
        </nav>
        <!-- End Navbar -->

        <!-- The actual page content will be included/rendered after this point -->
