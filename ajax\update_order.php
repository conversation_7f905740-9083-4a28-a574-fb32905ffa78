<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'You must be logged in to perform this action']);
    exit;
}

require_once '../config/database.php';
require_once '../includes/functions.php';

// Get database connection
$conn = connectDB();

// Check if action is provided
if (!isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'No action specified']);
    exit;
}

// Get action
$action = $_POST['action'];

// Handle update status action
if ($action === 'update_status') {
    // Check if order_id and status are provided
    if (!isset($_POST['order_id']) || !isset($_POST['status'])) {
        echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
        exit;
    }
    
    $order_id = (int)$_POST['order_id'];
    $status = $_POST['status'];
    
    // Validate status
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled', 'in_transit', 'received', 'returned'];
    if (!in_array($status, $valid_statuses)) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit;
    }
    
    // Update order status
    $stmt = $conn->prepare("UPDATE orders SET status = ? WHERE id = ?");
    $stmt->bind_param("si", $status, $order_id);
    
    if ($stmt->execute()) {
        // Log the activity
        logActivity($conn, 'order_status_update', 'Updated order #' . $order_id . ' status to ' . $status);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Order status updated successfully',
            'order_id' => $order_id,
            'status' => $status
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update order status: ' . $conn->error]);
    }
    
    $stmt->close();
}
// Handle delete order action
else if ($action === 'delete_order') {
    // Check if order_id is provided
    if (!isset($_POST['order_id'])) {
        echo json_encode(['success' => false, 'message' => 'Missing order ID']);
        exit;
    }
    
    $order_id = (int)$_POST['order_id'];
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Get order details for logging
        $stmt = $conn->prepare("SELECT order_number FROM orders WHERE id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $order = $result->fetch_assoc();
        $stmt->close();
        
        if (!$order) {
            throw new Exception("Order not found");
        }
        
        // Delete order items first
        $stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        $stmt->close();
        
        // Delete the order
        $stmt = $conn->prepare("DELETE FROM orders WHERE id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        $stmt->close();
        
        // Commit transaction
        $conn->commit();
        
        // Log the activity
        logActivity($conn, 'order_delete', 'Deleted order #' . $order['order_number']);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Order deleted successfully',
            'order_id' => $order_id
        ]);
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'Failed to delete order: ' . $e->getMessage()]);
    }
}
else {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

// Close connection
closeDB($conn);
?>