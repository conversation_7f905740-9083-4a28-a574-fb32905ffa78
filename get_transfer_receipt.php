<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Unauthorized access");
}

// Get database connection
$conn = connectDB();

// Get transfer ID from URL
$transfer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$transfer_id) {
    die("Transfer ID not provided");
}

try {
    // Fetch transfer details
    $query = "SELECT t.*, 
              u.username as created_by_name
              FROM transfers t
              LEFT JOIN users u ON t.created_by = u.id
              WHERE t.id = ?";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing transfer query: " . $conn->error);
    }

    $stmt->bind_param("i", $transfer_id);
    if (!$stmt->execute()) {
        throw new Exception("Error executing transfer query: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $transfer = $result->fetch_assoc();

    if (!$transfer) {
        throw new Exception("Transfer not found");
    }

    // Fetch transfer items
    $items_query = "SELECT ti.*, p.name as product_name, p.sku
                   FROM transfer_items ti
                   LEFT JOIN products p ON ti.product_id = p.id
                   WHERE ti.transfer_id = ?";

    $stmt = $conn->prepare($items_query);
    if (!$stmt) {
        throw new Exception("Error preparing items query: " . $conn->error);
    }

    $stmt->bind_param("i", $transfer_id);
    if (!$stmt->execute()) {
        throw new Exception("Error executing items query: " . $stmt->error);
    }

    $items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

    // Generate receipt HTML
    ?>
    <style>
        @page {
            size: 5.5in 11in;
            margin: 0.25in;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #fff;
            margin: 0;
            padding: 0;
            width: 5.5in;
            height: 11in;
        }
        
        .transfer-receipt-container {
            width: 5in;
            height: 10.5in;
            margin: 0 auto;
            padding: 0.25in;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .company-header {
            text-align: center;
            margin-bottom: 0.3in;
            padding-bottom: 0.2in;
            border-bottom: 1px solid #ddd;
        }
        
        .company-logo {
            max-width: 1.5in;
            margin-bottom: 0.1in;
        }
        
        .receipt-title {
            color: #2c3e50;
            font-size: 16pt;
            font-weight: bold;
            margin: 0.1in 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .receipt-number {
            color: #7f8c8d;
            font-size: 12pt;
            font-weight: bold;
            margin-bottom: 0.1in;
        }
        
        .info-section {
            margin-bottom: 0.2in;
            display: flex;
            flex-wrap: wrap;
        }
        
        .info-group {
            width: 50%;
            margin-bottom: 0.15in;
        }
        
        .info-label {
            color: #7f8c8d;
            font-size: 10pt;
            font-weight: bold;
            margin-bottom: 0.05in;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .info-value {
            color: #2c3e50;
            font-size: 10pt;
            margin-bottom: 0.05in;
            font-weight: 500;
        }
        
        .items-table {
            width: 100%;
            margin: 0.2in 0;
            border-collapse: collapse;
            font-size: 9pt;
        }
        
        .items-table th {
            background: #f8f9fa;
            padding: 0.1in;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .items-table td {
            padding: 0.1in;
            border-bottom: 1px solid #dee2e6;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .footer {
            margin-top: 0.3in;
            padding-top: 0.2in;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #7f8c8d;
            font-size: 8pt;
            position: absolute;
            bottom: 0.25in;
            left: 0.25in;
            right: 0.25in;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.05in 0.1in;
            border-radius: 0.1in;
            font-weight: bold;
            font-size: 9pt;
            text-transform: uppercase;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-in-transit {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .notes-section {
            margin-top: 0.2in;
            padding: 0.1in;
            background-color: #f8f9fa;
            border-radius: 0.1in;
            font-size: 9pt;
        }
        
        .notes-label {
            font-weight: bold;
            margin-bottom: 0.05in;
            color: #7f8c8d;
        }
        
        .notes-content {
            color: #2c3e50;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .transfer-receipt-container {
                box-shadow: none;
            }
        }
    </style>
    <div class="transfer-receipt-container">
        <div class="company-header">
            <img src="assets/img/logo.png" alt="Company Logo" class="company-logo">
            <div class="receipt-title">Transfer Receipt</div>
            <div class="receipt-number"><?php echo htmlspecialchars($transfer['transfer_number']); ?></div>
        </div>
        
        <div class="info-section">
            <div class="info-group">
                <div class="info-label">Source Branch</div>
                <div class="info-value"><?php echo htmlspecialchars($transfer['source_branch']); ?></div>
            </div>
            <div class="info-group">
                <div class="info-label">Destination Branch</div>
                <div class="info-value"><?php echo htmlspecialchars($transfer['destination_branch']); ?></div>
            </div>
            <div class="info-group">
                <div class="info-label">Transfer Date</div>
                <div class="info-value"><?php echo date('F d, Y', strtotime($transfer['created_at'])); ?></div>
            </div>
            <div class="info-group">
                <div class="info-label">Status</div>
                <div class="info-value">
                    <span class="status-badge status-<?php echo strtolower($transfer['status']); ?>">
                        <?php echo ucfirst($transfer['status']); ?>
                    </span>
                </div>
            </div>
            <div class="info-group">
                <div class="info-label">Created By</div>
                <div class="info-value"><?php echo htmlspecialchars($transfer['created_by_name']); ?></div>
            </div>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Quantity</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($items as $item): ?>
                <tr>
                    <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                    <td><?php echo $item['quantity']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <?php if (!empty($transfer['notes'])): ?>
        <div class="notes-section">
            <div class="notes-label">Notes:</div>
            <div class="notes-content"><?php echo nl2br(htmlspecialchars($transfer['notes'])); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="footer">
            <p>Generated on <?php echo date('F d, Y, h:i A'); ?></p>
        </div>
    </div>
    <?php

} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
} finally {
    // Close database connection
    closeDB($conn);
} 