<?php
session_start();
require_once '../config/database.php';

// Get database connection
$conn = connectDB();

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$product_id = (int)$_GET['id'];

// Get product details
$query = "SELECT p.*, c.name as category_name, b.name as brand_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          LEFT JOIN brands b ON p.brand_id = b.id 
          WHERE p.id = ? AND p.is_online = 1";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $product_id);
$stmt->execute();
$result = $stmt->get_result();

// Check if product exists and is available online
if ($result->num_rows === 0) {
    header('Location: index.php');
    exit();
}

$product = $result->fetch_assoc();

// Get related products (same category)
$related_query = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.category_id = ? AND p.id != ? AND p.is_online = 1 
                LIMIT 4";

$stmt = $conn->prepare($related_query);
$stmt->bind_param("ii", $product['category_id'], $product_id);
$stmt->execute();
$related_result = $stmt->get_result();

// Close connection
closeDB($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($product['name']); ?> - Online Store</title>
    <meta name="description" content="<?php echo htmlspecialchars(substr(strip_tags($product['description']), 0, 160)); ?>">
    <link rel="icon" href="../franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/shop-style.css">
    <style>
        /* Reduced font sizes for shop pages */
        body {
            font-size: 14px;
        }

        /* Product description styles */
        .product-detail-description {
            background-color: #fff;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .product-detail-description .card-body {
            padding: 1.5rem;
        }

        .description-content {
            line-height: 1.6;
            color: #4a5568;
        }

        .features-list {
            list-style: none;
            padding-left: 0;
            margin-bottom: 0;
        }

        .features-list li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .features-list li:last-child {
            margin-bottom: 0;
        }
        
        h1, .h1 { font-size: 1.8rem; }
        h2, .h2 { font-size: 1.5rem; }
        h3, .h3 { font-size: 1.3rem; }
        h4, .h4 { font-size: 1.1rem; }
        h5, .h5 { font-size: 1rem; }
        h6, .h6 { font-size: 0.9rem; }
        
        .card-title {
            font-size: 1.1rem;
        }
        
        .card-text {
            font-size: 0.9rem;
        }
        
        .product-title {
            font-size: 1rem;
        }
        
        .product-price {
            font-size: 1.1rem;
        }
        
        .product-description {
            font-size: 0.9rem;
        }
        
        .btn {
            font-size: 0.9rem;
        }
        
        .nav-link {
            font-size: 0.9rem;
        }
        
        .table {
            font-size: 0.9rem;
        }
        
        .form-control {
            font-size: 0.9rem;
        }
        
        .form-label {
            font-size: 0.9rem;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .small {
            font-size: 0.8rem;
        }
        
        .text-muted {
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark navbar-shop sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../logo.png" alt="Store Logo" height="40">
                <span class="ms-2">Online Store</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> Home</a>
                    </li>
                </ul>
                <form class="d-flex me-2" action="index.php" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="search" placeholder="Search products...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                <a href="cart.php" class="btn btn-outline-light position-relative">
                    <i class="fas fa-shopping-cart"></i>
                    <?php if (isset($_SESSION['shop_cart']) && count($_SESSION['shop_cart']) > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <?php echo count($_SESSION['shop_cart']); ?>
                        </span>
                    <?php endif; ?>
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <?php if (!empty($product['category_name'])): ?>
                <li class="breadcrumb-item"><a href="index.php?category=<?php echo $product['category_id']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a></li>
                <?php endif; ?>
                <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($product['name']); ?></li>
            </ol>
        </nav>
        
        <!-- Product Details -->
        <div class="row animate-fade-in">
            <!-- Product Image -->
            <div class="col-md-5 mb-4">
                <div class="card shadow-sm border-0 rounded-3 overflow-hidden">
                    <div class="card-body p-0">
                        <div class="product-img-container bg-light p-4 text-center">
                            <?php if ($product['quantity'] <= 0): ?>
                                <span class="badge bg-danger product-badge">Out of Stock</span>
                            <?php elseif ($product['created_at'] >= date('Y-m-d', strtotime('-7 days'))): ?>
                                <span class="badge bg-success product-badge">New</span>
                            <?php endif; ?>
                            
                            <?php if (!empty($product['image_url'])): ?>
                                <img src="../uploads/products/<?php echo $product['image_url']; ?>" class="product-detail-img" alt="<?php echo htmlspecialchars($product['name']); ?>">
                            <?php else: ?>
                                <img src="no-image.png" class="product-detail-img" alt="No Image Available">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Product Info -->
            <div class="col-md-7">
                <h1 class="product-detail-title"><?php echo htmlspecialchars($product['name']); ?></h1>
                
                <div class="product-detail-price">₱<?php echo number_format($product['unit_price'], 2); ?></div>
                
                <div class="product-detail-meta mb-4">
                    <span><i class="fas fa-box"></i> <?php echo ($product['quantity'] > 0) ? 'In Stock' : 'Out of Stock'; ?></span>
                    
                    <?php if (!empty($product['sku'])): ?>
                    <span><i class="fas fa-barcode"></i> SKU: <?php echo htmlspecialchars($product['sku']); ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($product['category_name'])): ?>
                    <span><i class="fas fa-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($product['brand_name'])): ?>
                    <span><i class="fas fa-copyright"></i> <?php echo htmlspecialchars($product['brand_name']); ?></span>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($product['description'])): ?>
                <div class="product-detail-description mb-4">
                    <div class="card shadow-sm border-0 rounded-3">
                        <div class="card-body">
                            <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>Product Description</h5>
                            <div class="description-content">
                                <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                            </div>
                            <?php if (!empty($product['features'])): ?>
                            <div class="mt-3">
                                <h6 class="mb-2">Key Features:</h6>
                                <ul class="features-list">
                                    <?php foreach (explode('\n', $product['features']) as $feature): ?>
                                        <li><i class="fas fa-check-circle text-success me-2"></i><?php echo htmlspecialchars($feature); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Add to Cart Form -->
                <form action="add_to_cart.php" method="POST" class="mb-4">
                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                    <input type="hidden" name="redirect" value="product.php?id=<?php echo $product['id']; ?>">
                    
                    <?php if ($product['quantity'] > 0): ?>
                    <div class="quantity-selector">
                        <button type="button" class="quantity-btn" id="decrease-qty"><i class="fas fa-minus"></i></button>
                        <input type="number" class="quantity-input" id="quantity" name="quantity" value="1" min="1" max="<?php echo $product['quantity']; ?>">
                        <button type="button" class="quantity-btn" id="increase-qty"><i class="fas fa-plus"></i></button>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg hero-btn">
                            <i class="fas fa-cart-plus me-2"></i> Add to Cart
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-secondary btn-lg" disabled>
                            <i class="fas fa-times me-2"></i> Out of Stock
                        </button>
                    </div>
                    <?php endif; ?>
                </form>
                
                <!-- Share Buttons -->
                <div class="mt-4">
                    <h5>Share This Product</h5>
                    <div class="footer-social mt-2">
                        <a href="#" aria-label="Share on Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Share on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Share on Pinterest"><i class="fab fa-pinterest"></i></a>
                        <a href="#" aria-label="Share on WhatsApp"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <?php if ($related_result->num_rows > 0): ?>
        <section class="mt-5">
            <h2 class="section-title mb-4">Related Products</h2>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <?php $delay = 0.1; while ($related = $related_result->fetch_assoc()): $delay += 0.1; ?>
                <div class="col animate-fade-in" style="animation-delay: <?php echo $delay; ?>s;">
                    <div class="product-card">
                        <div class="product-img-container">
                            <?php if (!empty($related['image_url'])): ?>
                                <img src="../uploads/products/<?php echo $related['image_url']; ?>" class="product-img" alt="<?php echo htmlspecialchars($related['name']); ?>">
                            <?php else: ?>
                                <img src="no-image.png" class="product-img" alt="No Image Available">
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <h5 class="product-title"><?php echo htmlspecialchars($related['name']); ?></h5>
                            <p class="product-price">₱<?php echo number_format($related['unit_price'], 2); ?></p>
                            <div class="d-grid">
                                <a href="product.php?id=<?php echo $related['id']; ?>" class="btn btn-outline-primary product-btn">
                                    View Details <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            </div>
        </section>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <?php require_once 'footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Quantity input validation and controls
            const quantityInput = document.getElementById('quantity');
            const decreaseBtn = document.getElementById('decrease-qty');
            const increaseBtn = document.getElementById('increase-qty');
            
            if (quantityInput && decreaseBtn && increaseBtn) {
                const max = parseInt(quantityInput.getAttribute('max'));
                
                // Decrease quantity
                decreaseBtn.addEventListener('click', function() {
                    let value = parseInt(quantityInput.value);
                    if (value > 1) {
                        quantityInput.value = value - 1;
                    }
                });
                
                // Increase quantity
                increaseBtn.addEventListener('click', function() {
                    let value = parseInt(quantityInput.value);
                    if (value < max) {
                        quantityInput.value = value + 1;
                    } else {
                        alert('Sorry, we only have ' + max + ' items in stock.');
                    }
                });
                
                // Direct input validation
                quantityInput.addEventListener('change', function() {
                    const value = parseInt(this.value);
                    
                    if (value > max) {
                        this.value = max;
                        alert('Sorry, we only have ' + max + ' items in stock.');
                    }
                    
                    if (value < 1 || isNaN(value)) {
                        this.value = 1;
                    }
                });
            }
            
            // Image zoom effect on hover
            const productImage = document.querySelector('.product-detail-img');
            if (productImage) {
                productImage.addEventListener('mouseover', function() {
                    this.style.transform = 'scale(1.05)';
                });
                
                productImage.addEventListener('mouseout', function() {
                    this.style.transform = 'scale(1)';
                });
            }
        });
    </script>
</body>
</html>