<?php
require_once 'config/database.php';

// Start session to get user info and set messages
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login or show an error
    $_SESSION['error_message'] = "You must be logged in to create a purchase order.";
    header('Location: login.php');
    exit();
}

// Check if the form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error_message'] = "Invalid request method.";
    header('Location: create_purchase_order.php'); // Redirect back to form
    exit();
}

// --- Get Data from Form ---
$po_number = isset($_POST['po_number']) ? trim($_POST['po_number']) : null;
$supplier_id = isset($_POST['supplier_id']) ? (int)$_POST['supplier_id'] : null;
$order_date = isset($_POST['order_date']) ? trim($_POST['order_date']) : date('Y-m-d'); // Default to today
$expected_delivery_date = isset($_POST['expected_delivery_date']) && !empty($_POST['expected_delivery_date']) ? trim($_POST['expected_delivery_date']) : null;
$status = isset($_POST['status']) ? trim($_POST['status']) : 'Pending'; // Default status
$notes = isset($_POST['notes']) ? trim($_POST['notes']) : null;
$products = isset($_POST['products']) && is_array($_POST['products']) ? $_POST['products'] : [];

// Get current user's branch
$ordering_branch = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch'; // Or handle error if branch not set

// --- Validation ---
if (empty($po_number) || empty($supplier_id) || empty($products)) {
    $_SESSION['error_message'] = "Missing required fields (PO Number, Supplier, or Products).";
    // Optionally pass back submitted data to repopulate form
    header('Location: create_purchase_order.php');
    exit();
}

// --- Database Insertion ---
$conn = connectDB();
$conn->begin_transaction(); // Start transaction

$success = true;
$inserted_items = 0;

// Prepare statement for inserting items (assuming one row per item in purchase_orders)
$insert_query = "INSERT INTO purchase_orders
                 (po_number, supplier_id, sku, quantity, unit_price, created_at, expected_delivery_date, status, notes, ordering_branch, created_by)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt_insert = $conn->prepare($insert_query);

if (!$stmt_insert) {
    $success = false;
    $_SESSION['error_message'] = "Database error preparing statement: " . $conn->error;
} else {
    // Get user ID from session
    $user_id = $_SESSION['user_id'];
    $created_at_datetime = $order_date . ' ' . date('H:i:s'); // Combine date with current time

    foreach ($products as $product_id => $item) {
        // Sanitize/validate item data
        $sku = isset($item['sku']) ? trim($item['sku']) : null; // Assuming SKU is passed or fetchable based on ID
        $quantity = isset($item['quantity']) ? (int)$item['quantity'] : 0;
        $unit_price = isset($item['cost']) ? (float)$item['cost'] : 0.00; // 'cost' from the form input

        // Fetch SKU if not directly submitted (might need adjustment based on your form)
        // For now, assume SKU is available or handle fetching it if needed.
        // If SKU isn't in $item, you'd need to query the products table using $product_id.
        // Example: $sku_result = $conn->query("SELECT sku FROM products WHERE id = " . (int)$product_id); $sku = $sku_result->fetch_assoc()['sku'];

        if (empty($sku) || $quantity <= 0) {
            $_SESSION['error_message'] = "Invalid data for one or more products (Missing SKU or invalid quantity).";
            $success = false;
            break; // Stop processing items
        }


        // Bind parameters for each item
        // Types: s=string, i=integer, d=double, b=blob
        $stmt_insert->bind_param(
            "sisiddssssi",
            $po_number,
            $supplier_id,
            $sku, // Make sure you have the correct SKU here
            $quantity,
            $unit_price,
            $created_at_datetime, // Use combined date and time
            $expected_delivery_date, // Can be null
            $status,
            $notes, // Same notes for all items in this PO
            $ordering_branch,
            $user_id // Use the user_id from session for the created_by column
        );

        if (!$stmt_insert->execute()) {
            $_SESSION['error_message'] = "Error inserting item (SKU: " . htmlspecialchars($sku) . "): " . $stmt_insert->error;
            $success = false;
            break; // Stop inserting on first error
        } else {
            $inserted_items++;
        }
    }
    $stmt_insert->close();
}

// --- Commit or Rollback ---
if ($success && $inserted_items > 0) {
    $conn->commit();
    $_SESSION['success_message'] = "Purchase Order '" . htmlspecialchars($po_number) . "' created successfully with " . $inserted_items . " item(s).";
    // Redirect to the PO list or the detail page of the new PO
    header('Location: purchase_order_v2.php'); // Redirect to list view
    // Or redirect to detail view: header('Location: purchase_order_detail_v2.php?po_number=' . urlencode($po_number));
    exit();
} else {
    $conn->rollback();
    // Error message should already be set
    if (empty($_SESSION['error_message'])) { // Set a generic error if none was specific
        $_SESSION['error_message'] = "Failed to create purchase order. Please try again.";
    }
    // Redirect back to the form
    header('Location: create_purchase_order.php');
    exit();
}

closeDB($conn); // Close connection (might not be reached due to redirects)
?>