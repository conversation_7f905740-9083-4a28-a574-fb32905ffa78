-- Create database
CREATE DATABASE IF NOT EXISTS inventory_management;
USE inventory_management;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role ENUM('admin', 'manager', 'staff') NOT NULL DEFAULT 'staff',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category_id INT,
    sku VARCHAR(50) NOT NULL UNIQUE,
    quantity INT NOT NULL DEFAULT 0,
    unit_price DECIMAL(10, 2) NOT NULL,
    reorder_level INT NOT NULL DEFAULT 10,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100),
    customer_phone VARCHAR(20),
    total_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Expenses table
CREATE TABLE IF NOT EXISTS expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description TEXT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    expense_date DATE NOT NULL,
    category VARCHAR(50) NOT NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, full_name, email, role) VALUES
('admin', '$2y$10$8zUlxiVDpvfR9n0aW1u6.eVQQleTCcYPW.xJUyFX3aOkFdqQUVzWG', 'Admin User', '<EMAIL>', 'admin');

-- Insert sample categories
INSERT INTO categories (name, description) VALUES
('Electronics', 'Electronic devices and accessories'),
('Office Supplies', 'Office stationery and supplies'),
('Furniture', 'Office and home furniture');

-- Insert sample products
INSERT INTO products (name, description, category_id, sku, quantity, unit_price, reorder_level, image_url) VALUES
('Laptop', 'High-performance laptop', 1, 'ELEC-001', 15, 1200.00, 5, 'laptop.jpg'),
('Smartphone', 'Latest smartphone model', 1, 'ELEC-002', 25, 800.00, 8, 'smartphone.jpg'),
('Desk Chair', 'Ergonomic office chair', 3, 'FURN-001', 10, 150.00, 3, 'chair.jpg'),
('Notebook', 'Premium quality notebook', 2, 'OFSP-001', 100, 5.00, 20, 'notebook.jpg'),
('Pen Set', 'Set of 10 premium pens', 2, 'OFSP-002', 50, 12.00, 15, 'pen-set.jpg');

-- Insert sample suppliers
INSERT INTO suppliers (name, contact_person, email, phone, address) VALUES
('Tech Supplies Inc.', 'John Smith', '<EMAIL>', '555-1234', '123 Tech St, Silicon Valley, CA'),
('Office Essentials', 'Jane Doe', '<EMAIL>', '555-5678', '456 Office Blvd, Business Park, NY'),
('Furniture Depot', 'Mike Johnson', '<EMAIL>', '555-9012', '789 Furniture Ave, Design District, FL');

-- Insert sample orders
INSERT INTO orders (order_number, customer_name, customer_email, customer_phone, total_amount, status, created_by) VALUES
('ORD-2023-001', 'Acme Corporation', '<EMAIL>', '555-1111', 2400.00, 'completed', 1),
('ORD-2023-002', 'Global Enterprises', '<EMAIL>', '555-2222', 1750.00, 'processing', 1),
('ORD-2023-003', 'Local Business', '<EMAIL>', '555-3333', 450.00, 'pending', 1);

-- Insert sample order items
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES
(1, 1, 2, 1200.00, 2400.00),
(2, 2, 2, 800.00, 1600.00),
(2, 4, 30, 5.00, 150.00),
(3, 3, 3, 150.00, 450.00);

-- Insert sample expenses
INSERT INTO expenses (description, amount, expense_date, category, created_by) VALUES
('Office Rent', 2000.00, '2023-01-01', 'Rent', 1),
('Utilities', 500.00, '2023-01-05', 'Utilities', 1),
('Inventory Restock', 3500.00, '2023-01-10', 'Inventory', 1),
('Marketing Campaign', 1000.00, '2023-01-15', 'Marketing', 1);