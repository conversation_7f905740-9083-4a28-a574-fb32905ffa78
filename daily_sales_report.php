<?php
require_once 'config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get database connection
$conn = connectDB();

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Get report date (today by default)
$report_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Handle AJAX request to save remittance
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_remittance') {
    $remittance_amount = isset($_POST['remittance_amount']) ? floatval($_POST['remittance_amount']) : 0;
    $_SESSION['remittanceAmount'] = $remittance_amount;

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'amount' => $remittance_amount]);
    exit();
}

// Initialize arrays to store data
$cash_transactions = [];
$card_transactions = [];
$cheque_transactions = [];
$bank_transactions = [];
$wallet_transactions = [];
$charge_transactions = [];
$tuition_payments = [];

// Initialize totals
$card_debit_total = 0;
$card_credit_total = 0;
$cheque_total = 0;
$charge_total = 0;
$cash_total = 0;
$bank_transfer_total = 0;
$wallet_total = 0;
$tuition_total = 0;
$grand_total = 0;

// Get all sales for the selected date and branch
$query = "SELECT o.id, o.order_number, o.invoice_number, o.customer_name, o.customer_phone,o.customer_email,
          o.payment_type, o.total_amount, o.created_at, o.status
          FROM orders o
          WHERE DATE(o.created_at) = ? AND o.branch_name = ? AND o.status != 'cancelled'
          ORDER BY o.payment_type, o.invoice_number";

$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $report_date, $branch_name);
$stmt->execute();
$result = $stmt->get_result();

// Process orders
while ($order = $result->fetch_assoc()) {
    $order_id = $order['id'];
    $payment_type = $order['payment_type'];

    // Get order items
    $items_query = "SELECT oi.*, p.name as product_name, p.sku, c.name as category_name
                   FROM order_items oi
                   LEFT JOIN products p ON oi.product_id = p.id
                   LEFT JOIN categories c ON p.category_id = c.id
                   WHERE oi.order_id = ?";

    $items_stmt = $conn->prepare($items_query);
    $items_stmt->bind_param("i", $order_id);
    $items_stmt->execute();
    $items_result = $items_stmt->get_result();

    $order_items = [];
    $is_tuition = false;

    while ($item = $items_result->fetch_assoc()) {
        // Check if this is a tuition payment
        if (stripos($item['product_name'], 'tuition') !== false) {
            $is_tuition = true;
        }
        $order_items[] = $item;
    }

    // Add to appropriate array based on payment type and whether it's tuition
    if ($is_tuition) {
        $tuition_payments[] = [
            'order' => $order,
            'items' => $order_items
        ];
        $tuition_total += floatval($order['total_amount']);

    } else {
        if ($payment_type == 'Cash') {
            $cash_transactions[] = [
                'order' => $order,
                'items' => $order_items
            ];
            $cash_total += floatval($order['total_amount']);  // Changed += to add to scalar

        } elseif ($payment_type == 'Card (Debit)') {  // Fixed typo in payment type name
            $card_transactions[] = [                         // Store in card_transactions array
                'order' => $order,
                'items' => $order_items
            ];
            $card_debit_total += floatval($order['total_amount']);  // Add to scalar total

        } elseif ($payment_type == 'Card (Credit)') {  // Fixed typo in payment type name
            $card_transactions[] = [                         // Store in card_transactions array
                'order' => $order,
                'items' => $order_items
            ];
            $card_credit_total += floatval($order['total_amount']);  // Add to scalar total

        } elseif ($payment_type == 'Cheque') {
            $cheque_transactions[] = [                       // Store in cheque_transactions array
                'order' => $order,
                'items' => $order_items
            ];
            $cheque_total += floatval($order['total_amount']);  // Add to scalar total

        } elseif ($payment_type == 'Charge') {
            $charge_transactions[] = [                       // Added charge_transactions array
                'order' => $order,
                'items' => $order_items
            ];
            $charge_total += floatval($order['total_amount']);  // Add to scalar total

        } elseif ($payment_type == 'Bank Transfer') {
            $bank_transactions[] = [                         // Store in bank_transactions array
                'order' => $order,
                'items' => $order_items
            ];
            $bank_transfer_total += floatval($order['total_amount']);  // Add to scalar total

        } elseif ($payment_type == 'GCash/Maya') {
            $wallet_transactions[] = [                         // Store in bank_transactions array
                'order' => $order,
                'items' => $order_items
            ];
            $wallet_total += floatval($order['total_amount']);  // Add to scalar total
        }
    }
}

// Calculate grand total
$retail_total = $card_debit_total + $card_credit_total + $cheque_total + $charge_total + $cash_total + $bank_transfer_total + $wallet_total;
$grand_total = $retail_total + $tuition_total;

// Get expenses for the day
$expenses_query = "SELECT e.*, u.username
                 FROM expenses e
                 LEFT JOIN users u ON e.created_by = u.id
                 WHERE DATE(e.expense_date) = ? AND e.branch_name = ?";

$expenses_stmt = $conn->prepare($expenses_query);
$expenses_stmt->bind_param("ss", $report_date, $branch_name);
$expenses_stmt->execute();
$expenses_result = $expenses_stmt->get_result();

$expenses = [];
$expenses_total = 0;

while ($expense = $expenses_result->fetch_assoc()) {
    $expenses[] = $expense;
    $expenses_total += floatval($expense['amount']);
}

// BEGINNING BALANCE QUERY (moved here)
$beginning_balance = 0;
$beginning_balance_date = '';
$beginning_fund_query = "SELECT amount, fund_date FROM cash_funds WHERE branch_name = ? AND fund_date <= ? ORDER BY fund_date DESC, id DESC LIMIT 1";
$stmt_begin = $conn->prepare($beginning_fund_query);
$stmt_begin->bind_param("ss", $branch_name, $report_date);
$stmt_begin->execute();
$begin_result = $stmt_begin->get_result();
if ($begin_row = $begin_result->fetch_assoc()) {
    $beginning_balance = floatval($begin_row['amount']);
    $beginning_balance_date = $begin_row['fund_date'];
}
$stmt_begin->close();

// Close database connection
$stmt->close();
closeDB($conn);

// Format date for display
$display_date = date('m/d/Y', strtotime($report_date));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Sales Report - <?php echo htmlspecialchars($display_date); ?></title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/print-styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
            background-color: #e9ecef;
        }

        .page-content-wrapper {
            background-color: #ffffff;
            width: 8.5in;
            min-height: 11in;
            margin: 0 auto;
            padding: 0.3in;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            border-radius: 5px;
        }

        @media print {
            @page {
                size: 8.5in 11in;
                margin: 0;
            }

            body {
                padding: 0;
                background-color: #ffffff;
            }

            .page-content-wrapper {
                width: 8.5in;
                height: 11in;
                padding: 0.3in;
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }

            .no-print {
                display: none;
            }
        }

        .report-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .report-header h1 {
            color: #ff0000;
            margin-bottom: 0;
            font-size: 20px;
            line-height: 1.1;
        }

        .report-header h2 {
            margin-top: 0;
            margin-bottom: 0;
            font-size: 16px;
            line-height: 1.1;
        }

        .report-header p {
            margin: 1px 0;
            line-height: 1.1;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            margin-top: 0;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
            font-size: 11px;
        }

        th {
            background-color: #f2f2f2;
        }

        .amount {
            text-align: right;
        }

        .section-header {
            color: #0000ff;
            margin-top: 15px;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: bold;
        }

        .invoice-total {
            font-weight: bold;
            background-color: #f2f2f2;
        }

        .payment-total {
            font-weight: bold;
            color: #0000ff;
            margin: 5px 0 10px 0;
        }

        .summary-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }

        .expenses-section {
            flex: 1;
            min-width: 300px;
        }

        .summary-section {
            flex: 0 0 auto;
            width: 300px;
        }

        .mb-1 {
            margin-bottom: 1px;
        }

        .mb-2 {
            margin-bottom: 2px;
        }

        h4 {
            margin-top: 10px;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
            padding: 6px;
        }

        .text-right {
            text-align: right !important;
        }

        .text-center {
            text-align: center !important;
        }

        @media print {
            .summary-container {
                display: flex;
                flex-wrap: nowrap;
            }

            .expenses-section {
                flex: 1;
            }

            .summary-section {
                width: 300px;
            }
        }

        .print-buttons {
            margin-bottom: 20px;
            text-align: center;
        }

        .print-buttons button {
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .print-buttons button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="print-buttons no-print">
        <button onclick="window.print()">Print Report</button>
        <button onclick="window.location.href='sales_list.php'">Back to Sales List</button>
        <button id="remittanceBtn" type="button">Set Remittance</button>
    </div>

    <div class="page-content-wrapper">
        <div class="report-header">
            <h1>MUSAR MUSIC CORPORATION</h1>
            <p>Daily Sales Report</p>
            <p>Transaction Date: <?php echo htmlspecialchars($display_date); ?></p>
            <p>Branch: <strong><?php echo htmlspecialchars($branch_name); ?></strong></p>
        </div>

        <!-- RETAIL SECTION -->
        <h3 class="section-header">RETAIL</h3>
        <?php if (count($cash_transactions) > 0): ?>
        <h4>Cash Transactions</h4>
        <?php foreach ($cash_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0 mb-2">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty</th>
                        <th width="15%">Price</th>
                        <th width="10%">Disc</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">Cash Total: <?php echo number_format($cash_total, 2); ?></p>
        <?php endif; ?>

        <?php
        $card_debit_transactions = array_filter($card_transactions, function($transaction) {
            return $transaction['order']['payment_type'] == 'Card (Debit)';
        });
        if (count($card_debit_transactions) > 0):
        ?>
        <h4>Card (Debit) Transactions</h4>
        <?php foreach ($card_debit_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty Sold</th>
                        <th width="15%">Selling Price</th>
                        <th width="10%">Discount</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">Card Debit Total: <?php echo number_format($card_debit_total, 2); ?></p>
        <?php endif; ?>

        <?php
        $card_credit_transactions = array_filter($card_transactions, function($transaction) {
            return $transaction['order']['payment_type'] == 'Card (Credit)';
        });
        if (count($card_credit_transactions) > 0):
        ?>
        <h4>Card (Credit) Transactions</h4>
        <?php foreach ($card_credit_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty Sold</th>
                        <th width="15%">Selling Price</th>
                        <th width="10%">Discount</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">Card Credit Total: <?php echo number_format($card_credit_total, 2); ?></p>
        <?php endif; ?>

        <?php if (count($cheque_transactions) > 0): ?>
        <h4>Cheque Transactions</h4>
        <?php foreach ($cheque_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty Sold</th>
                        <th width="15%">Selling Price</th>
                        <th width="10%">Discount</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">Cheque Total: <?php echo number_format($cheque_total, 2); ?></p>
        <?php endif; ?>

        <?php if (count($charge_transactions) > 0): ?>
        <h4>Charge Transactions</h4>
        <?php foreach ($charge_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty Sold</th>
                        <th width="15%">Selling Price</th>
                        <th width="10%">Discount</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">Charge Total: <?php echo number_format($charge_total, 2); ?></p>
        <?php endif; ?>

        <?php if (count($bank_transactions) > 0): ?>
        <h4>Bank Transfer Transactions</h4>
        <?php foreach ($bank_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty Sold</th>
                        <th width="15%">Selling Price</th>
                        <th width="10%">Discount</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">Bank Transfer Total: <?php echo number_format($bank_transfer_total, 2); ?></p>
        <?php endif; ?>

        <?php if (count($wallet_transactions) > 0): ?>
        <h4>GCash/Maya Transactions</h4>
        <?php foreach ($wallet_transactions as $transaction): ?>
            <p class="mb-0">Invoice #: <?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></p>
            <table class="mt-0">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th width="10%">Qty Sold</th>
                        <th width="15%">Selling Price</th>
                        <th width="10%">Discount</th>
                        <th width="15%">Total</th>
                        <th width="15%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                            <td class="text-center"><?php echo $item['quantity']; ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td class="amount">0.00</td>
                            <td class="amount"><?php echo number_format($item['total_price'], 2); ?></td>
                            <td></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr class="invoice-total">
                        <td colspan="4" class="text-right">Invoice-total</td>
                        <td class="amount"><?php echo number_format($transaction['order']['total_amount'], 2); ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        <?php endforeach; ?>
        <p class="payment-total">GCash/Maya Total: <?php echo number_format($wallet_total, 2); ?></p>
        <?php endif; ?>

        <?php if (count($tuition_payments) > 0): ?>
        <!-- TUITION SECTION -->
        <h3 class="section-header">School Payments</h3>
        <table class="mt-0">
            <thead>
                <tr>
                    <th>Invoice #</th>
                    <th>Student Name</th>
                    <th>Teacher</th>
                    <th>Lesson</th>
                    <th width="15%">Amount</th>
                    <th width="15%">Payment Method</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($tuition_payments as $transaction): ?>
                    <?php foreach ($transaction['items'] as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($transaction['order']['invoice_number'] ?: 'N/A'); ?></td>
                            <td><?php echo htmlspecialchars($transaction['order']['customer_name'] ?: ''); ?></td>
                            <td><?php echo htmlspecialchars($transaction['order']['customer_email'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($transaction['order']['customer_phone'] ?? ''); ?></td>
                            <td class="amount"><?php echo number_format($item['unit_price'], 2); ?></td>
                            <td>
                                <?php echo htmlspecialchars($transaction['order']['payment_type']); ?><br>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endforeach; ?>
                <tr><td colspan="7" class="payment-total">School Fees Total: <?php echo number_format($tuition_total, 2); ?></td></tr>
            </tbody>
        </table>
        <?php endif; ?>

        <!-- SUMMARY AND EXPENSES SECTION -->

        <div class="summary-container">
            <!-- EXPENSES SECTION -->
            <div class="expenses-section">
                <h4>Expenses</h4>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Description</th>
                            <th class="amount">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="table-primary">
                            <td colspan="2" class="text-right"><strong>Beginning Balance<?php echo $beginning_balance_date ? ' (as of ' . date('M d, Y', strtotime($beginning_balance_date)) . ')' : ''; ?></strong></td>
                            <td class="amount"><strong><?php echo number_format($beginning_balance, 2); ?></strong></td>
                        </tr>

                        <?php if (count($expenses) > 0): ?>
                            <?php foreach ($expenses as $expense): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($expense['category'] ?? 'General'); ?></td>
                                    <td><?php echo htmlspecialchars($expense['description']); ?></td>
                                    <td class="amount"><?php echo number_format($expense['amount'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="3" class="text-center">No expenses recorded</td>
                            </tr>
                        <?php endif; ?>
                        <tr class="invoice-total">
                            <td colspan="2" class="text-right">Total Expenses</td>
                            <td class="amount"><?php echo number_format($expenses_total, 2); ?></td>
                        </tr>
                        <tr class="table-success">
                            <td colspan="2" class="text-right"><strong>Ending Balance</strong></td>
                            <td class="amount"><strong><?php echo number_format($beginning_balance - $expenses_total, 2); ?></strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- FINANCIAL SUMMARY -->
            <div class="summary-section">
                <h4>Financial Summary (Retail)</h4>
                <table class="summary-table">
                    <thead>
                        <tr>
                            <th>Particulars</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Card Payments (Debit)</td>
                            <td class="amount"><?php echo number_format($card_debit_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Card Payment (Credit)</td>
                            <td class="amount"><?php echo number_format($card_credit_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Cheque Payments</td>
                            <td class="amount"><?php echo number_format($cheque_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Charge Transactions</td>
                            <td class="amount"><?php echo number_format($charge_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Bank Transfer</td>
                            <td class="amount"><?php echo number_format($bank_transfer_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Gcash/Maya</td>
                            <td class="amount"><?php echo number_format($wallet_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Cash Transactions</td>
                            <td class="amount"><?php echo number_format($cash_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Total (Retail)</strong></td>
                            <td class="amount"><strong><?php echo number_format($retail_total, 2); ?></strong></td>
                        </tr>
                    </tbody>
                </table>
                <h4>Financial Summary (School Payments)</h4>
                <table class="summary-table">
                    <thead>
                        <tr>
                            <th>Payment Method</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Group tuition payments by payment method
                        $school_payment_methods = [];
                        foreach ($tuition_payments as $transaction) {
                            $method = $transaction['order']['payment_type'] ?: 'Other';
                            if (!isset($school_payment_methods[$method])) {
                                $school_payment_methods[$method] = 0;
                            }
                            $school_payment_methods[$method] += floatval($transaction['order']['total_amount']);
                        }

                        // Initialize all payment methods with 0 if not set
                        $all_methods = ['Card (Debit)', 'Card (Credit)', 'Cheque', 'Charge', 'Bank Transfer', 'GCash/Maya', 'Cash'];
                        foreach ($all_methods as $method) {
                            if (!isset($school_payment_methods[$method])) {
                                $school_payment_methods[$method] = 0;
                            }
                        }

                        // Display all payment methods
                        foreach ($all_methods as $method): ?>
                        <tr>
                            <td><?php echo $method; ?></td>
                            <td class="amount"><?php echo number_format($school_payment_methods[$method], 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                        <tr>
                            <td><strong>Total School Payments</strong></td>
                            <td class="amount"><strong><?php echo number_format(array_sum($school_payment_methods), 2); ?></strong></td>
                        </tr>
                    </tbody>
                </table>
                <!-- New summary table for Total Retail, Total Tuition, Petty Cash Balance -->
                <h4>Financial Summary (Gross Cash Sales)</h4>
                <table class="summary-table">
                    <thead>
                        <tr>
                            <th>Particulars</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Total Retail</td>
                            <td class="amount"><?php echo number_format($retail_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Total Tuition</td>
                            <td class="amount"><?php echo number_format($tuition_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Petty Cash Balance</td>
                            <td class="amount"><?php echo number_format($beginning_balance, 2); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Total</strong></td>
                            <td class="amount"><strong><?php echo number_format($retail_total + $tuition_total + $beginning_balance, 2); ?></strong></td>
                        </tr>
                    </tbody>
                </table>
                <h4>Grand Total</h4>
                <table class="summary-table">
                    <thead>
                        <tr>
                            <th>Particulars</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Expenses</td>
                            <td class="amount"><?php echo number_format($expenses_total, 2); ?></td>
                        </tr>
                        <?php
                        // Calculate school payments GCash/Maya total
                        $school_gcash_total = 0;
                        foreach ($tuition_payments as $transaction) {
                            if (isset($transaction['order']['payment_type']) && $transaction['order']['payment_type'] === 'GCash/Maya') {
                                $school_gcash_total += floatval($transaction['order']['total_amount']);
                            }
                        }
                        $total_gcash = $wallet_total + $school_gcash_total;
                        ?>
                        <tr>
                            <td>Total Gcash</td>
                            <td class="amount"><?php echo number_format($total_gcash, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Total Debit</td>
                            <td class="amount"><?php echo number_format($card_debit_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Total Credit</td>
                            <td class="amount"><?php echo number_format($card_credit_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Total Bank Transfer</td>
                            <td class="amount"><?php echo number_format($bank_transfer_total, 2); ?></td>
                        </tr>
                        <tr>
                            <td>Total Cheque</td>
                            <td class="amount"><?php echo number_format($cheque_total, 2); ?></td>
                        </tr>
                        <tr id="remittanceRow">
                            <td>Remittance</td>
                            <td class="amount">
                                <span id="remittanceDisplay">
                                    <?php
                                    // Always show remittance, get from session or default to 0
                                    $remittance_amount = isset($_SESSION['remittanceAmount']) ? $_SESSION['remittanceAmount'] : 0;
                                    echo number_format($remittance_amount, 2);
                                    ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>Cash-on-Hand</td>
                            <td class="amount">
                                <?php
                                // Calculate petty cash balance after expenses
                                $petty_cash_balance_after_expenses = $beginning_balance - $expenses_total;
                                // Get remittance from session or default to 0
                                $remittance = isset($_SESSION['remittanceAmount']) ? floatval($_SESSION['remittanceAmount']) : 0;
                                // Cash-on-Hand = remittance - (total retail + total tuition + (petty cash balance - expenses))
                                $cash_on_hand = $remittance - ($retail_total + $tuition_total + $petty_cash_balance_after_expenses);
                                echo number_format($cash_on_hand, 2);
                                ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get elements
            const remittanceBtn = document.getElementById('remittanceBtn');
            const remittanceBox = document.getElementById('remittanceBox');
            const closeRemittanceBox = document.getElementById('closeRemittanceBox');
            const saveFloatingRemittance = document.getElementById('saveFloatingRemittance');
            const floatingRemittanceAmount = document.getElementById('floatingRemittanceAmount');
            const remittanceDisplay = document.getElementById('remittanceDisplay');

            // Initialize remittance amount from localStorage or PHP session
            let remittanceAmount = parseFloat(localStorage.getItem('remittanceAmount')) || 0;

            // Check if PHP session has remittance amount and sync with localStorage
            <?php if (isset($_SESSION['remittanceAmount'])): ?>
            const sessionRemittance = <?php echo $_SESSION['remittanceAmount']; ?>;
            if (sessionRemittance !== remittanceAmount) {
                remittanceAmount = sessionRemittance;
                localStorage.setItem('remittanceAmount', remittanceAmount);
            }
            <?php endif; ?>

            updateDisplays();

            // Show remittance box when button is clicked
            if (remittanceBtn) {
                remittanceBtn.addEventListener('click', function() {
                    if (remittanceBox) {
                        remittanceBox.style.display = 'block';
                        if (floatingRemittanceAmount) {
                            floatingRemittanceAmount.value = remittanceAmount;
                        }
                    }
                });
            }

            // Close remittance box when close button is clicked
            if (closeRemittanceBox) {
                closeRemittanceBox.addEventListener('click', function() {
                    if (remittanceBox) {
                        remittanceBox.style.display = 'none';
                    }
                });
            }

            // Save remittance amount when save button is clicked
            if (saveFloatingRemittance) {
                saveFloatingRemittance.addEventListener('click', function() {
                    const newAmount = parseFloat(floatingRemittanceAmount.value) || 0;
                    if (newAmount < 0) {
                        alert('Please enter a valid positive number');
                        return;
                    }

                    remittanceAmount = newAmount;
                    localStorage.setItem('remittanceAmount', remittanceAmount);

                    // Send to PHP session via AJAX
                    const formData = new FormData();
                    formData.append('action', 'save_remittance');
                    formData.append('remittance_amount', remittanceAmount);

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateDisplays();
                        }
                    })
                    .catch(error => {
                        console.error('Error saving remittance:', error);
                        updateDisplays(); // Still update displays even if AJAX fails
                    });

                    remittanceBox.style.display = 'none';
                });
            }

            // Close box when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target == remittanceBox) {
                    remittanceBox.style.display = 'none';
                }
            });

            // Allow dragging the remittance box
            makeDraggable(remittanceBox);

            function updateDisplays() {
                // Update remittance display
                if (remittanceDisplay) {
                    remittanceDisplay.textContent = remittanceAmount.toFixed(2);
                }

                // Remittance row is now always visible, no need to show/hide

                // Recalculate cash on hand using new formula
                // cash-on-hand = remittance - (total retail + total tuition + (petty cash balance - expenses))
                const retailTotal = <?php echo $retail_total; ?>;
                const tuitionTotal = <?php echo $tuition_total; ?>;
                const beginningBalance = <?php echo $beginning_balance; ?>;
                const expensesTotal = <?php echo $expenses_total; ?>;
                const pettyCashBalanceAfterExpenses = beginningBalance - expensesTotal;
                const cashOnHand = remittanceAmount - (retailTotal + tuitionTotal + pettyCashBalanceAfterExpenses);

                // Find the cash on hand element and update it
                const rows = document.querySelectorAll('.summary-table tbody tr');
                if (rows.length > 0) {
                    const lastRow = rows[rows.length - 1];
                    const amountCell = lastRow.querySelector('.amount strong');
                    if (amountCell) {
                        amountCell.textContent = number_format(cashOnHand, 2);
                    }
                }
            }

            // Helper function to format numbers like PHP's number_format
            function number_format(number, decimals) {
                return number.toLocaleString('en-US', {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                });
            }

            // Make an element draggable
            function makeDraggable(element) {
                if (!element) return;

                let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

                // Get the header element to use as the drag handle
                const header = element.querySelector('.remittance-header');
                if (header) {
                    header.onmousedown = dragMouseDown;
                } else {
                    element.onmousedown = dragMouseDown;
                }

                function dragMouseDown(e) {
                    e = e || window.event;
                    e.preventDefault();
                    // Get the mouse cursor position at startup
                    pos3 = e.clientX;
                    pos4 = e.clientY;
                    document.onmouseup = closeDragElement;
                    // Call a function whenever the cursor moves
                    document.onmousemove = elementDrag;
                }

                function elementDrag(e) {
                    e = e || window.event;
                    e.preventDefault();
                    // Calculate the new cursor position
                    pos1 = pos3 - e.clientX;
                    pos2 = pos4 - e.clientY;
                    pos3 = e.clientX;
                    pos4 = e.clientY;
                    // Set the element's new position
                    element.style.top = (element.offsetTop - pos2) + "px";
                    element.style.left = (element.offsetLeft - pos1) + "px";
                }

                function closeDragElement() {
                    // Stop moving when mouse button is released
                    document.onmouseup = null;
                    document.onmousemove = null;
                }
            }
        });
    </script>
</body>
</html>


<!-- Include Bootstrap JS for modal functionality -->
<script src="assets/js/bootstrap.bundle.min.js"></script>

<!-- Floating Remittance Box (initially hidden) -->
<div id="remittanceBox" class="remittance-box no-print" style="display: none;">
    <div class="remittance-header">
        <h5>Set Remittance Amount</h5>
        <button type="button" class="close-btn" id="closeRemittanceBox">&times;</button>
    </div>
    <div class="remittance-body">
        <div class="form-group">
            <label for="floatingRemittanceAmount">Remittance Amount</label>
            <input type="number" class="form-control" id="floatingRemittanceAmount" step="0.01" min="0">
        </div>
        <button type="button" class="save-btn" id="saveFloatingRemittance">Save</button>
    </div>
</div>

<style>
    .remittance-box {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 300px;
        background-color: white;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        z-index: 1000;
    }

    .remittance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        cursor: move;
    }

    .remittance-header h5 {
        margin: 0;
        font-size: 16px;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
    }

    .remittance-body {
        padding: 15px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
    }

    .form-control {
        width: 100%;
        padding: 8px;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .save-btn {
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        cursor: pointer;
    }

    .save-btn:hover {
        background-color: #0056b3;
    }
</style>



















