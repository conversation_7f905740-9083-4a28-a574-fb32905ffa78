body {
  /* Lighter default background for better contrast with paper */
  background-color: #e9ecef;
  transition: background-color 0.3s ease;
  /* Add some padding to prevent content touching edges */
  padding-top: 20px;
  padding-bottom: 20px;
}

/* Styles for dark mode */
@media (prefers-color-scheme: dark) {
  body {
    /* Darker background for dark mode */
    background-color: #1c1c1e; /* Slightly darker than before */
  }
  .page-content-wrapper {
     /* Dark paper background */
     background-color: #2c2c2e; /* Darker gray for the paper */
     color: #eaeaeb; /* Light text for dark mode */
     /* More pronounced shadow for dark mode */
     box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
     border: 1px solid #444; /* Optional subtle border for dark mode */
  }
   /* Adjust common elements for dark mode */
  .page-content-wrapper .section-title,
  .page-content-wrapper .product-title,
  .page-content-wrapper .product-price,
  .page-content-wrapper .product-meta,
  .page-content-wrapper h1,
  .page-content-wrapper h2,
  .page-content-wrapper h3,
  .page-content-wrapper h4,
  .page-content-wrapper h5,
  .page-content-wrapper p,
  .page-content-wrapper label,
  .page-content-wrapper .form-label,
  .page-content-wrapper .breadcrumb-item a,
  .page-content-wrapper .breadcrumb-item.active,
  .page-content-wrapper .nav-link, /* Added nav links inside wrapper */
  .page-content-wrapper .dropdown-item, /* Added dropdown items */
  .page-content-wrapper .list-group-item, /* Added list group items */
  .page-content-wrapper .table /* Added tables */
  {
      color: #eaeaeb; /* Consistent light text color */
  }

  .page-content-wrapper .card,
  .page-content-wrapper .product-card,
  .page-content-wrapper .category-card,
  .page-content-wrapper .dropdown-menu,
  .page-content-wrapper .list-group-item {
      background-color: #3a3a3c; /* Slightly lighter card background */
      border-color: #545458;
  }

    .page-content-wrapper .dropdown-item:hover,
    .page-content-wrapper .dropdown-item:focus {
        background-color: #545458;
    }
    .page-content-wrapper .form-control,
    .page-content-wrapper .form-select {
        background-color: #3a3a3c;
        color: #eaeaeb;
        border-color: #545458;
    }
    .page-content-wrapper .form-control::placeholder {
        color: #8e8e93;
    }
    .page-content-wrapper .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(255, 255, 255, 0.04);
    }
    .page-content-wrapper .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.06);
    }
    .page-content-wrapper .table-bordered,
    .page-content-wrapper .table-bordered th,
    .page-content-wrapper .table-bordered td {
        border-color: #545458;
    }
    .page-content-wrapper .btn-outline-primary {
        color: #64b5f6; /* Lighter blue for dark mode */
        border-color: #64b5f6;
    }
    .page-content-wrapper .btn-outline-primary:hover {
        color: #1c1c1e;
        background-color: #64b5f6;
        border-color: #64b5f6;
    }
    .page-content-wrapper .btn-outline-light {
        color: #eaeaeb;
        border-color: #eaeaeb;
    }
     .page-content-wrapper .btn-outline-light:hover {
        color: #1c1c1e;
        background-color: #eaeaeb;
        border-color: #eaeaeb;
    }
}

/* Class for the main content area */
.page-content-wrapper {
  background-color: #ffffff; /* White paper background */
  padding: 30px 40px; /* Increased horizontal padding */
  /* Set max-width and center horizontally */
  max-width: 1200px; /* Adjust as needed */
  margin-left: auto;
  margin-right: auto;
  /* More pronounced shadow */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-radius: 5px; /* Slightly less rounded corners */
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Remove the container-fluid max-width rule if it exists */
/*
.container-fluid.page-content-wrapper {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}
*/

/* Ensure the wrapper is applied correctly */
body > .page-content-wrapper {
    /* This selector might increase specificity if needed */
}