<?php
require_once '../config/database.php';

header('Content-Type: application/json');

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    $conn = connectDB();
    
    // Get search parameters
    $branch = isset($_GET['branch']) ? $_GET['branch'] : (isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch');
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';

    // Base query with joins
    $query = "SELECT p.*, c.name as category_name, b.name as brand_name
              FROM products p
              LEFT JOIN categories c ON p.category_id = c.id
              LEFT JOIN brands b ON p.brand_id = b.id
              WHERE p.branch_name = ? AND p.quantity > 0";

$params = [$branch];
    if (!empty($search)) {
        $search = "%{$search}%";
        $query .= " AND (p.name LIKE ? OR p.sku LIKE ? OR c.name LIKE ? OR b.name LIKE ?)";
        $params = array_merge([$branch], [$search, $search, $search, $search]);
        $types = "sssss";
    } else {
        $params = [$branch];
        $types = "s";
    }

    $query .= " ORDER BY p.name ASC LIMIT 50";

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

$products = [];
while ($row = $result->fetch_assoc()) {
    $products[] = [
        'id' => $row['id'],
        'name' => $row['name'],
        'sku' => $row['sku'],
        'quantity' => $row['quantity'],
        'category' => $row['category_name'],
        'brand' => $row['brand_name']
    ];
}

    echo json_encode($products);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        closeDB($conn);
    }
}