$(document).ready(function() {
    // Global variables
    let items = [];
    let totalAmount = 0;

    // Initialize Select2 for supplier dropdown
    $('#supplier_id').select2({
        placeholder: 'Select a supplier',
        allowClear: true
    });

    // Initialize datepicker for expected delivery date
    $('#expected_delivery_date').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true
    });

    // Add new item row
    $('#add_item').click(function() {
        const newRow = `
            <tr class="item-row">
                <td>
                    <select class="form-control product-select" name="product_id[]" required>
                        <option value="">Select Product</option>
                    </select>
                </td>
                <td><input type="number" class="form-control quantity" name="quantity[]" min="1" required></td>
                <td><input type="number" class="form-control unit-price" name="unit_price[]" step="0.01" required></td>
                <td><span class="total-price">0.00</span></td>
                <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
            </tr>
        `;
        $('#items_table tbody').append(newRow);
        initializeProductSelect();
    });

    // Remove item row
    $(document).on('click', '.remove-item', function() {
        $(this).closest('tr').remove();
        calculateTotal();
    });

    // Calculate line total when quantity or price changes
    $(document).on('change', '.quantity, .unit-price', function() {
        const row = $(this).closest('tr');
        calculateLineTotal(row);
        calculateTotal();
    });

    // Calculate line total
    function calculateLineTotal(row) {
        const quantity = parseFloat(row.find('.quantity').val()) || 0;
        const unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
        const total = quantity * unitPrice;
        row.find('.total-price').text(total.toFixed(2));
    }

    // Calculate grand total
    function calculateTotal() {
        let total = 0;
        $('.total-price').each(function() {
            total += parseFloat($(this).text()) || 0;
        });
        $('#total_amount').text(total.toFixed(2));
    }

    // Initialize product select with AJAX
    function initializeProductSelect() {
        $('.product-select').select2({
            placeholder: 'Select a product',
            allowClear: true,
            ajax: {
                url: 'ajax/get_products.php',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        search: params.term
                    };
                },
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
    }

    // Form validation before submit
    $('#purchase_order_form').submit(function(e) {
        if ($('.item-row').length === 0) {
            e.preventDefault();
            alert('Please add at least one item to the purchase order.');
            return false;
        }
        return true;
    });

    // Initialize first product select
    initializeProductSelect();

    // Handle status changes
    $('#status').change(function() {
        const status = $(this).val();
        if (status === 'sent') {
            if (!confirm('Are you sure you want to mark this PO as sent? This will send an email to the supplier.')) {
                $(this).val($(this).data('previous-value'));
                return;
            }
        }
        $(this).data('previous-value', status);
    });
});
$(document).ready(function() {
    // Initialize variables
    let selectedProducts = [];
    const productTable = $('#productTable').DataTable({
        pageLength: 10,
        order: [[0, 'asc']]
    });

    // Product search handling with validation
    $(document).on('input', '.product-search', function() {
        const searchInput = $(this);
        const searchResults = searchInput.siblings('.search-results');
        const row = searchInput.closest('tr');
        const query = searchInput.val().trim();
        
        // Clear previous values if search is emptied
        if (query.length === 0) {
            clearRowValues(row);
        }

        if (query.length < 2) {
            searchResults.addClass('d-none').empty();
            return;
        }

        $.ajax({
            url: 'ajax/search_products.php',
            method: 'GET',
            data: { query: query },
            success: function(data) {
                searchResults.empty();
                if (data.length > 0) {
                    data.forEach(function(product) {
                        searchResults.append(`
                            <div class="search-item" 
                                 data-id="${product.id}" 
                                 data-name="${product.name}"
                                 data-price="${product.price}">
                                ${product.name}
                            </div>
                        `);
                    });
                    searchResults.removeClass('d-none');
                } else {
                    searchResults.append(`
                        <div class="p-2 text-muted">
                            No products found
                        </div>
                    `);
                    searchResults.removeClass('d-none');
                    clearRowValues(row);
                }
            }
        });
    });

    // Handle product selection with automatic calculations
    $(document).on('click', '.search-item', function() {
        const item = $(this);
        const container = item.closest('.search-container');
        const searchInput = container.find('.product-search');
        const productIdInput = container.find('.product-id');
        const row = item.closest('tr');
        const unitPriceInput = row.find('.unit-price');
        const quantityInput = row.find('.quantity');
        const totalPriceSpan = row.find('.total-price');

        // Set the values
        searchInput.val(item.data('name'));
        productIdInput.val(item.data('id'));
        unitPriceInput.val(parseFloat(item.data('price')).toFixed(2));
        
        // Calculate total if quantity exists
        if (quantityInput.val()) {
            calculateLineTotal(row);
        }
        
        // Hide search results
        item.parent().addClass('d-none');
        
        // Update grand total
        calculateTotal();
    });

    // Calculate line total when quantity changes
    $(document).on('change', '.quantity', function() {
        const row = $(this).closest('tr');
        calculateLineTotal(row);
        calculateTotal();
    });

    // Helper function to clear row values
    function clearRowValues(row) {
        row.find('.product-id').val('');
        row.find('.unit-price').val('');
        row.find('.total-price').text('0.00');
        calculateTotal();
    }

    // Calculate line total
    function calculateLineTotal(row) {
        const quantity = parseFloat(row.find('.quantity').val()) || 0;
        const unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
        const total = quantity * unitPrice;
        row.find('.total-price').text(total.toFixed(2));
    }

    // Calculate grand total
    function calculateTotal() {
        let total = 0;
        $('.total-price').each(function() {
            total += parseFloat($(this).text()) || 0;
        });
        $('#total_amount').text(total.toFixed(2));
        $('input[name="total_amount"]').val(total.toFixed(2));
    }

    // Handle product selection
    $(document).on('click', '.search-item', function() {
        const item = $(this);
        const container = item.closest('.search-container');
        const searchInput = container.find('.product-search');
        const productIdInput = container.find('.product-id');
        const row = item.closest('tr');
        const unitPriceInput = row.find('.unit-price');
    
        searchInput.val(item.data('name'));
        productIdInput.val(item.data('id'));
        unitPriceInput.val(item.data('price'));
        
        // Update total
        const quantityInput = row.find('.quantity');
        if (quantityInput.val()) {
            calculateLineTotal(row);
            calculateTotal();
        }
    
        item.parent().addClass('d-none');
    });

    // Close search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.search-container').length) {
            $('.search-results').addClass('d-none');
        }
    });

    // Add product to purchase order
    $(document).on('click', '.add-product-btn', function() {
        const productId = $(this).data('id');
        const productName = $(this).data('name');
        const productPrice = parseFloat($(this).data('price')) || 0;
        const quantity = parseInt($('#quantity-' + productId).val()) || 1;

        // Check if product already exists in selection
        const existingProduct = selectedProducts.find(p => p.id === productId);
        if (existingProduct) {
            existingProduct.quantity += quantity;
            existingProduct.total = existingProduct.quantity * existingProduct.price;
        } else {
            selectedProducts.push({
                id: productId,
                name: productName,
                quantity: quantity,
                price: productPrice,
                total: quantity * productPrice
            });
        }

        updateSelectedProductsTable();
        calculateTotal();
    });

    // Remove product from selection
    $(document).on('click', '.remove-product', function() {
        const index = $(this).data('index');
        selectedProducts.splice(index, 1);
        updateSelectedProductsTable();
        calculateTotal();
    });

    // Update quantity
    $(document).on('change', '.product-quantity', function() {
        const index = $(this).data('index');
        const quantity = parseInt($(this).val()) || 0;
        
        if (quantity > 0) {
            selectedProducts[index].quantity = quantity;
            selectedProducts[index].total = quantity * selectedProducts[index].price;
            updateSelectedProductsTable();
            calculateTotal();
        }
    });

    // Update selected products table
    function updateSelectedProductsTable() {
        const tbody = $('#selectedProductsTable tbody');
        tbody.empty();

        if (selectedProducts.length === 0) {
            $('#noProductsSelected').show();
            $('#selectedProductsContainer').hide();
        } else {
            $('#noProductsSelected').hide();
            $('#selectedProductsContainer').show();

            selectedProducts.forEach((product, index) => {
                tbody.append(`
                    <tr>
                        <td>${product.name}
                            <input type="hidden" name="product_ids[]" value="${product.id}">
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm product-quantity" 
                                   name="quantities[]" value="${product.quantity}" 
                                   min="1" data-index="${index}">
                        </td>
                        <td class="text-end">
                            <input type="number" class="form-control form-control-sm product-price" 
                                   name="prices[]" value="${product.price.toFixed(2)}" 
                                   min="0.01" step="0.01" data-index="${index}">
                        </td>
                        <td class="text-end">${product.total.toFixed(2)}</td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm remove-product" data-index="${index}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `);
            });
        }
    }

    // Calculate total amount
    function calculateTotal() {
        const total = selectedProducts.reduce((sum, product) => sum + product.total, 0);
        $('#totalAmount').text(total.toFixed(2));
        $('input[name="total_amount"]').val(total.toFixed(2));
    }

    // Handle form submission
    $('#purchaseOrderForm').on('submit', function(e) {
        if (selectedProducts.length === 0) {
            e.preventDefault();
            alert('Please add at least one product to the purchase order.');
            return false;
        }

        if (!confirm('Are you sure you want to create this purchase order?')) {
            e.preventDefault();
            return false;
        }
    });

    // Handle supplier change
    $('#supplier_id').on('change', function() {
        const supplierId = $(this).val();
        if (supplierId) {
            $.ajax({
                url: 'ajax/get_supplier_details.php',
                method: 'GET',
                data: { supplier_id: supplierId },
                success: function(response) {
                    const supplier = JSON.parse(response);
                    $('#delivery_address').val(supplier.address);
                },
                error: function() {
                    alert('Error fetching supplier details');
                }
            });
        }
    });

    // Initialize date picker for expected delivery date
    $('#expected_delivery_date').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        startDate: new Date()
    });

    // Handle status changes
    $('#status').on('change', function() {
        const status = $(this).val();
        if (status === 'cancelled') {
            if (!confirm('Are you sure you want to cancel this purchase order?')) {
                $(this).val($(this).data('previous-status'));
                return;
            }
        }
        $(this).data('previous-status', status);
    });
});