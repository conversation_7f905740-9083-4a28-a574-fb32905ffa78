/* Shop Custom Styles */

/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    color: #212529;
}

/* Navbar Styling */
.navbar-shop {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand img {
    transition: transform 0.3s ease;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #2575fc 100%);
    padding: 4rem 0;
    margin-bottom: 3rem;
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.hero-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* Featured Categories */
.featured-categories {
    margin-bottom: 3rem;
}

.category-card {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.category-img-container {
    height: 150px;
    overflow: hidden;
    position: relative;
}

.category-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.category-card:hover .category-img {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
    padding: 1rem;
    color: white;
}

.category-title {
    font-weight: 600;
    margin-bottom: 0;
}

/* Product Cards */
.product-card {
    border: 1px solid #e9ecef;
    border-radius: 0.4rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
    transition: all 0.3s ease;
    height: 100%;
    margin-bottom: 1.5rem;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-img-container {
    padding: 0.5rem;
    background-color: #ffffff;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 0.4rem 0.4rem 0 0;
}

.product-img {
    height: 150px;
    object-fit: contain;
    transition: transform 0.5s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
}

.product-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.3rem;
    height: 2.2rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2575fc;
}

.product-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.product-btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.product-btn:hover {
    transform: translateY(-2px);
}

/* Product Details Page */
.product-detail-img {
    max-height: 400px;
    object-fit: contain;
    transition: transform 0.5s ease;
}

.product-detail-img:hover {
    transform: scale(1.03);
}

.product-detail-title {
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.product-detail-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2575fc;
    margin-bottom: 1.5rem;
}

.product-detail-meta {
    margin-bottom: 1.5rem;
}

.product-detail-meta span {
    display: inline-block;
    margin-right: 1.5rem;
    color: #6c757d;
}

.product-detail-meta i {
    margin-right: 0.5rem;
    color: #2575fc;
}

.product-detail-description {
    margin-bottom: 2rem;
    line-height: 1.7;
}

.quantity-selector {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f1f3f5;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background-color: #e9ecef;
}

.quantity-input {
    width: 60px;
    height: 40px;
    text-align: center;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin: 0 0.5rem;
}

/* Cart Page */
.cart-item {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.cart-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.cart-item-img {
    height: 100px;
    object-fit: contain;
}

.cart-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cart-item-price {
    font-weight: 700;
    color: #2575fc;
}

.cart-quantity-input {
    width: 60px;
    text-align: center;
}

.cart-summary {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.cart-summary-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.cart-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.cart-summary-total {
    font-weight: 700;
    font-size: 1.2rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

/* Footer */
.footer {
    background-color: #212529;
    color: #f8f9fa;
    padding: 3rem 0 1.5rem;
}

.footer-logo {
    margin-bottom: 1.5rem;
}

.footer-about {
    margin-bottom: 2rem;
}

.footer-heading {
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.footer-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: #2575fc;
}

.footer-links {
    list-style: none;
    padding-left: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #2575fc;
}

.footer-contact p {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.footer-contact i {
    margin-right: 0.75rem;
    color: #2575fc;
    width: 20px;
}

.footer-social {
    margin-top: 1.5rem;
}

.footer-social a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background-color: #2575fc;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    margin-top: 3rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: #adb5bd;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .product-card {
        margin-bottom: 1.5rem;
    }
    
    .cart-item {
        text-align: center;
    }
    
    .cart-item-img {
        margin-bottom: 1rem;
    }
}