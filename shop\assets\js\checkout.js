document.addEventListener('DOMContentLoaded', function() {
    const shippingSelect = document.getElementById('shipping_courier');
    const subtotalElement = document.querySelector('[data-subtotal]');
    const shippingElement = document.querySelector('[data-shipping]');
    const totalElement = document.querySelector('[data-total]');
    const weightElement = document.querySelector('[data-weight]');

    function updateShippingCost() {
        const selectedCourier = shippingSelect.value;
        if (!selectedCourier) return;

        const weight = parseFloat(weightElement.getAttribute('data-weight'));
        const subtotal = parseFloat(subtotalElement.getAttribute('data-subtotal'));

        // Get shipping options from the select element's options
        const selectedOption = shippingSelect.options[shippingSelect.selectedIndex];
        const baseRate = parseFloat(selectedOption.getAttribute('data-base-rate'));
        const ratePerKg = parseFloat(selectedOption.getAttribute('data-rate-per-kg'));

        // Calculate shipping cost
        const shippingCost = baseRate + (ratePerKg * weight);
        const total = subtotal + shippingCost;

        // Update display
        shippingElement.textContent = '₱' + shippingCost.toFixed(2);
        totalElement.textContent = '₱' + total.toFixed(2);
    }

    // Add event listener for shipping courier selection
    if (shippingSelect) {
        shippingSelect.addEventListener('change', updateShippingCost);
        // Trigger initial update if a courier is already selected
        updateShippingCost();
    }
});