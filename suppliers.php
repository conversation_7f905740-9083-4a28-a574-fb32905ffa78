
<?php
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$sort_by = 'name';
$sort_order = 'ASC';
$page = 1;
$items_per_page = 10;

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Build query for suppliers
$query = "SELECT * FROM suppliers WHERE 1=1";
$count_query = "SELECT COUNT(*) as total FROM suppliers WHERE 1=1";

// Add search condition
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (name LIKE '%" . $conn->real_escape_string($search) . "%' 
               OR contact_person LIKE '%" . $conn->real_escape_string($search) . "%'
               OR email LIKE '%" . $conn->real_escape_string($search) . "%'
               OR phone LIKE '%" . $conn->real_escape_string($search) . "%')";
    $count_query .= " AND (name LIKE '%" . $conn->real_escape_string($search) . "%' 
                    OR contact_person LIKE '%" . $conn->real_escape_string($search) . "%'
                    OR email LIKE '%" . $conn->real_escape_string($search) . "%'
                    OR phone LIKE '%" . $conn->real_escape_string($search) . "%')";
}

// Get total count for pagination
$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Add sorting
$query .= " ORDER BY " . $conn->real_escape_string($sort_by) . " " . $conn->real_escape_string($sort_order);

// Add pagination
$offset = ($page - 1) * $items_per_page;
$query .= " LIMIT $items_per_page OFFSET $offset";

// Execute query
$result = $conn->query($query);

// Process form submission for adding/editing supplier
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_supplier'])) {
    $supplier_id = isset($_POST['supplier_id']) ? $_POST['supplier_id'] : '';
    $name = trim($_POST['name']);
    $contact_person = trim($_POST['contact_person']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Supplier name is required";
    }
    
    // Validate email if provided
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    // If no errors, save supplier
    if (empty($errors)) {
        if (!empty($supplier_id)) {
            // Update existing supplier
            $query = "UPDATE suppliers SET 
                      name = ?, 
                      contact_person = ?, 
                      email = ?, 
                      phone = ?, 
                      address = ? 
                      WHERE id = ?";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssssi", $name, $contact_person, $email, $phone, $address, $supplier_id);
            
            if ($stmt->execute()) {
                $success_message = "Supplier updated successfully";
            } else {
                $errors[] = "Error updating supplier: " . $conn->error;
            }
        } else {
            // Insert new supplier
            $query = "INSERT INTO suppliers (name, contact_person, email, phone, address) 
                      VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssss", $name, $contact_person, $email, $phone, $address);
            
            if ($stmt->execute()) {
                $success_message = "Supplier added successfully";
            } else {
                $errors[] = "Error adding supplier: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_supplier'])) {
    $supplier_id = $_POST['supplier_id'];
    
    // Delete supplier
    $delete_query = "DELETE FROM suppliers WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $supplier_id);
    
    if ($stmt->execute()) {
        // Set a flag instead of redirecting
        $deleted = true;
    } else {
        $errors[] = "Error deleting supplier: " . $conn->error;
    }
    
    $stmt->close();
}

// Close connection
closeDB($conn);

// Now include the header after all potential redirects
require_once 'includes/header.php';

// Check if we need to show deleted message
$show_deleted_message = isset($deleted) && $deleted || isset($_GET['deleted']) && $_GET['deleted'] == 1;
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Suppliers Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
            <i class="fas fa-plus me-2"></i> Add New Supplier
        </button>
    </div>
    
    <?php if ($show_deleted_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            Supplier has been deleted successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header bg-light">
            <form method="get" action="suppliers.php" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="Search suppliers..." value="<?php echo htmlspecialchars($search); ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="suppliers.php" class="btn btn-outline-secondary">
                        <i class="fas fa-sync-alt me-2"></i> Reset
                    </a>
                </div>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>
                                <a href="suppliers.php?sort_by=name&sort_order=<?php echo ($sort_by == 'name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>&search=<?php echo urlencode($search); ?>" class="text-decoration-none text-dark">
                                    Name
                                    <?php if ($sort_by == 'name'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>Contact Person</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Address</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($supplier = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($supplier['name']); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['contact_person'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['email'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['phone'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['address'] ?? ''); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary edit-supplier" 
                                                data-bs-toggle="modal" data-bs-target="#editSupplierModal"
                                                data-id="<?php echo $supplier['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($supplier['name']); ?>"
                                                data-contact="<?php echo htmlspecialchars($supplier['contact_person'] ?? ''); ?>"
                                                data-email="<?php echo htmlspecialchars($supplier['email'] ?? ''); ?>"
                                                data-phone="<?php echo htmlspecialchars($supplier['phone'] ?? ''); ?>"
                                                data-address="<?php echo htmlspecialchars($supplier['address'] ?? ''); ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger delete-supplier"
                                                data-bs-toggle="modal" data-bs-target="#deleteSupplierModal"
                                                data-id="<?php echo $supplier['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($supplier['name']); ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <p class="mb-0 text-muted">No suppliers found</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="suppliers.php?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                                Previous
                            </a>
                        </li>
                        
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="suppliers.php?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="suppliers.php?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Supplier Modal -->
<div class="modal fade" id="addSupplierModal" tabindex="-1" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="suppliers.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSupplierModalLabel">Add New Supplier</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Supplier Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact_person" class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_supplier" class="btn btn-primary">Save Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Supplier Modal -->
<div class="modal fade" id="editSupplierModal" tabindex="-1" aria-labelledby="editSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="suppliers.php">
                <input type="hidden" name="supplier_id" id="edit_supplier_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSupplierModalLabel">Edit Supplier</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Supplier Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_contact_person" class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="edit_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">Address</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_supplier" class="btn btn-primary">Update Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Supplier Modal -->
<div class="modal fade" id="deleteSupplierModal" tabindex="-1" aria-labelledby="deleteSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="suppliers.php">
                <input type="hidden" name="supplier_id" id="delete_supplier_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteSupplierModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the supplier: <strong id="delete_supplier_name"></strong>?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_supplier" class="btn btn-danger">Delete Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Edit supplier modal data
document.addEventListener('DOMContentLoaded', function() {
    const editButtons = document.querySelectorAll('.edit-supplier');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const contact = this.getAttribute('data-contact');
            const email = this.getAttribute('data-email');
            const phone = this.getAttribute('data-phone');
            const address = this.getAttribute('data-address');
            
            document.getElementById('edit_supplier_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_contact_person').value = contact;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_phone').value = phone;
            document.getElementById('edit_address').value = address;
        });
    });
    
    // Delete supplier modal data
    const deleteButtons = document.querySelectorAll('.delete-supplier');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            document.getElementById('delete_supplier_id').value = id;
            document.getElementById('delete_supplier_name').textContent = name;
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>