.product-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.product-list-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    background-color: #fff;
    position: relative;
    gap: 1rem;
}

.product-list-item:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-1px);
}

.product-list-item.out-of-stock {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

.product-list-item.low-stock {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
}

.product-list-item.in-stock {
    background-color: rgba(25, 135, 84, 0.1);
    border-color: #198754;
}

.product-image-container {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.product-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.product-info {
    flex-grow: 1;
    min-width: 0;
}

.product-name {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: #212529;
}

.product-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.product-sku,
.product-brand,
.product-price {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.product-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
}

.stock-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
    .product-list-item {
        flex-direction: column;
        text-align: center;
    }

    .product-actions {
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .product-details {
        justify-content: center;
    }
}