<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Check if branch name is provided
if (isset($_POST['branch_name']) && !empty($_POST['branch_name'])) {
    // Update session with new branch name
    $_SESSION['branch_name'] = $_POST['branch_name'];
    
    // Log the branch change
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    
    $conn = connectDB();
    logActivity($conn, 'branch_change', 'Changed branch to ' . $_SESSION['branch_name']);
    $conn->close();
    
    // Set success message
    $_SESSION['success_message'] = "Branch changed to " . $_SESSION['branch_name'];
}

// Redirect back to the previous page or to sales page
$redirect_url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'sales.php';
header("Location: " . $redirect_url);
exit();