<?php
// Remove session_start() as it's already in header.php
require_once 'config/database.php';

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: products.php?error=invalid_id");
    exit();
}

// Include header after authorization check
require_once 'includes/header.php';

$product_id = $_GET['id'];

// Get database connection
$conn = connectDB();

// Get product details
$query = "SELECT p.*, c.name as category_name, b.name as brand_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          LEFT JOIN brands b ON p.brand_id = b.id 
          WHERE p.id = ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $product_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    // Product not found
    closeDB($conn);
    header("Location: products.php?error=product_not_found");
    exit();
}

$product = $result->fetch_assoc();
$stmt->close();

// Close connection
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Product Details</h1>
        <div>
            <a href="products.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i> Back to Products
            </a>
            <a href="product_form.php?id=<?php echo $product_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i> Edit Product
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <div class="row">
                <!-- Product Image -->
                <div class="col-md-4 text-center mb-4 mb-md-0">
                    <?php if (!empty($product['image_url'])): ?>
                        <img src="uploads/products/<?php echo $product['image_url']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-fluid rounded" style="max-height: 300px;">
                    <?php else: ?>
                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 300px;">
                            <i class="fas fa-box fa-5x text-secondary"></i>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Product Details -->
                <div class="col-md-8">
                    <h3 class="mb-3"><?php echo htmlspecialchars($product['name']); ?></h3>
                    
                    <div class="mb-4">
                        <?php if ($product['quantity'] == 0): ?>
                            <span class="badge bg-danger">Out of Stock</span>
                        <?php elseif ($product['quantity'] <= $product['reorder_level']): ?>
                            <span class="badge bg-warning text-dark">Low Stock</span>
                        <?php else: ?>
                            <span class="badge bg-success">In Stock</span>
                        <?php endif; ?>
                        
                        <span class="badge bg-info ms-2">SKU: <?php echo htmlspecialchars($product['sku']); ?></span>
                        
                        <?php if (!empty($product['category_name'])): ?>
                            <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($product['category_name']); ?></span>
                        <?php endif; ?>
                        
                        <?php if (!empty($product['brand_name'])): ?>
                            <span class="badge bg-info ms-2"><?php echo htmlspecialchars($product['brand_name']); ?></span>
                        <?php endif; ?>
                        
                        <?php if (!empty($product['branch_name'])): ?>
                            <span class="badge bg-dark ms-2"><?php echo htmlspecialchars($product['branch_name']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Price</h5>
                            <p class="h3 text-primary">₱<?php echo number_format($product['unit_price'], 2); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>Quantity in Stock</h5>
                            <p class="h3 <?php echo ($product['quantity'] <= $product['reorder_level']) ? 'text-warning' : 'text-success'; ?>">
                                <?php echo $product['quantity']; ?>
                            </p>
                        </div>
                    </div>
                    
                    <?php if (!empty($product['description'])): ?>
                        <div class="mb-4">
                            <h5>Description</h5>
                            <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Reorder Level</h5>
                            <p><?php echo $product['reorder_level']; ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>Last Updated</h5>
                            <p><?php echo date('F j, Y, g:i a', strtotime($product['updated_at'])); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>