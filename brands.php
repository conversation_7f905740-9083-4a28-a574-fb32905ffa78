<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Process form submission for adding/editing brand
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_brand'])) {
    $brand_id = isset($_POST['brand_id']) ? $_POST['brand_id'] : '';
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Brand name is required";
    }
    
    // Handle logo upload
    $logo_path = '';
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['logo']['type'], $allowed_types)) {
            $errors[] = "Invalid file type. Only JPG, PNG and GIF are allowed.";
        } elseif ($_FILES['logo']['size'] > $max_size) {
            $errors[] = "File is too large. Maximum size is 2MB.";
        } else {
            // Create uploads directory if it doesn't exist
            $upload_dir = 'uploads/brands/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            // Generate unique filename
            $file_ext = pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION);
            $filename = uniqid('brand_') . '.' . $file_ext;
            $logo_path = $upload_dir . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($_FILES['logo']['tmp_name'], $logo_path)) {
                $errors[] = "Error uploading file.";
                $logo_path = '';
            }
        }
    }
    
    // Check if brand name already exists (for new brands or if name changed)
    $name_check_query = "SELECT id FROM brands WHERE name = ? AND id != ?";
    $stmt = $conn->prepare($name_check_query);
    $stmt->bind_param("si", $name, $brand_id);
    $stmt->execute();
    $name_result = $stmt->get_result();
    
    if ($name_result->num_rows > 0) {
        $errors[] = "Brand name already exists. Please use a different name.";
    }
    
    $stmt->close();
    
    // If no errors, save brand
    if (empty($errors)) {
        if (!empty($brand_id)) {
            // Update existing brand
            $query = "UPDATE brands SET name = ?, description = ?";
            $types = "ss";
            $params = array($types);
            $params[] = &$name;
            $params[] = &$description;
            
            // Add logo path to update if new logo was uploaded
            if (!empty($logo_path)) {
                $query .= ", logo_path = ?";
                $types .= "s";
                $params[0] = $types;
                $params[] = &$logo_path;
            }
            
            $query .= " WHERE id = ?";
            $types .= "i";
            $params[0] = $types;
            $params[] = &$brand_id;
            
            $stmt = $conn->prepare($query);
            call_user_func_array(array($stmt, 'bind_param'), $params);
            
            if ($stmt->execute()) {
                $success_message = "Brand updated successfully";
            } else {
                $errors[] = "Error updating brand: " . $conn->error;
            }
        } else {
            // Insert new brand
            $query = "INSERT INTO brands (name, description, logo_path) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sss", $name, $description, $logo_path);
            
            if ($stmt->execute()) {
                $success_message = "Brand added successfully";
            } else {
                $errors[] = "Error adding brand: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_brand'])) {
    $brand_id = $_POST['brand_id'];
    
    // Check if brand is used in products
    $check_query = "SELECT COUNT(*) as count FROM products WHERE brand_id = ?";
    $stmt = $conn->prepare($check_query);
    $stmt->bind_param("i", $brand_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    if ($count > 0) {
        $errors[] = "Cannot delete brand because it is used by $count product(s). Please reassign these products to another brand first.";
    } else {
        // Delete brand
        $delete_query = "DELETE FROM brands WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $brand_id);
        
        if ($stmt->execute()) {
            $success_message = "Brand deleted successfully";
        } else {
            $errors[] = "Error deleting brand: " . $conn->error;
        }
        
        $stmt->close();
    }
}

// Pagination variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

// Initialize search variable
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM brands";

// Add search condition if search term exists
if (!empty($search)) {
    $count_query .= " WHERE name LIKE '%" . $conn->real_escape_string($search) . "%'";
}

$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get brands with pagination
$query = "SELECT b.*, COUNT(p.id) as product_count 
          FROM brands b 
          LEFT JOIN products p ON b.id = p.brand_id";

// Add search condition if search term exists
if (!empty($search)) {
    $query .= " WHERE b.name LIKE '%" . $conn->real_escape_string($search) . "%'";
}

$query .= " GROUP BY b.id 
            ORDER BY b.name ASC 
            LIMIT $items_per_page OFFSET $offset";
$result = $conn->query($query);

// Close connection
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Brands</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBrandModal">
            <i class="fas fa-plus me-2"></i> Add New Brand
        </button>
    </div>
    
    <!-- Add Search Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="" class="row g-3 align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-filter"></i></span>
                        <input type="text" class="form-control" name="search" placeholder="Filter brands..." value="<?php echo htmlspecialchars($search); ?>" 
                               oninput="this.form.submit()">
                    </div>
                </div>
                <?php if (!empty($search)): ?>
                    <div class="col-auto">
                        <a href="brands.php" class="btn btn-secondary">Clear</a>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>Logo</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Products</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result && $result->num_rows > 0): ?>
                            <?php while ($brand = $result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($brand['logo_path'])): ?>
                                            <img src="<?php echo htmlspecialchars($brand['logo_path']); ?>" 
                                                     alt="<?php echo htmlspecialchars($brand['name']); ?>" 
                                                     style="max-width: 50px; height: auto;">
                                        <?php else: ?>
                                            <span class="text-muted">No logo</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($brand['name']); ?></td>
                                    <td>
                                        <?php 
                                        if (!empty($brand['description'])) {
                                            echo (strlen($brand['description']) > 100) 
                                                ? htmlspecialchars(substr($brand['description'], 0, 100)) . '...' 
                                                : htmlspecialchars($brand['description']);
                                        } else {
                                            echo '<span class="text-muted">No description</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <a href="products.php?brand=<?php echo $brand['id']; ?>">
                                            <?php echo $brand['product_count']; ?> product(s)
                                        </a>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($brand['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#editBrandModal" 
                                                    data-id="<?php echo $brand['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($brand['name']); ?>"
                                                    data-description="<?php echo htmlspecialchars($brand['description']); ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteBrandModal"
                                                    data-id="<?php echo $brand['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($brand['name']); ?>"
                                                    data-count="<?php echo $brand['product_count']; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <h5>No brands found</h5>
                                        <p class="text-muted">Start by adding a new brand</p>
                                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addBrandModal">
                                            <i class="fas fa-plus me-2"></i> Add New Brand
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        <?php
                        // Calculate the range of page numbers to display (max 7 pages)
                        $max_visible_pages = 7;
                        $start_page = max(1, $page - floor($max_visible_pages/2));
                        $end_page = min($total_pages, $start_page + $max_visible_pages - 1);
                        
                        // Adjust start_page if we're near the end
                        if ($end_page - $start_page + 1 < $max_visible_pages) {
                            $start_page = max(1, $end_page - $max_visible_pages + 1);
                        }
                        
                        // Show first page with ellipsis if needed
                        if ($start_page > 1) {
                            echo '<li class="page-item"><a class="page-link" href="?page=1">1</a></li>';
                            if ($start_page > 2) {
                                echo '<li class="page-item disabled"><a class="page-link">...</a></li>';
                            }
                        }
                        
                        // Show the page numbers in the calculated range
                        for ($i = $start_page; $i <= $end_page; $i++) {
                            echo '<li class="page-item ' . (($page == $i) ? 'active' : '') . '">';
                            echo '<a class="page-link" href="?page=' . $i . '">' . $i . '</a>';
                            echo '</li>';
                        }
                        
                        // Show last page with ellipsis if needed
                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<li class="page-item disabled"><a class="page-link">...</a></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="?page=' . $total_pages . '">' . $total_pages . '</a></li>';
                        }
                        ?>
                        
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Brand Modal -->
<div class="modal fade" id="addBrandModal" tabindex="-1" aria-labelledby="addBrandModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="brands.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="addBrandModalLabel">Add New Brand</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Brand Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_brand" class="btn btn-primary">Save Brand</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Brand Modal -->
<div class="modal fade" id="editBrandModal" tabindex="-1" aria-labelledby="editBrandModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="brands.php" enctype="multipart/form-data">
                <input type="hidden" name="brand_id" id="edit_brand_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editBrandModalLabel">Edit Brand</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Brand Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_logo" class="form-label">Brand Logo</label>
                        <input type="file" class="form-control" id="edit_logo" name="logo" accept="image/*">
                        <div class="form-text">Supported formats: JPG, PNG, GIF. Max size: 2MB</div>
                        <div id="current_logo" class="mt-2"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_brand" class="btn btn-primary">Update Brand</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Brand Modal -->
<div class="modal fade" id="deleteBrandModal" tabindex="-1" aria-labelledby="deleteBrandModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="brands.php">
                <input type="hidden" name="brand_id" id="delete_brand_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteBrandModalLabel">Delete Brand</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the brand <strong id="delete_brand_name"></strong>?</p>
                    <div id="delete_warning" class="alert alert-warning d-none">
                        This brand is currently assigned to <span id="product_count"></span> product(s). Deleting it may affect these products.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_brand" class="btn btn-danger">Delete Brand</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Edit Brand Modal
document.addEventListener('DOMContentLoaded', function() {
    const editBrandModal = document.getElementById('editBrandModal');
    if (editBrandModal) {
        editBrandModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const id = button.getAttribute('data-id');
            const name = button.getAttribute('data-name');
            const description = button.getAttribute('data-description');
            
            const modal = this;
            modal.querySelector('#edit_brand_id').value = id;
            modal.querySelector('#edit_name').value = name;
            modal.querySelector('#edit_description').value = description;
        });
    }
    
    // Delete Brand Modal
    const deleteBrandModal = document.getElementById('deleteBrandModal');
    if (deleteBrandModal) {
        deleteBrandModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const id = button.getAttribute('data-id');
            const name = button.getAttribute('data-name');
            const count = button.getAttribute('data-count');
            
            const modal = this;
            modal.querySelector('#delete_brand_id').value = id;
            modal.querySelector('#delete_brand_name').textContent = name;
            
            const warningElement = modal.querySelector('#delete_warning');
            const countElement = modal.querySelector('#product_count');
            
            if (count && parseInt(count) > 0) {
                countElement.textContent = count;
                warningElement.classList.remove('d-none');
            } else {
                warningElement.classList.add('d-none');
            }
        });
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>