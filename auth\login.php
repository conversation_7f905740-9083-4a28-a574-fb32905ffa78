<?php
session_start();
require_once '../config/database.php';

$error = '';
$success = '';
$auth_step = isset($_SESSION['auth_step']) ? $_SESSION['auth_step'] : 'identify';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: ../dashboard.php");
    exit();
}

// Process authentication requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Step 1: Email identification
    if (isset($_POST['action']) && $_POST['action'] == 'identify') {
        $email = filter_var(trim($_POST['email']), FILTER_SANITIZE_EMAIL);
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Please enter a valid email address";
        } else {
            $conn = connectDB();
            $stmt = $conn->prepare("SELECT id, email, full_name, role, admin_approved FROM users WHERE email = ?");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows == 1) {
                $user = $result->fetch_assoc();
                
                if ($user['admin_approved'] == 'No') {
                    $error = "Your account is pending approval";
                } else {
                    // Generate 6-digit OTP
                    $otp = sprintf("%06d", mt_rand(1, 999999));
                    $otp_expiry = date('Y-m-d H:i:s', strtotime('+10 minutes'));
                    
                    // Store OTP in database
                    $stmt = $conn->prepare("UPDATE users SET otp = ?, otp_expiry = ? WHERE id = ?");
                    $stmt->bind_param("ssi", $otp, $otp_expiry, $user['id']);
                    $stmt->execute();
                    
                    // Store user data in session
                    $_SESSION['auth_email'] = $email;
                    $_SESSION['auth_name'] = $user['full_name'];
                    $_SESSION['auth_step'] = 'verify';
                    
                    // Send OTP via email
                    require_once '../includes/mail_functions.php';
                    $email_sent = send_otp_email($email, $otp, $user['full_name']);
                    
                    if (!$email_sent) {
                        // For development purposes only
                        $_SESSION['dev_otp'] = $otp;
                    }
                    
                    // Redirect to prevent form resubmission
                    header("Location: login.php");
                    exit();
                }
            } else {
                $error = "No account found with this email";
            }
            
            $stmt->close();
            closeDB($conn);
        }
    }
    
    // Step 2: OTP verification
    if (isset($_POST['action']) && $_POST['action'] == 'verify') {
        $entered_otp = trim($_POST['otp']);
        $email = $_SESSION['auth_email'];
        
        if (strlen($entered_otp) != 6 || !ctype_digit($entered_otp)) {
            $error = "Please enter a valid 6-digit code";
        } else {
            $conn = connectDB();
            $stmt = $conn->prepare("SELECT id, username, full_name, role, otp, otp_expiry, profile_image FROM users WHERE email = ?");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows == 1) {
                $user = $result->fetch_assoc();
                $current_time = date('Y-m-d H:i:s');
                
                if ($user['otp'] == $entered_otp && $current_time <= $user['otp_expiry']) {
                    // OTP is valid - authenticate user
                    session_regenerate_id(true);
                    
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['profile_image'] = $user['profile_image'];
                    
                    // Clear auth session variables
                    unset($_SESSION['auth_step']);
                    unset($_SESSION['auth_email']);
                    unset($_SESSION['auth_name']);
                    unset($_SESSION['dev_otp']);
                    
                    // Reset OTP in database
                    $null_value = null;
                    $stmt = $conn->prepare("UPDATE users SET otp = ?, otp_expiry = ? WHERE id = ?");
                    $stmt->bind_param("ssi", $null_value, $null_value, $user['id']);
                    $stmt->execute();
                    
                    // Redirect based on role
                    $redirect = ($user['role'] == 'staff') ? '../sales.php' : '../dashboard.php';
                    header("Location: $redirect");
                    exit();
                } elseif ($current_time > $user['otp_expiry']) {
                    $error = "Verification code has expired";
                    $_SESSION['auth_step'] = 'identify'; // Reset to first step
                } else {
                    $error = "Invalid verification code";
                }
            } else {
                $error = "Authentication failed";
                $_SESSION['auth_step'] = 'identify';
            }
            
            $stmt->close();
            closeDB($conn);
        }
    }
    
    // Registration request
    if (isset($_POST['action']) && $_POST['action'] == 'register') {
        $full_name = trim($_POST['full_name']);
        $email = filter_var(trim($_POST['email']), FILTER_SANITIZE_EMAIL);
        
        if (empty($full_name) || strlen($full_name) < 3) {
            $error = "Please enter your full name (at least 3 characters)";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Please enter a valid email address";
        } else {
            $conn = connectDB();
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $error = "Email already registered";
            } else {
                // Generate username and temporary password
                $username = strtolower(explode('@', $email)[0]) . rand(100, 999);
                $temp_password = bin2hex(random_bytes(4));
                $hashed_password = password_hash($temp_password, PASSWORD_DEFAULT);
                
                // Insert new user
                $stmt = $conn->prepare("INSERT INTO users (username, password, full_name, email, role, admin_approved) VALUES (?, ?, ?, ?, 'staff', 'No')");
                $stmt->bind_param("ssss", $username, $hashed_password, $full_name, $email);
                
                if ($stmt->execute()) {
                    $success = "Registration successful! Your account is pending approval. Your temporary credentials are: Username: $username, Password: $temp_password";
                    $auth_step = 'identify';
                } else {
                    $error = "Registration failed: " . $conn->error;
                }
            }
            
            $stmt->close();
            closeDB($conn);
        }
    }
}

// Reset authentication flow
if (isset($_GET['reset'])) {
    unset($_SESSION['auth_step']);
    unset($_SESSION['auth_email']);
    unset($_SESSION['auth_name']);
    unset($_SESSION['dev_otp']);
    header("Location: login.php");
    exit();
}

// Show registration form
if (isset($_GET['register'])) {
    $auth_step = 'register';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InvenTech | Secure Login</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #f8fafc;
            --dark: #1e293b;
            --light: #f1f5f9;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #0ea5e9;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .auth-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .auth-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        }
        
        .auth-sidebar {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 3rem 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .auth-sidebar-content {
            margin-top: 2rem;
        }
        
        .auth-form {
            padding: 3rem 2rem;
            background: white;
        }
        
        .auth-logo {
            width: 50px;
            height: 50px;
            background-color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .auth-logo img {
            width: 35px;
            height: 35px;
        }
        
        .auth-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .auth-subtitle {
            color: #94a3b8;
            margin-bottom: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-floating > .form-control {
            padding: 1rem 0.75rem;
            height: calc(3.5rem + 2px);
            line-height: 1.25;
        }
        
        .form-floating > label {
            padding: 1rem 0.75rem;
        }
        
        .btn-auth {
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-auth:hover {
            background-color: var(--primary-dark);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
        }
        
        .btn-auth-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-auth-outline:hover {
            background-color: rgba(99, 102, 241, 0.05);
            color: var(--primary-dark);
        }
        
        .auth-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .auth-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }
        
        .otp-inputs {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .otp-input {
            width: 50px;
            height: 60px;
            font-size: 1.5rem;
            text-align: center;
            border: 1px solid #cbd5e1;
            border-radius: 8px;
            background-color: #f8fafc;
        }
        
        .otp-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
            outline: none;
        }
        
        .material-icons-round {
            vertical-align: middle;
            font-size: 1.25rem;
        }
        
        .alert {
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: none;
        }
        
        .alert-danger {
            background-color: #fef2f2;
            color: #b91c1c;
        }
        
        .alert-success {
            background-color: #f0fdf4;
            color: #166534;
        }
        
        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
        }
        
        @media (max-width: 767.98px) {
            .auth-sidebar {
                display: none;
            }
            
            .auth-form {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="auth-container">
            <div class="row g-0 auth-card">
                <!-- Sidebar -->
                <div class="col-md-5 auth-sidebar">
                    <div>
                        <div class="auth-logo">
                            <img src="../logo.png" alt="Musar Logo" style="width: 50px; height: 50px; display: block; margin: 0 auto;">
                        </div>
                        <h3 class="fw-bold">StockMaster</h3>
                        <p class="mb-0 opacity-75">Musar Stock Management System</p>
                    </div>
                    
                    <div class="auth-sidebar-content">
                        <h4 class="mb-4">Streamline your inventory management</h4>
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <span class="material-icons-round me-2">check_circle</span>
                                Real-time stock tracking
                            </li>
                            <li class="mb-3">
                                <span class="material-icons-round me-2">check_circle</span>
                                Secure authentication
                            </li>
                            <li class="mb-3">
                                <span class="material-icons-round me-2">check_circle</span>
                                Comprehensive reporting
                            </li>
                        </ul>
                    </div>
                    
                    <div class="mt-auto">
                        <div class="d-flex align-items-center">
                            <p class="mb-0 me-2">&copy; <?php echo date('Y'); ?> StockMaster. All rights reserved.</p>
                            <img src="../franzsys.png" alt="FranzSys Logo" style="height: 30px;">
                        </div>
                    </div>
                </div>
                
                <!-- Auth Form -->
                <div class="col-md-7 auth-form">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <span class="material-icons-round me-2">error_outline</span>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <span class="material-icons-round me-2">check_circle</span>
                            <?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_SESSION['dev_otp'])): ?>
                        <div class="alert alert-info">
                            <span class="material-icons-round me-2">info</span>
                            Development mode: Your OTP is <?php echo $_SESSION['dev_otp']; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($auth_step == 'identify'): ?>
                        <!-- Email Identification Form -->
                        <h2 class="auth-title">Welcome back</h2>
                        <p class="auth-subtitle">Enter your email to continue</p>
                        
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="identifyForm">
                            <div class="form-floating mb-4">
                                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required autofocus>
                                <label for="email">
                                    <span class="material-icons-round me-1">email</span>
                                    Email address
                                </label>
                            </div>
                            
                            <input type="hidden" name="action" value="identify">
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-auth">
                                    <span class="material-icons-round me-1">login</span>
                                    Continue
                                </button>
                            </div>
                            
                            <div class="text-center mt-4">
                                <p>Don't have an account? <a href="login.php?register=1" class="auth-link">Register</a></p>
                            </div>
                        </form>
                    
                    <?php elseif ($auth_step == 'verify'): ?>
                        <!-- OTP Verification Form -->
                        <h2 class="auth-title">Verification required</h2>
                        <p class="auth-subtitle">
                            Enter the 6-digit code sent to 
                            <strong><?php echo isset($_SESSION['auth_email']) ? $_SESSION['auth_email'] : ''; ?></strong>
                        </p>
                        
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="verifyForm">
                            <div class="text-center">
                                <div class="otp-inputs">
                                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" required>
                                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" required>
                                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" required>
                                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" required>
                                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" required>
                                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" required>
                                </div>
                                <input type="hidden" id="otp" name="otp">
                                <p class="text-muted small">
                                    <span class="material-icons-round me-1" style="font-size: 14px;">schedule</span>
                                    Code expires in 10 minutes
                                </p>
                            </div>
                            
                            <input type="hidden" name="action" value="verify">
                            
                            <div class="d-grid gap-2 mb-3">
                                <button type="submit" class="btn btn-auth">
                                    <span class="material-icons-round me-1">verified_user</span>
                                    Verify & Login
                                </button>
                            </div>
                            
                            <div class="text-center">
                                <a href="login.php?reset=1" class="auth-link">
                                    <span class="material-icons-round me-1">refresh</span>
                                    Restart authentication
                                </a>
                            </div>
                        </form>
                    
                    <?php elseif ($auth_step == 'register'): ?>
                        <!-- Registration Form -->
                        <h2 class="auth-title">Create an account</h2>
                        <p class="auth-subtitle">Fill in your details to register</p>
                        
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="registerForm">
                            <div class="form-floating mb-4">
                                <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Your full name" required autofocus>
                                <label for="full_name">
                                    <span class="material-icons-round me-1">person</span>
                                    Full Name
                                </label>
                            </div>
                            
                            <div class="form-floating mb-4">
                                <input type="email" class="form-control" id="reg_email" name="email" placeholder="<EMAIL>" required>
                                <label for="reg_email">
                                    <span class="material-icons-round me-1">email</span>
                                    Email address
                                </label>
                            </div>
                            
                            <input type="hidden" name="action" value="register">
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-auth">
                                    <span class="material-icons-round me-1">person_add</span>
                                    Register
                                </button>
                            </div>
                            
                            <div class="text-center mt-4">
                                <p>Already have an account? <a href="login_v2.php" class="auth-link">Login</a></p>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle OTP inputs
            const otpInputs = document.querySelectorAll('.otp-input');
            const otpHiddenInput = document.getElementById('otp');
            
            if (otpInputs.length > 0) {
                // Auto-focus first input
                otpInputs[0].focus();
                
                // Handle input events
                otpInputs.forEach((input, index) => {
                    // Only allow numbers
                    input.addEventListener('input', function(e) {
                        this.value = this.value.replace(/[^0-9]/g, '');
                        
                        // Auto-focus next input
                        if (this.value && index < otpInputs.length - 1) {
                            otpInputs[index + 1].focus();
                        }
                        
                        // Update hidden input with combined OTP
                        updateOtpValue();
                    });
                    
                    // Handle backspace
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Backspace' && !this.value && index > 0) {
                            otpInputs[index - 1].focus();
                        }
                    });
                    
                    // Handle paste
                    input.addEventListener('paste', function(e) {
                        e.preventDefault();
                        const pasteData = e.clipboardData.getData('text').trim();
                        
                        if (/^\d+$/.test(pasteData)) {
                            // Fill inputs with pasted digits
                            for (let i = 0; i < Math.min(pasteData.length, otpInputs.length); i++) {
                                otpInputs[i].value = pasteData[i];
                            }
                            
                            // Focus appropriate input
                            if (pasteData.length < otpInputs.length) {
                                otpInputs[pasteData.length].focus();
                            } else {
                                otpInputs[otpInputs.length - 1].focus();
                            }
                            
                            updateOtpValue();
                        }
                    });
                });
                
                // Function to update hidden OTP field
                function updateOtpValue() {
                    const combinedOtp = Array.from(otpInputs).map(input => input.value).join('');
                    otpHiddenInput.value = combinedOtp;
                    
                    // Auto-submit when all digits are entered
                    if (combinedOtp.length === 6 && /^\d{6}$/.test(combinedOtp)) {
                        setTimeout(() => {
                            document.getElementById('verifyForm').submit();
                        }, 500);
                    }
                }
            }
            
            // Email validation
            const emailInput = document.getElementById('email');
            if (emailInput) {
                emailInput.addEventListener('input', function() {
                    const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.value);
                    if (isValid) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        if (this.value) {
                            this.classList.add('is-invalid');
                        } else {
                            this.classList.remove('is-invalid');
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>











