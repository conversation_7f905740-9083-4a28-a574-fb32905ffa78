<?php
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Check if cash_funds table exists
$result = $conn->query("SHOW TABLES LIKE 'cash_funds'");
if ($result->num_rows == 0) {
    echo "Creating cash_funds table...\n";
    
    // Create cash_funds table
    $create_table = "CREATE TABLE cash_funds (
        id INT(11) NOT NULL AUTO_INCREMENT,
        amount DECIMAL(10,2) NOT NULL,
        fund_date DATE NOT NULL,
        description VARCHAR(255) NOT NULL,
        branch_name VARCHAR(100) DEFAULT 'Main Branch',
        created_by INT(11),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY created_by (created_by),
        CONSTRAINT cash_funds_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id)
    )";
    
    if ($conn->query($create_table)) {
        echo "Cash funds table created successfully.\n";
    } else {
        echo "Error creating cash_funds table: " . $conn->error . "\n";
    }
} else {
    echo "Cash funds table already exists.\n";
}

// Close connection
closeDB($conn);

echo "Script completed.\n";
?>