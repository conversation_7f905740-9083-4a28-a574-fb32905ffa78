<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Initialize variables
$id = '';
$name = '';
$description = '';
$category_id = '';
$brand_id = '';
$sku = '';
$quantity = '';
$unit_price = '';
$reorder_level = '';
$image_url = '';
$branch_name = '';
$weight = ''; // Add weight variable
$is_online = 0; // Initialize the is_online variable
$page_title = 'Add New Product';
$form_action = 'product_form.php';
$is_edit = false;

// Get categories for dropdown
$categories_query = "SELECT * FROM categories ORDER BY name ASC";
$categories_result = $conn->query($categories_query);

// Get brands for dropdown
$brands_query = "SELECT * FROM brands ORDER BY name ASC";
$brands_result = $conn->query($brands_query);

// Get suppliers for dropdown
$suppliers_query = "SELECT * FROM suppliers ORDER BY name ASC";
$suppliers_result = $conn->query($suppliers_query);

// Check if editing existing product
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $is_edit = true;
    $page_title = 'Edit Product';
    $form_action = 'product_form.php?id=' . $id;
    
    // Get product data
    $query = "SELECT * FROM products WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 1) {
        $product = $result->fetch_assoc();
        $name = $product['name'];
        $description = $product['description'];
        $category_id = $product['category_id'];
        $brand_id = $product['brand_id'];
        $supplier_id = $product['supplier_id'];
        $sku = $product['sku'];
        $quantity = $product['quantity'];
        $unit_price = $product['unit_price'];
        $reorder_level = $product['reorder_level'];
        $image_url = $product['image_url'];
        $weight = isset($product['weight']) ? $product['weight'] : ''; // Get weight value
        $is_online = isset($product['is_online']) ? $product['is_online'] : 0;
        $branch_name = $_SESSION['branch_name'];
    } else {
        // Product not found
        header("Location: products.php?error=product_not_found");
        exit();
    }
    
    $stmt->close();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    $category_id = !empty($_POST['category_id']) ? $_POST['category_id'] : null;
    $brand_id = !empty($_POST['brand_id']) ? $_POST['brand_id'] : null;
    $supplier_id = !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null;
    $sku = trim($_POST['sku']);
    $quantity = (int)$_POST['quantity'];
    $unit_price = (float)$_POST['unit_price'];
    $reorder_level = (int)$_POST['reorder_level'];
    $weight = !empty($_POST['weight']) ? (float)$_POST['weight'] : null; // Get weight value
    $branch_name = trim($_SESSION['branch_name']);
    $is_online = isset($_POST['is_online']) ? 1 : 0;
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Product name is required";
    }
    
    if (empty($sku)) {
        $errors[] = "SKU is required";
    }
    
    // Check if SKU already exists (for new products or if SKU changed)
    $sku_check_query = "SELECT id FROM products WHERE sku = ? AND id != ?";
    $stmt = $conn->prepare($sku_check_query);
    $stmt->bind_param("si", $sku, $id);
    $stmt->execute();
    $sku_result = $stmt->get_result();
    
    if ($sku_result->num_rows > 0) {
        $errors[] = "SKU already exists. Please use a different SKU.";
    }
    
    $stmt->close();
    
    // Handle image upload
    $upload_dir = 'uploads/products/';
    
    // Create directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    // Process image upload if a file was selected
    $image_updated = false;
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['image']['type'], $allowed_types)) {
            $errors[] = "Invalid file type. Only JPG, PNG, and GIF files are allowed.";
        } elseif ($_FILES['image']['size'] > $max_size) {
            $errors[] = "File size exceeds the maximum limit of 2MB.";
        } else {
            // Generate unique filename
            $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $new_filename = uniqid('product_') . '.' . $file_extension;
            
            // Move uploaded file
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $new_filename)) {
                // Delete old image if updating
                if ($is_edit && !empty($image_url) && file_exists($upload_dir . $image_url)) {
                    unlink($upload_dir . $image_url);
                }
                
                $image_url = $new_filename;
                $image_updated = true;
            } else {
                $errors[] = "Failed to upload image. Please try again.";
            }
        }
    } elseif (!$is_edit) {
        // For new products, if no image is uploaded, set image_url to empty string instead of null
        $image_url = '';
    }
    // For existing products, if no new image is uploaded, keep the existing image_url
    
    // If no errors, save product
    if (empty($errors)) {
        if ($is_edit) {
            // Update existing product
            if ($image_updated) {
                // Include image_url in the update if a new image was uploaded
                $query = "UPDATE products SET 
                          name = ?, 
                          description = ?, 
                          category_id = ?, 
                          brand_id = ?, 
                          supplier_id = ?, 
                          sku = ?, 
                          quantity = ?, 
                          unit_price = ?, 
                          reorder_level = ?, 
                          image_url = ?,
                          is_online = ?,
                          weight = ?,
                          branch_name = ? 
                          WHERE id = ?";
                
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ssiiissdisidsi", $name, $description, $category_id, $brand_id, $supplier_id, $sku, $quantity, $unit_price, $reorder_level, $image_url, $is_online, $weight, $branch_name, $id);
            } else {
                // Don't update image_url if no new image was uploaded
                $query = "UPDATE products SET 
                          name = ?, 
                          description = ?, 
                          category_id = ?, 
                          brand_id = ?, 
                          supplier_id = ?, 
                          sku = ?, 
                          quantity = ?, 
                          unit_price = ?, 
                          reorder_level = ?, 
                          is_online = ?,
                          weight = ?,
                          branch_name = ? 
                          WHERE id = ?";
                
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ssiiissdisisi", $name, $description, $category_id, $brand_id, $supplier_id, $sku, $quantity, $unit_price, $reorder_level, $is_online, $weight, $branch_name, $id);
            }
            
            if ($stmt->execute()) {
                // Store success message in session instead of using header redirect
                $_SESSION['success_message'] = "Product updated successfully";
                echo "<script>window.location.href='products.php?updated=1';</script>";
                exit();
            } else {
                $errors[] = "Error updating product: " . $conn->error;
            }
        } else {
            // Insert new product
            $query = "INSERT INTO products (name, description, category_id, brand_id, supplier_id, sku, quantity, unit_price, reorder_level, image_url, is_online, weight, branch_name) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ssiiissdisids", $name, $description, $category_id, $brand_id, $supplier_id, $sku, $quantity, $unit_price, $reorder_level, $image_url, $is_online, $weight, $branch_name);
            
            if ($stmt->execute()) {
                // Store success message in session instead of using header redirect
                $_SESSION['success_message'] = "Product added successfully";
                echo "<script>window.location.href='products.php?added=1';</script>";
                exit();
            } else {
                $errors[] = "Error adding product: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Close connection
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2"><?php echo $page_title; ?></h1>
        <a href="products.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to Products
        </a>
    </div>
    
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-body">
            <form method="post" action="<?php echo $form_action; ?>" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">-- Select Category --</option>
                                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_id == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="brand_id" class="form-label">Brand</label>
                                    <select class="form-select" id="brand_id" name="brand_id">
                                        <option value="">-- Select Brand --</option>
                                        <?php while ($brand = $brands_result->fetch_assoc()): ?>
                                            <option value="<?php echo $brand['id']; ?>" <?php echo ($brand_id == $brand['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($brand['name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="supplier_id" class="form-label">Supplier</label>
                                    <select class="form-select" id="supplier_id" name="supplier_id">
                                        <option value="">-- Select Supplier --</option>
                                        <?php while ($supplier = $suppliers_result->fetch_assoc()): ?>
                                            <option value="<?php echo $supplier['id']; ?>" <?php echo (isset($supplier_id) && $supplier_id == $supplier['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($supplier['name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sku" class="form-label">SKU <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="sku" name="sku" value="<?php echo htmlspecialchars($sku); ?>" required>
                                        <button type="button" class="btn btn-secondary" onclick="generateSKU()" title="Generate SKU">
                                            <i class="fas fa-random"></i>
                                        </button>
                                        <button type="button" id="printBarcodeBtn" class="btn btn-info" onclick="showBarcodeModal()" title="Print Barcode" <?php echo empty($sku) ? 'disabled' : ''; ?>>
                                            <i class="fas fa-barcode"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" value="<?php echo htmlspecialchars($quantity); ?>" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="unit_price" class="form-label">Unit Price (₱) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" value="<?php echo htmlspecialchars($unit_price); ?>" min="0" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="reorder_level" class="form-label">Reorder Level <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="reorder_level" name="reorder_level" value="<?php echo htmlspecialchars($reorder_level); ?>" min="0" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Weight (kg)</label>
                                    <input type="number" class="form-control" id="weight" name="weight" value="<?php echo isset($weight) ? htmlspecialchars($weight) : ''; ?>" min="0" step="0.01">
                                    <small class="form-text text-muted">Product weight in kilograms</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Add Online Store Checkbox -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_online" name="is_online" value="1" <?php echo ($is_online == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_online">Include in Online Store</label>
                                    <small class="form-text text-muted d-block">Check this box to make this product available in the online store.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="image" class="form-label">Product Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this, 'imagePreview')">
                            <small class="form-text text-muted">Max file size: 2MB. Allowed formats: JPG, PNG, GIF</small>
                        </div>
                        
                        <div class="mb-3 text-center">
                            <?php if (!empty($image_url)): ?>
                                <img id="imagePreview" src="uploads/products/<?php echo $image_url; ?>" alt="Product Image" class="img-thumbnail mt-2" style="max-height: 200px;">
                            <?php else: ?>
                                <img id="imagePreview" src="#" alt="Product Image Preview" class="img-thumbnail mt-2" style="max-height: 200px; display: none;">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-end">
                    <a href="products.php" class="btn btn-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <?php echo $is_edit ? 'Update Product' : 'Add Product'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<!-- Include JsBarcode library -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>

<script>
function generateSKU() {
    // Define characters to use (only uppercase letters and numbers)
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    
    // Generate an 8-character SKU
    let sku = '';
    for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * chars.length);
        sku += chars[randomIndex];
    }
    
    // Set the generated SKU in the input field
    document.getElementById('sku').value = sku;
    // Enable barcode button if SKU is generated
    document.getElementById('printBarcodeBtn').disabled = !sku;
}

function showBarcodeModal() {
    const sku = document.getElementById('sku').value;
    // Get current name and price from form fields
    const productName = document.getElementById('name').value || 'N/A';
    const productPriceRaw = document.getElementById('unit_price').value;
    const productPrice = !isNaN(parseFloat(productPriceRaw)) ? '₱' + parseFloat(productPriceRaw).toFixed(2) : 'Price N/A';

    if (sku) {
        // Update name and price display in the modal (IDs remain the same)
        document.getElementById('barcodeProductName').textContent = productName;
        document.getElementById('barcodeProductPrice').textContent = productPrice;

        // Generate barcode
        JsBarcode("#barcode", sku, {
            format: "CODE128", // Or any other format you prefer
            lineColor: "#000",
            width: 2,
            height: 80, // Keep reduced height
            displayValue: true, // Show SKU text below barcode
            marginTop: 10 // Keep margin
        });
        // Show the modal
        var barcodeModal = new bootstrap.Modal(document.getElementById('barcodeModal'));
        barcodeModal.show();
    } else {
        alert('Please enter or generate an SKU first.');
    }
}

function printBarcode() {
    // Select the container with name, price, and barcode
    const printContentNode = document.getElementById('barcodeContainer');
    // Clone the node to avoid modifying the original modal content directly
    const contentToPrint = printContentNode.cloneNode(true);

    // Optional: Add specific print styles if needed
    // For example, ensure text is black for printing
    contentToPrint.style.color = '#000';
    const textElements = contentToPrint.querySelectorAll('div');
    textElements.forEach(el => el.style.color = '#000');

    const printWindow = window.open('', '_blank', 'height=600,width=800');
    printWindow.document.write('<html><head><title>Print Barcode</title>');
    // Optional: Include Bootstrap for grid layout if needed, or basic print CSS
    printWindow.document.write('<style>');
    printWindow.document.write(`
        body { text-align: center; margin-top: 20px; }
        .row { display: flex; justify-content: center; margin-bottom: 15px; width: 100%; }
        .col-auto { flex: 0 0 auto; padding: 0 10px; }
        #barcodeProductName { font-weight: bold; font-size: 1.1em; text-align: left; }
        #barcodeProductPrice { text-align: right; }
        svg#barcode { display: block; margin: 10px auto 0 auto; }
    `);
    printWindow.document.write('</style></head><body>');
    printWindow.document.write(contentToPrint.innerHTML);
    printWindow.document.write('</body></html>');

    printWindow.document.close(); // Necessary for IE >= 10
    printWindow.focus(); // Necessary for IE >= 10

    // Use timeout to ensure content is loaded before printing
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 250); // Adjust timeout as needed
}


// Disable barcode button initially if SKU is empty
document.addEventListener('DOMContentLoaded', function() {
    const skuInput = document.getElementById('sku');
    const printBtn = document.getElementById('printBarcodeBtn');
    printBtn.disabled = !skuInput.value;

    skuInput.addEventListener('input', function() {
        printBtn.disabled = !this.value;
    });
});

function previewImage(input, previewId) {
    const file = input.files[0];
    const preview = document.getElementById(previewId);
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(file);
    } else {
        preview.src = '#';
        preview.style.display = 'none';
    }
}
</script>

<!-- Barcode Modal -->
<div class="modal fade" id="barcodeModal" tabindex="-1" aria-labelledby="barcodeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="barcodeModalLabel">Product Barcode</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center" id="barcodeContainer">
        <!-- Use Bootstrap row/col for side-by-side layout -->
        <div class="row justify-content-center mb-3">
            <div class="col-auto text-start" style="font-weight: bold; font-size: 1.1em;" id="barcodeProductName">
                <!-- Name will be inserted here -->
            </div>
            <div class="col-auto text-end" id="barcodeProductPrice">
                <!-- Price will be inserted here -->
            </div>
        </div>
        <svg id="barcode"></svg>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" onclick="printBarcode()">
            <i class="fas fa-print me-1"></i> Print Barcode
        </button>
      </div>
    </div>
  </div>
</div>