/**
 * Sales page JavaScript functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Make product cards clickable
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        // Add hover effect
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.03)';
            this.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(2deg) rotateY(2deg)';
            this.style.boxShadow = '';
        });
        
        // Add click event to add product to cart
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on form elements
            if (e.target.closest('.product-form') || 
                e.target.tagName === 'INPUT' || 
                e.target.tagName === 'BUTTON') {
                return;
            }
            
            const productId = this.getAttribute('data-product-id');
            const productQuantity = parseInt(this.getAttribute('data-product-quantity'));
            
            // Check if product is in stock
            if (productQuantity <= 0) {
                alert('This product is out of stock.');
                return;
            }
            
            // Find the form within this card
            const form = this.querySelector('.product-form');
            if (form) {
                // Submit the form to add product to cart
                form.submit();
            }
        });
    });
    
    // Prevent form submission from bubbling to card click
    const productForms = document.querySelectorAll('.product-form');
    productForms.forEach(form => {
        form.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
});

// If there's form validation for the checkout form, update it to remove email validation
document.addEventListener('DOMContentLoaded', function() {
    const checkoutForm = document.getElementById('checkout-form');
    
    // Add event listener to checkout form submission
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
            const customerName = document.getElementById('customer_name').value.trim();
            const invoiceNumber = document.getElementById('invoice_number').value.trim();
            
            // Only validate required fields
            if (!customerName || !invoiceNumber) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }
            
            // The hide_receipt checkbox is already part of the form
            // No need to add a hidden field as it will be submitted with the form
            
            return true;
        });
    }
    
    // Remove email validation function if it exists
    // function isValidEmail(email) {
    //     const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    //     return re.test(email);
    // }
});

// Function to update item total and cart total
function updateTotals() {
    let newCartTotal = 0;

    // Get all price inputs to ensure we update all items
    const allPriceInputs = document.querySelectorAll('.item-price');
    
    console.log('Updating totals for ' + allPriceInputs.length + ' items');

    allPriceInputs.forEach(input => {
        const index = input.getAttribute('data-index');
        const quantity = parseFloat(input.getAttribute('data-quantity'));
        const price = parseFloat(input.value);

        console.log(`Item #${index}: Quantity=${quantity}, Price=${price}`);

        // Calculate item total - ensure we're only multiplying once
        const itemTotal = price * quantity;

        // Update item total display
        const itemTotalElement = input.closest('tr').querySelector('.item-total');
        itemTotalElement.textContent = itemTotal.toFixed(2);

        // Add to cart total
        newCartTotal += itemTotal;
    });

    // Update cart total display
    const cartTotalElement = document.getElementById('cart-total');
    if (cartTotalElement) {
        cartTotalElement.textContent = newCartTotal.toFixed(2);
    }

    // Update checkout total display
    const checkoutTotalElement = document.getElementById('checkout-total-amount');
    if (checkoutTotalElement) {
        checkoutTotalElement.textContent = newCartTotal.toFixed(2);
    }

    // Update hidden input for checkout
    const checkoutTotalInput = document.getElementById('checkout-total-input');
    if (checkoutTotalInput) {
        checkoutTotalInput.value = newCartTotal.toFixed(2);
    }

    return newCartTotal;
}

// Function to save modified prices to hidden fields for form submission
function saveModifiedPrices() {
    // Clear previous hidden fields
    const container = document.getElementById('updated-prices-container');
    if (!container) return;
    
    container.innerHTML = '';

    // Add hidden fields for each item's updated price
    const allPriceInputs = document.querySelectorAll('.item-price');
    
    console.log('Saving modified prices for ' + allPriceInputs.length + ' items');

    allPriceInputs.forEach(input => {
        const index = input.getAttribute('data-index');
        const price = input.value;
        
        console.log(`Saving price for item #${index}: ${price}`);

        const hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.name = `updated_prices[${index}]`;
        hiddenField.value = price;

        container.appendChild(hiddenField);
    });
}

// Add event listener to checkout form submission
document.addEventListener('DOMContentLoaded', function() {
    // Store original prices when page loads to preserve modified prices
    const originalPrices = {};
    document.querySelectorAll('.item-price').forEach(input => {
        const index = input.getAttribute('data-index');
        originalPrices[index] = parseFloat(input.value);
        console.log(`Original price for item #${index}: ${originalPrices[index]}`);
    });

    // Get all price inputs
    const priceInputs = document.querySelectorAll('.item-price');
    
    console.log('Found ' + priceInputs.length + ' price inputs');

    // Add event listeners to price inputs
    priceInputs.forEach(input => {
        // Add event listeners for both change and input events
        input.addEventListener('change', function() {
            updateTotals();
            saveModifiedPrices();
        });
        
        input.addEventListener('input', function() {
            updateTotals();
        });

        // Add blur event to ensure update happens when focus leaves the input
        input.addEventListener('blur', function() {
            updateTotals();
            saveModifiedPrices();
        });
    });

    // Add event listener to checkout form
    const checkoutForm = document.querySelector('#checkoutModal form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
            // Update totals one final time before submission
            updateTotals();
            
            // Save all modified prices to hidden fields
            saveModifiedPrices();
            
            console.log('Form submitted with updated prices');
        });
    }

    // Run updateTotals once on page load
    updateTotals();
});
