<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$conn = connectDB();
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch'; // Assuming branch context is needed

// --- Filter Logic ---
$filter_supplier_id = isset($_GET['supplier_id']) ? $_GET['supplier_id'] : '';
$filter_date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$filter_date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Fetch suppliers for filter dropdown
$suppliers_query = "SELECT id, name FROM suppliers ORDER BY name ASC";
$suppliers_result = $conn->query($suppliers_query);

// Build the base query
// We need to group by PO number to get one row per PO
// Use MIN() for dates/status assuming they are consistent per PO, SUM() for total
$query = "SELECT
            po.po_number,
            po.supplier_id,
            s.name AS supplier_name,
            MIN(po.created_at) AS date_created,
            MIN(po.expected_delivery_date) AS expected_delivery,
            SUM(po.quantity * po.unit_price) AS total_amount,
            MIN(po.status) AS status
          FROM

            purchase_orders po 
          LEFT JOIN
            suppliers s ON po.supplier_id = s.id
          WHERE
            po.ordering_branch = ?"; // Filter by branch if necessary <<< Add semicolon here

$params = [$branch_name];
$types = "s";

// Add filters to the query
if (!empty($filter_supplier_id)) {
    $query .= " AND po.supplier_id = ?";
    $params[] = $filter_supplier_id;
    $types .= "i";
}
if (!empty($filter_date_from)) {
    $query .= " AND DATE(po.created_at) >= ?";
    $params[] = $filter_date_from;
    $types .= "s";
}
if (!empty($filter_date_to)) {
    $query .= " AND DATE(po.created_at) <= ?";
    $params[] = $filter_date_to;
    $types .= "s";
}

$query .= " GROUP BY po.po_number, po.supplier_id, s.name ORDER BY MIN(po.created_at) DESC";

$stmt = $conn->prepare($query);
if ($stmt) {
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    // Handle error - query preparation failed
    echo "Error preparing query: " . $conn->error;
    $result = false; // Set result to false to avoid errors later
}

?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Purchase Orders (V2)</h5>
                    <!-- Link to the correct create page -->
                    <button type="button" class="btn btn-primary" onclick="window.location.href='create_purchase_order.php?action=new'">
                        <i class="fas fa-plus me-2"></i>Create Purchase Order
                    </button>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form method="GET" action="purchase_order_v2.php" class="mb-4 p-3 border rounded">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label for="supplier_id" class="form-label">Supplier</label>
                                <select class="form-select" id="supplier_id" name="supplier_id">
                                    <option value="">All Suppliers</option>
                                    <?php while ($supplier = $suppliers_result->fetch_assoc()): ?>
                                        <option value="<?php echo $supplier['id']; ?>" <?php echo ($filter_supplier_id == $supplier['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($supplier['name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Date From</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Date To</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-info w-100">Filter</button>
                            </div>
                        </div>
                    </form>
                    <!-- End Filter Form -->

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>PO Number</th>
                                    <th>Supplier</th>
                                    <th>Date Created</th>
                                    <th>Expected Delivery</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if ($result && $result->num_rows > 0):
                                    while ($row = $result->fetch_assoc()):
                                ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['po_number']); ?></td>
                                    <td><?php echo htmlspecialchars($row['supplier_name'] ?? 'N/A'); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($row['date_created'])); ?></td>
                                    <td><?php echo !empty($row['expected_delivery']) ? date('Y-m-d', strtotime($row['expected_delivery'])) : 'N/A'; ?></td>
                                    <td>₱<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                    <td>
                                        <span class="badge bg-<?php
                                            switch ($row['status']) {
                                                case 'draft': echo 'secondary'; break;
                                                case 'pending': echo 'warning'; break;
                                                case 'approved': echo 'info'; break;
                                                case 'sent': echo 'primary'; break;
                                                case 'complete': // Changed from 'received' to 'complete' based on detail page logic
                                                case 'completed': echo 'success'; break; // Added 'completed' as potential status
                                                case 'cancelled': echo 'danger'; break;
                                                default: echo 'light';
                                            }
                                        ?>"><?php echo ucfirst(htmlspecialchars($row['status'])); ?></span>
                                    </td>
                                    <td>
                                        <!-- Link to the new detail page -->
                                        <a href="purchase_order_detail_v2.php?po_number=<?php echo urlencode($row['po_number']); ?>" class="btn btn-sm btn-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <!-- Add other actions like edit/cancel if needed -->
                                    </td>
                                </tr>
                                <?php
                                    endwhile;
                                else:
                                ?>
                                <tr>
                                    <td colspan="7" class="text-center">No purchase orders found.</td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($result) $stmt->close(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>