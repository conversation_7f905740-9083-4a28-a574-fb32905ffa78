<?php
require_once 'config/database.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit();
}

// Get database connection
$conn = connectDB();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $transfer_number = $_POST['transfer_number'];
    $source_branch = $_POST['source_branch'];
    $destination_branch = $_POST['destination_branch'];
    $notes = trim($_POST['notes']);
    $products = isset($_POST['products']) ? $_POST['products'] : [];
    $quantities = isset($_POST['quantities']) ? $_POST['quantities'] : [];

    // Validate form data
    $errors = [];

    if (empty($transfer_number)) $errors[] = "Transfer number is required";
    if (empty($source_branch)) $errors[] = "Source branch is required";
    if (empty($destination_branch)) $errors[] = "Destination branch is required";
    if (empty($products)) $errors[] = "At least one product must be selected";
    if ($source_branch === $destination_branch) $errors[] = "Source and destination branches cannot be the same";

    if (empty($errors)) {
        try {
            // Start transaction
            $conn->begin_transaction();            // Insert transfer record with completed status
            $transfer_query = "INSERT INTO transfers (
                transfer_number, source_branch, destination_branch, 
                notes, created_by, status
            ) VALUES (?, ?, ?, ?, ?, 'completed')";

            $stmt = $conn->prepare($transfer_query);
            $stmt->bind_param("ssssi", 
                $transfer_number, 
                $source_branch, 
                $destination_branch, 
                $notes, 
                $_SESSION['user_id']
            );
            $stmt->execute();
            $transfer_id = $stmt->insert_id;

            // Insert transfer items and update source branch inventory
            foreach ($products as $index => $product_id) {
                $quantity = $quantities[$index];                // Get product details from source branch including SKU
                $source_product_query = "SELECT p.*, p.id as product_id FROM products p 
                                       WHERE p.id = ? AND p.branch_name = ? AND p.quantity >= ?";
                $source_stmt = $conn->prepare($source_product_query);
                $source_stmt->bind_param("isi", $product_id, $source_branch, $quantity);
                $source_stmt->execute();
                $source_product = $source_stmt->get_result()->fetch_assoc();

                if (!$source_product) {
                    throw new Exception("Insufficient stock for product ID: $product_id");
                }

                // Insert transfer item
                $item_query = "INSERT INTO transfer_items (transfer_id, product_id, quantity) 
                              VALUES (?, ?, ?)";
                $item_stmt = $conn->prepare($item_query);
                $item_stmt->bind_param("iii", $transfer_id, $product_id, $quantity);
                $item_stmt->execute();                // Reduce quantity from source branch
                $update_query = "UPDATE products SET quantity = quantity - ? 
                               WHERE id = ? AND branch_name = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("iis", $quantity, $product_id, $source_branch);
                $update_stmt->execute();                // Check if product exists in destination branch by SKU
                $check_dest_query = "SELECT id FROM products WHERE sku = ? AND branch_name = ?";
                $check_dest_stmt = $conn->prepare($check_dest_query);
                $check_dest_stmt->bind_param("ss", $source_product['sku'], $destination_branch);
                $check_dest_stmt->execute();
                $check_dest_result = $check_dest_stmt->get_result();

                if ($check_dest_result->num_rows > 0) {
                    // Update existing product quantity in destination branch
                    $update_dest_query = "UPDATE products SET quantity = quantity + ? 
                                        WHERE sku = ? AND branch_name = ?";
                    $update_dest_stmt = $conn->prepare($update_dest_query);
                    $update_dest_stmt->bind_param("iss", $quantity, $source_product['sku'], $destination_branch);
                    $update_dest_stmt->execute();
                } else {
                    // Create new product in destination branch using source product details
                    $new_product_query = "INSERT INTO products (name, sku, description, unit_price, 
                                        quantity, category_id, brand_id, supplier_id, reorder_level, 
                                        image_url, branch_name) 
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $new_product_stmt = $conn->prepare($new_product_query);
                    $new_product_stmt->bind_param("sssdiiiiiss", 
                        $source_product['name'],
                        $source_product['sku'],
                        $source_product['description'],
                        $source_product['unit_price'],
                        $quantity,
                        $source_product['category_id'],
                        $source_product['brand_id'],
                        $source_product['supplier_id'],
                        $source_product['reorder_level'],
                        $source_product['image_url'],
                        $destination_branch
                    );

                    // Create new product in destination branch
                    $new_product_query = "INSERT INTO products (id, name, sku, description, unit_price, 
                                        quantity, category_id, brand_id, supplier_id, reorder_level, 
                                        image_url, branch_name) 
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $new_product_stmt = $conn->prepare($new_product_query);
                    $new_product_stmt->bind_param("isssdiiiisss", 
                        $product['id'],
                        $product['name'],
                        $product['sku'],
                        $product['description'],
                        $product['unit_price'],
                        $quantity,
                        $product['category_id'],
                        $product['brand_id'],
                        $product['supplier_id'],
                        $product['reorder_level'],
                        $product['image_url'],
                        $destination_branch
                    );
                    $new_product_stmt->execute();
                }
            }

            // Commit transaction
            $conn->commit();

            // Redirect with success message
            $_SESSION['success_message'] = "Transfer #$transfer_number has been created successfully";
            header('Location: transfers.php');
            exit();

        } catch (Exception $e) {
            // Rollback on error
            $conn->rollback();
            $_SESSION['error_message'] = "Error creating transfer: " . $e->getMessage();
            header('Location: transfers_v2.php');
            exit();
        }
    } else {
        // Redirect back with errors
        $_SESSION['error_message'] = implode("<br>", $errors);
        header('Location: transfers_v2.php');
        exit();
    }
}

// Close connection
closeDB($conn);
