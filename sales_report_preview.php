<?php
require_once 'includes/header.php';
require_once 'connection.php';

// Get date from request or use current date
$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Define payment types at global scope
$paymentTypes = ['Cash', 'Card', 'Cheque', 'Bank Transfer'];

// Function to get sales data for the selected date
function getSalesData($conn, $date, $branch_name) {
    $paymentTypes = ['Cash', 'Card', 'Cheque', 'Bank Transfer'];
    $results = [];
    $feesResult = null;
    
    // Get transactions for each payment type
    foreach ($paymentTypes as $paymentType) {
        $query = "SELECT o.id as invoice_id, o.created_at, p.name as product_name, 
                 oi.quantity, oi.unit_price as price, 
                 (oi.total_price - (oi.quantity * oi.unit_price)) as discount, 
                 oi.total_price as total, o.payment_type
                 FROM orders o
                 JOIN order_items oi ON o.id = oi.order_id
                 JOIN products p ON oi.product_id = p.id
                 WHERE DATE(o.created_at) = ? 
                 AND o.branch_name = ?
                 AND o.payment_type = ?
                 ORDER BY o.id";
        
        $stmt = $conn->prepare($query);
        
        if ($stmt === false) {
            return [
                'Cash' => [],
                'Card' => [],
                'Cheque' => [],
                'Bank Transfer' => [],
                'fees' => $feesResult,
                'error' => null
            ];
        }
        
        $stmt->bind_param("sss", $date, $branch_name, $paymentType);
        $stmt->execute();
        $results[$paymentType] = $stmt->get_result();
        $stmt->close();
    }
    
    // Check if school_fees table exists
    $checkTableQuery = "SHOW TABLES LIKE 'school_fees'";
    $tableExists = $conn->query($checkTableQuery)->num_rows > 0;
    
    $feesResult = null;
    
    if ($tableExists) {
        // Get school fees
        $feesQuery = "SELECT sf.id, s.id as student_id, s.first_name, s.last_name, 
                     sf.amount, sf.description, sf.remarks, sf.created_at
                     FROM school_fees sf
                     JOIN students s ON sf.student_id = s.id
                     WHERE DATE(sf.created_at) = ?
                     AND sf.branch_name = ?
                     ORDER BY sf.id";
        
        $stmt = $conn->prepare($feesQuery);
        
        if ($stmt !== false) {
            $stmt->bind_param("ss", $date, $branch_name);
            $stmt->execute();
            $feesResult = $stmt->get_result();
            $stmt->close();
        }
    }
    
    return [
        'Cash' => $results['Cash'],
        'Card' => $results['Card'],
        'Cheque' => $results['Cheque'],
        'Bank Transfer' => $results['Bank Transfer'],
        'fees' => $feesResult,
        'error' => null
    ];
}

// Get sales data
$salesData = getSalesData($conn, $date, $branch_name);

// Check for errors
if (isset($salesData['error']) && $salesData['error'] !== null) {
    echo '<div class="alert alert-danger">' . htmlspecialchars($salesData['error']) . '</div>';
    // Include footer and exit
    require_once 'includes/footer.php';
    exit;
}

// Calculate totals
$cashTotal = 0;
$cardTotal = 0;
$feesTotal = 0;

// Initialize sales arrays and totals for each payment type
$sales = [];
$totals = [];
foreach ($paymentTypes as $type) {
    $sales[$type] = [];
    $totals[$type] = 0;
}

// Process sales for each payment type
foreach ($paymentTypes as $type) {
    if (is_object($salesData[$type]) && $salesData[$type]->num_rows > 0) {
        $currentInvoice = null;
        $invoiceTotal = 0;

        while ($row = $salesData[$type]->fetch_assoc()) {
            if ($currentInvoice != $row['invoice_id']) {
                if ($currentInvoice !== null) {
                    $sales[$type][$currentInvoice]['total'] = $invoiceTotal;
                }
                $currentInvoice = $row['invoice_id'];
                $sales[$type][$currentInvoice] = [
                    'items' => [],
                    'total' => 0
                ];
                $invoiceTotal = 0;
            }
            
            $sales[$type][$currentInvoice]['items'][] = $row;
            $invoiceTotal += $row['total'];
            $totals[$type] += $row['total'];
        }

        if ($currentInvoice !== null) {
            $sales[$type][$currentInvoice]['total'] = $invoiceTotal;
        }
    }
}

// Process school fees
$schoolFees = [];
if (is_object($salesData['fees']) && $salesData['fees']->num_rows > 0) {
    while ($row = $salesData['fees']->fetch_assoc()) {
        $schoolFees[] = $row;
        $feesTotal += $row['amount'];
    }
}

// Calculate grand total
$grandTotal = $cashTotal + $cardTotal + $feesTotal;

// Get expenses for the day
$expensesQuery = "SELECT SUM(amount) as total_expenses FROM expenses 
                 WHERE DATE(created_at) = ? AND branch_name = ?";
$stmt = $conn->prepare($expensesQuery);

// Default expenses to 0 if query fails
$totalExpenses = 0;
if ($stmt !== false) {
    $stmt->bind_param("ss", $date, $branch_name);
    $stmt->execute();
    $expensesResult = $stmt->get_result()->fetch_assoc();
    $totalExpenses = $expensesResult['total_expenses'] ?: 0;
    $stmt->close();
}

// Calculate cash on hand
$cashOnHand = $grandTotal - $totalExpenses;

// Get payment details
$paymentDetails = [
    'card_payments' => 0,
    'cash_payments' => 0,
    'cheque_payments' => 0,
    'bank_transfer' => 0
];

$paymentDetailsQuery = "SELECT 
                        SUM(CASE WHEN payment_type = 'Card' THEN total_amount ELSE 0 END) as card_payments,
                        SUM(CASE WHEN payment_type = 'Cash' THEN total_amount ELSE 0 END) as cash_payments,
                        SUM(CASE WHEN payment_type = 'Cheque' THEN total_amount ELSE 0 END) as cheque_payments,
                        SUM(CASE WHEN payment_type = 'Bank Transfer' THEN total_amount ELSE 0 END) as bank_transfer
                        FROM orders
                        WHERE DATE(created_at) = ? AND branch_name = ?";
$stmt = $conn->prepare($paymentDetailsQuery);

if ($stmt !== false) {
    $stmt->bind_param("ss", $date, $branch_name);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $paymentDetails = $result->fetch_assoc();
    }
    $stmt->close();
}

// Format date for display
$displayDate = date('m/d/Y', strtotime($date));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Sales Report - <?php echo htmlspecialchars($displayDate); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .report-header h1 {
            margin: 0;
            color: #ff0000;
            font-size: 18px;
        }
        .report-header h2 {
            margin: 5px 0;
            font-size: 16px;
        }
        .report-header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .section-header {
            font-weight: bold;
            background-color: #f8f9fa;
            padding: 6px;
            margin-top: 15px;
            margin-bottom: 5px;
        }
        .invoice-header {
            font-weight: bold;
            padding: 6px;
            background-color: #e9ecef;
        }
        .invoice-total {
            font-weight: bold;
            text-align: right;
            padding: 6px;
            background-color: #e9ecef;
        }
        .sub-total {
            font-weight: bold;
            text-align: right;
            padding: 6px;
        }
        .grand-total {
            font-weight: bold;
            text-align: right;
            padding: 6px;
            background-color: #e9ecef;
        }
        .particulars-table {
            width: 50%;
            float: right;
            margin-top: 20px;
        }
        .print-buttons {
            margin-bottom: 20px;
        }
        @media print {
            .print-buttons {
                display: none;
            }
            body {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="print-buttons">
        <button onclick="window.print()">Print Report</button>
        <button onclick="window.location.href='sales_report.php'">Back to Sales Report</button>
    </div>

    <div class="report-header">
        <h1>MUSAR MUSIC CORPORATION</h1>
        <h2>Musar Maharlika</h2>
        <p>Daily Sales Report</p>
        <p>Transaction Date: <?php echo htmlspecialchars($displayDate); ?></p>
    </div>

    <!-- RETAIL SECTION -->
    <div class="section-header">RETAIL</div>
    <table>
        <thead>
            <tr>
                <th>Invoice #</th>
                <th>Description</th>
                <th>Qty Sold</th>
                <th>Selling Price</th>
                <th>Discount</th>
                <th>Total</th>
                <th>Remarks</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($paymentTypes as $type): ?>
            <!-- <?php echo $type; ?> Transactions -->
            <tr>
                <td colspan="7" class="section-header"><?php echo $type; ?> Transactions</td>
            </tr>
            
            <?php if (count($sales[$type]) > 0): ?>
                <?php foreach ($sales[$type] as $invoiceId => $invoice): ?>
                    <tr>
                        <td colspan="7" class="invoice-header">- <?php echo htmlspecialchars($invoiceId); ?></td>
                    </tr>
                    <?php foreach ($invoice['items'] as $item): ?>
                    <tr>
                        <td></td>
                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                        <td><?php echo $item['quantity']; ?></td>
                        <td><?php echo number_format($item['price'], 2); ?></td>
                        <td><?php echo number_format($item['discount'], 2); ?></td>
                        <td><?php echo number_format($item['total'], 2); ?></td>
                        <td><?php echo htmlspecialchars($item['remarks'] ?? ''); ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <tr>
                        <td colspan="7" class="invoice-total">Invoice-total: <?php echo number_format($invoice['total'], 2); ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="7" class="text-center">No <?php echo strtolower($type); ?> transactions for this date</td>
                </tr>
            <?php endif; ?>
            
            <tr>
                <td colspan="7" class="sub-total"><?php echo $type; ?> Sales Total: <?php echo number_format($totals[$type], 2); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- SCHOOL FEES SECTION -->
    <div class="section-header">SCHOOL FEES</div>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Student Name</th>
                <th>Description</th>
                <th>Amount</th>
                <th>Remarks</th>
            </tr>
        </thead>
        <tbody>
            <?php if (count($schoolFees) > 0): ?>
                <?php foreach ($schoolFees as $fee): ?>
                    <tr>
                        <td><?php echo $fee['student_id']; ?></td>
                        <td><?php echo htmlspecialchars($fee['last_name'] . ', ' . $fee['first_name']); ?></td>
                        <td><?php echo htmlspecialchars($fee['description']); ?></td>
                        <td><?php echo number_format($fee['amount'], 2); ?></td>
                        <td><?php echo htmlspecialchars($fee['remarks'] ?? ''); ?></td>
                    </tr>
                    <tr>
                        <td colspan="5" class="sub-total">Sub-total: <?php echo number_format($fee['amount'], 2); ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="5" class="text-center">No school fees for this date</td>
                </tr>
            <?php endif; ?>
            
            <tr>
                <td colspan="5" class="sub-total">Cash Payments Total: <?php echo number_format($feesTotal, 2); ?></td>
            </tr>
            <tr>
                <td colspan="5" class="grand-total">Total: <?php echo number_format($feesTotal, 2); ?></td>
            </tr>
        </tbody>
    </table>

    <!-- PARTICULARS SECTION -->
    <table class="particulars-table">
        <thead>
            <tr>
                <th>Particulars</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Card Payments (Debit)</td>
                <td><?php echo number_format($paymentDetails['card_payments'] ?? 0, 2); ?></td>
            </tr>
            <tr>
                <td>Card Payment (Credit)</td>
                <td><?php echo number_format($cardTotal, 2); ?></td>
            </tr>
            <tr>
                <td>Cheque Payments</td>
                <td><?php echo number_format($paymentDetails['cheque_payments'] ?? 0, 2); ?></td>
            </tr>
            <tr>
                <td>Charge Transactions</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>Cash Transactions</td>
                <td><?php echo number_format($cashTotal, 2); ?></td>
            </tr>
            <tr>
                <td>Bank Transfer</td>
                <td><?php echo number_format($paymentDetails['bank_transfer'] ?? 0, 2); ?></td>
            </tr>
            <tr>
                <td>School Cash Payments</td>
                <td><?php echo number_format($feesTotal, 2); ?></td>
            </tr>
            <tr>
                <td>Total Sales</td>
                <td><?php echo number_format($grandTotal, 2); ?></td>
            </tr>
            <tr>
                <td>Expenses</td>
                <td><?php echo number_format($totalExpenses, 2); ?></td>
            </tr>
            <tr>
                <td>Remittance</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>Cash on hand</td>
                <td><?php echo number_format($cashOnHand, 2); ?></td>
            </tr>
        </tbody>
    </table>

    <script>
        // Auto print when in print preview mode
        if (window.location.search.includes('print=true')) {
            window.onload = function() {
                window.print();
            };
        }
    </script>
</body>
</html>