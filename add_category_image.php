<?php
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// SQL to add image_url column to categories table if it doesn't exist
$check_column_sql = "SHOW COLUMNS FROM categories LIKE 'image_url'";
$result = $conn->query($check_column_sql);

if ($result->num_rows == 0) {
    // Column doesn't exist, add it
    $alter_sql = "ALTER TABLE categories ADD COLUMN image_url VARCHAR(255) DEFAULT NULL AFTER description";
    
    if ($conn->query($alter_sql) === TRUE) {
        echo "<p>Successfully added image_url column to categories table.</p>";
    } else {
        echo "<p>Error adding image_url column: " . $conn->error . "</p>";
    }
} else {
    echo "<p>The image_url column already exists in the categories table.</p>";
}

// Close connection
closeDB($conn);

echo "<p><a href='categories.php'>Return to Categories</a></p>";
?>