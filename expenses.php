<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start the session at the beginning of the file
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in before any output
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Check if branch_name column exists in expenses table
$result = $conn->query("SHOW COLUMNS FROM expenses LIKE 'branch_name'");
if ($result->num_rows == 0) {
    // Add branch_name column to expenses table
    $alter_query = "ALTER TABLE expenses ADD COLUMN branch_name VARCHAR(100) DEFAULT 'Main Branch' AFTER category";
    $conn->query($alter_query);
}

// Check if cash_funds table exists, if not create it
$result = $conn->query("SHOW TABLES LIKE 'cash_funds'");
if ($result->num_rows == 0) {
    // Create cash_funds table
    $create_table = "CREATE TABLE cash_funds (
        id INT(11) NOT NULL AUTO_INCREMENT,
        amount DECIMAL(10,2) NOT NULL,
        fund_date DATE NOT NULL,
        description VARCHAR(255) NOT NULL,
        branch_name VARCHAR(100) DEFAULT 'Main Branch',
        created_by INT(11),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY created_by (created_by),
        CONSTRAINT cash_funds_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id)
    )";
    $conn->query($create_table);
}

// Check if cash_funds table exists, if not create it
$result = $conn->query("SHOW TABLES LIKE 'cash_funds'");
if ($result->num_rows == 0) {
    // Create cash_funds table
    $create_table = "CREATE TABLE cash_funds (
        id INT(11) NOT NULL AUTO_INCREMENT,
        amount DECIMAL(10,2) NOT NULL,
        fund_date DATE NOT NULL,
        description VARCHAR(255) NOT NULL,
        branch_name VARCHAR(100) DEFAULT 'Main Branch',
        created_by INT(11),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY created_by (created_by),
        CONSTRAINT cash_funds_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id)
    )";
    $conn->query($create_table);
}

// Initialize variables
$search = '';
$category_filter = '';
$date_from = '';
$date_to = '';
$sort_by = 'expense_date';
$sort_order = 'DESC';
$page = 1;
$items_per_page = 10;
$show_report = isset($_GET['report']) && $_GET['report'] == '1';

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['category'])) {
    $category_filter = $_GET['category'];
}

if (isset($_GET['date_from'])) {
    $date_from = $_GET['date_from'];
}

if (isset($_GET['date_to'])) {
    $date_to = $_GET['date_to'];
}

// Set default date range to current month if not set
if (empty($date_from) && empty($date_to)) {
    $date_from = date('Y-m-01');
    $date_to = date('Y-m-t');
}
// If only one is set, set the other to match for a single-day filter
if (!empty($date_from) && empty($date_to)) {
    $date_to = $date_from;
}
if (!empty($date_to) && empty($date_from)) {
    $date_from = $date_to;
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Get expense categories for filter dropdown - exclude Cash Fund category
$categories_query = "SELECT DISTINCT category FROM expenses WHERE category != 'Cash Fund' ORDER BY category ASC";
$categories_result = $conn->query($categories_query);

// Build query for expenses - exclude Cash Fund category
$query = "SELECT e.*, u.username FROM expenses e 
          LEFT JOIN users u ON e.created_by = u.id 
          WHERE e.branch_name = ? AND e.category != 'Cash Fund'";

$count_query = "SELECT COUNT(*) as total FROM expenses e 
                LEFT JOIN users u ON e.created_by = u.id 
                WHERE e.branch_name = ? AND e.category != 'Cash Fund'";

// Get latest cash fund
$cash_fund_query = "SELECT * FROM cash_funds WHERE branch_name = ? ORDER BY fund_date DESC, id DESC LIMIT 1";
$stmt_cf = $conn->prepare($cash_fund_query);
$stmt_cf->bind_param("s", $branch_name);
$stmt_cf->execute();
$latest_cash_fund = $stmt_cf->get_result()->fetch_assoc();
$stmt_cf->close();

// Initialize running_balance
$running_balance = 0;

// If we have a cash fund, set it as the beginning balance
if ($latest_cash_fund) {
    $running_balance = $latest_cash_fund['amount'];
    
    // Get all expenses after the latest cash fund date
    $expenses_after_query = "SELECT SUM(amount) as total_expenses FROM expenses 
                           WHERE branch_name = ? AND category != 'Cash Fund' 
                           AND expense_date >= ?";
    $stmt_exp = $conn->prepare($expenses_after_query);
    $stmt_exp->bind_param("ss", $branch_name, $latest_cash_fund['fund_date']);
    $stmt_exp->execute();
    $expenses_result = $stmt_exp->get_result()->fetch_assoc();
    $stmt_exp->close();
    
    // Subtract expenses from running balance
    if ($expenses_result && $expenses_result['total_expenses']) {
        $running_balance -= $expenses_result['total_expenses'];
    }
}

// Get all cash funds for the report
$all_cash_funds_query = "SELECT cf.*, u.username FROM cash_funds cf 
                        LEFT JOIN users u ON cf.created_by = u.id 
                        WHERE cf.branch_name = ?";

if (!empty($date_from)) {
    $all_cash_funds_query .= " AND cf.fund_date >= '" . $conn->real_escape_string($date_from) . "'";
}

if (!empty($date_to)) {
    $all_cash_funds_query .= " AND cf.fund_date <= '" . $conn->real_escape_string($date_to) . "'";
}

$all_cash_funds_query .= " ORDER BY cf.fund_date ASC, cf.id ASC";

$stmt_all_cf = $conn->prepare($all_cash_funds_query);
$stmt_all_cf->bind_param("s", $branch_name);
$stmt_all_cf->execute();
$all_cash_funds_result = $stmt_all_cf->get_result();
$stmt_all_cf->close();

// Prepare statements with branch_name parameter
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();

$stmt = $conn->prepare($count_query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$count_result = $stmt->get_result();
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);
$stmt->close();

// Build query for expenses with all filters - exclude Cash Fund category
$query = "SELECT e.*, u.username FROM expenses e 
          LEFT JOIN users u ON e.created_by = u.id 
          WHERE e.branch_name = ? AND e.category != 'Cash Fund'";

$count_query = "SELECT COUNT(*) as total FROM expenses e 
                LEFT JOIN users u ON e.created_by = u.id 
                WHERE e.branch_name = ? AND e.category != 'Cash Fund'";

// Add search condition
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (e.description LIKE '%" . $conn->real_escape_string($search) . "%' 
               OR e.category LIKE '%" . $conn->real_escape_string($search) . "%')";
    $count_query .= " AND (e.description LIKE '%" . $conn->real_escape_string($search) . "%' 
                    OR e.category LIKE '%" . $conn->real_escape_string($search) . "%')";
}

// Add category filter
if (!empty($category_filter)) {
    $query .= " AND e.category = '" . $conn->real_escape_string($category_filter) . "'";
    $count_query .= " AND e.category = '" . $conn->real_escape_string($category_filter) . "'";
}

// Add date range filter
if (!empty($date_from)) {
    $query .= " AND e.expense_date >= '" . $conn->real_escape_string($date_from) . "'";
    $count_query .= " AND e.expense_date >= '" . $conn->real_escape_string($date_from) . "'";
}

if (!empty($date_to)) {
    $query .= " AND e.expense_date <= '" . $conn->real_escape_string($date_to) . "'";
    $count_query .= " AND e.expense_date <= '" . $conn->real_escape_string($date_to) . "'";
}

// Get total count for pagination
$stmt = $conn->prepare($count_query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$count_result = $stmt->get_result();
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);
$stmt->close();

// Get total expenses amount - exclude Cash Fund category
$total_query = "SELECT SUM(amount) as total_amount FROM expenses e WHERE e.branch_name = ? AND e.category != 'Cash Fund'";

// Add filters to total query
if (!empty($search)) {
    $total_query .= " AND (e.description LIKE '%" . $conn->real_escape_string($search) . "%' 
                    OR e.category LIKE '%" . $conn->real_escape_string($search) . "%')";
}

if (!empty($category_filter)) {
    $total_query .= " AND e.category = '" . $conn->real_escape_string($category_filter) . "'";
}

if (!empty($date_from)) {
    $total_query .= " AND e.expense_date >= '" . $conn->real_escape_string($date_from) . "'";
}

if (!empty($date_to)) {
    $total_query .= " AND e.expense_date <= '" . $conn->real_escape_string($date_to) . "'";
}

$stmt = $conn->prepare($total_query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$total_result = $stmt->get_result();
$total_amount = $total_result->fetch_assoc()['total_amount'] ?: 0;
$stmt->close();

// Add sorting
if ($show_report) {
    // For reports, always sort by date ascending to show proper flow
    $query .= " ORDER BY e.expense_date ASC, e.id ASC";
} else {
    $query .= " ORDER BY e." . $conn->real_escape_string($sort_by) . " " . $conn->real_escape_string($sort_order);
}

// Add pagination
$offset = ($page - 1) * $items_per_page;
$query .= " LIMIT $items_per_page OFFSET $offset";

// Execute query
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();

// Process form submission for adding/editing expense
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_expense'])) {
    $expense_id = isset($_POST['expense_id']) ? $_POST['expense_id'] : '';
    $description = trim($_POST['description']);
    $amount = (float)$_POST['amount'];
    $expense_date = $_POST['expense_date'];
    $category = trim($_POST['category']);
    $created_by = $_SESSION['user_id'];
    $branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';
    $payee = isset($_POST['payee']) ? trim($_POST['payee']) : '';
    
    // Validate form data
    $errors = [];
    
    if (empty($description)) {
        $errors[] = "Description is required";
    }
    
    if ($amount <= 0) {
        $errors[] = "Amount must be greater than zero";
    }
    
    if (empty($expense_date)) {
        $errors[] = "Date is required";
    }
    
    if (empty($category)) {
        $errors[] = "Category is required";
    }
    
    if (empty($payee)) {
        $errors[] = "Payee is required";
    }
    
    // If no errors, save expense
    if (empty($errors)) {
        if (!empty($expense_id)) {
            // Update existing expense
            $query = "UPDATE expenses SET 
                      description = ?, 
                      amount = ?, 
                      expense_date = ?, 
                      category = ?,
                      branch_name = ?,
                      payee = ?
                      WHERE id = ?";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sdssssi", $description, $amount, $expense_date, $category, $branch_name, $payee, $expense_id);
            
            if ($stmt->execute()) {
                $success_message = "Expense updated successfully";
            } else {
                $errors[] = "Error updating expense: " . $conn->error;
            }
        } else {
            // Insert new expense
            $query = "INSERT INTO expenses (description, amount, expense_date, category, branch_name, created_by, payee) 
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sdsssis", $description, $amount, $expense_date, $category, $branch_name, $created_by, $payee);
            
            if ($stmt->execute()) {
                $success_message = "Expense added successfully";
            } else {
                $errors[] = "Error adding expense: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Process form submission for adding/editing cash fund
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_cash_fund'])) {
    $fund_id = isset($_POST['fund_id']) ? $_POST['fund_id'] : '';
    $description = trim($_POST['description']);
    $amount = (float)$_POST['amount'];
    $fund_date = $_POST['fund_date'];
    $created_by = $_SESSION['user_id'];
    $branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';
    
    // Validate form data
    $errors = [];
    
    if (empty($description)) {
        $errors[] = "Description is required";
    }
    
    if ($amount <= 0) {
        $errors[] = "Amount must be greater than zero";
    }
    
    if (empty($fund_date)) {
        $errors[] = "Date is required";
    }
    
    // If no errors, save cash fund
    if (empty($errors)) {
        if (!empty($fund_id)) {
            // Update existing cash fund
            $query = "UPDATE cash_funds SET 
                      description = ?, 
                      amount = ?, 
                      fund_date = ?,
                      branch_name = ? 
                      WHERE id = ?";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sdssi", $description, $amount, $fund_date, $branch_name, $fund_id);
            
            if ($stmt->execute()) {
                $success_message = "Cash fund updated successfully";
            } else {
                $errors[] = "Error updating cash fund: " . $conn->error;
            }
        } else {
            // Insert new cash fund
            $query = "INSERT INTO cash_funds (description, amount, fund_date, branch_name, created_by) 
                      VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sdssi", $description, $amount, $fund_date, $branch_name, $created_by);
            
            if ($stmt->execute()) {
                $success_message = "Cash fund added successfully";
            } else {
                $errors[] = "Error adding cash fund: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Process delete request for expense
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_expense'])) {
    $expense_id = $_POST['expense_id'];
    
    // Delete expense
    $delete_query = "DELETE FROM expenses WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $expense_id);
    
    if ($stmt->execute()) {
        // Redirect to refresh the page
        header("Location: expenses.php?deleted=1");
        exit();
    } else {
        $error_message = "Error deleting expense: " . $conn->error;
    }
    
    $stmt->close();
}

// Process delete request for cash fund
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_cash_fund'])) {
    $fund_id = $_POST['fund_id'];
    
    // Delete cash fund
    $delete_query = "DELETE FROM cash_funds WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $fund_id);
    
    if ($stmt->execute()) {
        // Redirect to refresh the page
        header("Location: expenses.php?deleted=2");
        exit();
    } else {
        $error_message = "Error deleting cash fund: " . $conn->error;
    }
    
    $stmt->close();
}

// Include header after all potential redirects
require_once 'includes/header.php';

// Close connection - moved to end of file to ensure all database operations complete
// closeDB($conn); // Will close at the end of the file
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Expenses</h1>
        <div>
            <?php if (!$show_report): ?>
                <a href="?report=1<?php echo (!empty($date_from) ? '&date_from=' . urlencode($date_from) : ''); ?><?php echo (!empty($date_to) ? '&date_to=' . urlencode($date_to) : ''); ?>" class="btn btn-info me-2">
                    <i class="fas fa-file-alt me-2"></i> View Report
                </a>
            <?php else: ?>
                <a href="expenses.php" class="btn btn-secondary me-2">
                    <i class="fas fa-list me-2"></i> Back to List
                </a>
                <button onclick="window.print()" class="btn btn-success me-2">
                    <i class="fas fa-print me-2"></i> Print Report
                </button>
            <?php endif; ?>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                <i class="fas fa-plus me-2"></i> Add New Expense
            </button>
            <button type="button" class="btn btn-success ms-2" data-bs-toggle="modal" data-bs-target="#addCashFundModal">
                <i class="fas fa-money-bill-wave me-2"></i> Add Cash Fund
            </button>
           
        </div>
    </div>
    
    <?php if (isset($_GET['deleted']) && $_GET['deleted'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            Expense has been deleted successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php elseif (isset($_GET['deleted']) && $_GET['deleted'] == 2): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            Cash fund has been deleted successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="expenses.php" class="row g-3">
                <div class="col-md-3">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search expenses..." name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="category">
                        <option value="">All Categories</option>
                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($category['category']); ?>" <?php echo ($category_filter == $category['category']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['category']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>" placeholder="From Date">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>" placeholder="To Date">
                </div>
                <div class="col-md-2">
                    <div class="input-group">
                        <select class="form-select" name="sort_by">
                            <option value="expense_date" <?php echo ($sort_by == 'expense_date') ? 'selected' : ''; ?>>Date</option>
                            <option value="amount" <?php echo ($sort_by == 'amount') ? 'selected' : ''; ?>>Amount</option>
                            <option value="category" <?php echo ($sort_by == 'category') ? 'selected' : ''; ?>>Category</option>
                        </select>
                        <select class="form-select" name="sort_order">
                            <option value="DESC" <?php echo ($sort_order == 'DESC') ? 'selected' : ''; ?>>Descending</option>
                            <option value="ASC" <?php echo ($sort_order == 'ASC') ? 'selected' : ''; ?>>Ascending</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
                <input type="hidden" name="branch_name" value="<?php echo htmlspecialchars($branch_name); ?>">
            </form>
        </div>
    </div>
    
    <!-- Total Expenses Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Total Expenses</h6>
                    <h3 class="mb-0">₱<?php echo number_format($total_amount, 2); ?></h3>
                </div>
                <div class="text-end">
                    <p class="text-muted mb-0"><?php echo $total_items; ?> expense records found</p>
                    <?php if (!empty($search) || !empty($category_filter) || !empty($date_from) || !empty($date_to)): ?>
                        <a href="expenses.php" class="btn btn-sm btn-outline-secondary mt-2">Clear all filters</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Expenses Table or Report -->
    <div class="card">
        <div class="card-body">
            <?php if ($show_report): ?>
                <!-- Expenses Report View -->
                <div data-branch="<?php echo htmlspecialchars($branch_name); ?>" data-date-range="<?php echo (!empty($date_from) || !empty($date_to)) ? ($date_from ? date('M d, Y', strtotime($date_from)) : '') . ($date_from && $date_to ? ' to ' : '') . ($date_to ? date('M d, Y', strtotime($date_to)) : '') : ''; ?>">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr class="bg-light">
                                <th>Date</th>
                                <th>Description</th>
                                <th>Category</th>
                                <th class="text-end">Amount</th>
                                <th class="text-end">Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $running_balance = 0;
                            $total_expenses = 0;
                            $beginning_balance = 0;
                            $has_cash_fund = false;
                            $cash_fund_date = null;
                            
                            // Get the most recent cash fund for beginning balance
                            if ($latest_cash_fund) {
                                $beginning_balance = $latest_cash_fund['amount'];
                                $has_cash_fund = true;
                                $cash_fund_date = $latest_cash_fund['fund_date'];
                                $cash_fund_id = $latest_cash_fund['id'];
                                $running_balance = $beginning_balance;
                                
                                // Display beginning balance row
                                ?>
                                <tr class="table-primary">
                                    <td><?php echo date('M d, Y', strtotime($cash_fund_date)); ?></td>
                                    <td><strong>Beginning Balance</strong></td>
                                    <td>Cash Fund</td>
                                    <td class="text-end">₱<?php echo number_format($beginning_balance, 2); ?></td>
                                    <td class="text-end">₱<?php echo number_format($running_balance, 2); ?></td>
                                </tr>
                                <?php
                            }
                            
                            // Display all cash funds except the beginning balance one
                            if ($all_cash_funds_result->num_rows > 0) {
                                $all_cash_funds_result->data_seek(0);
                                while ($cash_fund = $all_cash_funds_result->fetch_assoc()) {
                                    // Skip the cash fund entry we already displayed as beginning balance
                                    if ($has_cash_fund && $cash_fund['id'] == $cash_fund_id) {
                                        continue;
                                    }
                                    
                                    // Add to balance
                                    $running_balance += $cash_fund['amount'];
                                    ?>
                                    <tr>
                                        <td><?php echo date('M d, Y', strtotime($cash_fund['fund_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($cash_fund['description']); ?></td>
                                        <td>
                                            <span class="badge bg-success">Cash Fund</span>
                                        </td>
                                        <td class="text-end">
                                            <span class="text-success">+₱<?php echo number_format($cash_fund['amount'], 2); ?></span>
                                        </td>
                                        <td class="text-end">₱<?php echo number_format($running_balance, 2); ?></td>
                                    </tr>
                                    <?php
                                }
                            }
                            
                            // Display all expenses
                            if ($result->num_rows > 0) {
                                while ($expense = $result->fetch_assoc()) {
                                    // Subtract from balance
                                    $running_balance -= $expense['amount'];
                                    $total_expenses += $expense['amount'];
                                    ?>
                                    <tr>
                                        <td><?php echo date('M d, Y', strtotime($expense['expense_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($expense['description']); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($expense['category']); ?>
                                            </span>
                                        </td>
                                        <td class="text-end">
                                            <span class="text-danger">-₱<?php echo number_format($expense['amount'], 2); ?></span>
                                        </td>
                                        <td class="text-end">₱<?php echo number_format($running_balance, 2); ?></td>
                                    </tr>
                                    <?php
                                }
                            }
                            
                            // Display summary row
                            ?>
                            <tr class="table-secondary">
                                <td colspan="3"><strong>Total Expenses</strong></td>
                                <td class="text-end text-danger">-₱<?php echo number_format($total_expenses, 2); ?></td>
                                <td></td>
                            </tr>
                            <tr class="table-success">
                                <td colspan="3"><strong>Ending Balance</strong></td>
                                <td></td>
                                <td class="text-end"><strong>₱<?php echo number_format($running_balance, 2); ?></strong></td>
                            </tr>
                            <?php if ($beginning_balance > 0): ?>
                            <tr class="table-info">
                                <td colspan="3"><strong>Summary</strong></td>
                                <td class="text-end"><strong>Beginning: ₱<?php echo number_format($beginning_balance, 2); ?></strong></td>
                                <td class="text-end"><strong>Ending: ₱<?php echo number_format($running_balance, 2); ?></strong></td>
                            </tr>
                            <?php endif; ?>
                            <?php
                            if ($result->num_rows > 0 || $all_cash_funds_result->num_rows > 0) {
                                // Content already displayed above
                            } else {
                                ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                            <h5>No expenses found for this period</h5>
                                            <p class="text-muted">Try adjusting your date range to find expenses.</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <!-- Regular Expenses Table View -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>Category</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Created By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($result->num_rows > 0): ?>
                                <?php while ($expense = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($expense['description']); ?></td>
                                        <td>
                                            <span class="badge <?php echo ($expense['category'] === 'Cash Fund') ? 'bg-success' : 'bg-info'; ?>">
                                                <?php echo htmlspecialchars($expense['category']); ?>
                                            </span>
                                        </td>
                                        <td>₱<?php echo number_format($expense['amount'], 2); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($expense['expense_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($expense['username'] ?? 'Unknown'); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editExpenseModal"
                                                    data-id="<?php echo $expense['id']; ?>"
                                                    data-description="<?php echo htmlspecialchars($expense['description']); ?>"
                                                    data-amount="<?php echo $expense['amount']; ?>"
                                                    data-date="<?php echo $expense['expense_date']; ?>"
                                                    data-category="<?php echo htmlspecialchars($expense['category']); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteExpenseModal"
                                                    data-id="<?php echo $expense['id']; ?>"
                                                    data-description="<?php echo htmlspecialchars($expense['description']); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                            <h5>No expenses found</h5>
                                            <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                            <?php if (!empty($search) || !empty($category_filter) || !empty($date_from) || !empty($date_to)): ?>
                                                <a href="expenses.php" class="btn btn-outline-primary mt-2">Clear all filters</a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>&branch_name=<?php echo urlencode($branch_name); ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>&branch_name=<?php echo urlencode($branch_name); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>&branch_name=<?php echo urlencode($branch_name); ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Add Expense Modal -->
    <div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addExpenseModalLabel">Add New Expense</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="expenses.php">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="description" name="description" required>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="expense_date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="expense_date" name="expense_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category" list="category-list" required>
                            <datalist id="category-list">
                                <?php 
                                $categories_result->data_seek(0); // Reset result pointer
                                while ($category = $categories_result->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo htmlspecialchars($category['category']); ?>">
                                <?php endwhile; ?>
                                <option value="Cash Fund">
                                <option value="Rent">
                                <option value="Utilities">
                                <option value="Salaries">
                                <option value="Inventory">
                                <option value="Marketing">
                                <option value="Maintenance">
                                <option value="Office Supplies">
                                <option value="Transporation">
                                <option value="Miscellaneous">
                            </datalist>
                        </div>
                        <div class="mb-3">
                            <label for="payee" class="form-label">Payee</label>
                            <input type="text" class="form-control" id="payee" name="payee" required>
                        </div>
                        <input type="hidden" name="branch_name" value="<?php echo htmlspecialchars($branch_name); ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="save_expense" class="btn btn-primary">Save Expense</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Add Cash Fund Modal -->
    <div class="modal fade" id="addCashFundModal" tabindex="-1" aria-labelledby="addCashFundModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCashFundModalLabel">Add Cash Fund</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="expenses.php">
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Cash Fund entries represent money added to the expense fund. The latest cash fund will be used as the beginning balance for expense reports.
                        </div>
                        <?php if ($latest_cash_fund): ?>
                        <div class="card mb-3 bg-light">
                            <div class="card-body">
                                <h6 class="mb-0">Current Balance</h6>
                                <h3 class="mb-0 text-success">₱<?php echo number_format($latest_cash_fund['amount'], 2); ?></h3>
                                <small class="text-muted">as of <?php echo date('M d, Y', strtotime($latest_cash_fund['fund_date'])); ?></small>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="mb-3">
                            <label for="cf_description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="cf_description" name="description" value="Cash Fund" required>
                        </div>
                        <div class="mb-3">
                            <label for="cf_amount" class="form-label">Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="cf_amount" name="amount" step="0.01" min="0.01" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="cf_fund_date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="cf_fund_date" name="fund_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <input type="hidden" name="branch_name" value="<?php echo htmlspecialchars($branch_name); ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="save_cash_fund" class="btn btn-success">Update Cash Fund</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Edit Expense Modal -->
    <div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editExpenseModalLabel">Edit Expense</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="expenses.php">
                    <div class="modal-body">
                        <input type="hidden" id="edit_expense_id" name="expense_id">
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="edit_description" name="description" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_amount" class="form-label">Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="edit_amount" name="amount" step="0.01" min="0.01" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_expense_date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="edit_expense_date" name="expense_date" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="edit_category" name="category" list="edit-category-list" required>
                            <datalist id="edit-category-list">
                                <?php 
                                $categories_result->data_seek(0); // Reset result pointer
                                while ($category = $categories_result->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo htmlspecialchars($category['category']); ?>">
                                <?php endwhile; ?>
                                <option value="Cash Fund">
                                <option value="Rent">
                                <option value="Utilities">
                                <option value="Salaries">
                                <option value="Inventory">
                                <option value="Marketing">
                                <option value="Maintenance">
                                <option value="Office Supplies">
                                <option value="Miscellaneous">
                            </datalist>
                        </div>
                        <input type="hidden" name="branch_name" value="<?php echo htmlspecialchars($branch_name); ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="save_expense" class="btn btn-primary">Update Expense</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Expense Modal -->
    <div class="modal fade" id="deleteExpenseModal" tabindex="-1" aria-labelledby="deleteExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteExpenseModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete the expense <strong><span id="delete_expense_description"></span></strong>? This action cannot be undone.
                </div>
                <form method="post" action="expenses.php">
                    <input type="hidden" id="delete_expense_id" name="expense_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="delete_expense" class="btn btn-danger">Delete</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

<style>
    @media print {
        /* Hide elements not needed in print version */
        .navbar, .sidebar, .footer, .btn, form, .modal, .page-item,
        input[type="search"], select, .card-body form, .alert {
            display: none !important;
        }
        
        /* Ensure the report takes full width */
        .container-fluid, .card, .card-body, .table-responsive {
            width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            border: none !important;
            box-shadow: none !important;
        }
        
        /* Add page title and date - only to the first card-body that contains the report */
        .card:first-of-type .card-body::before {
            content: "Expenses Report - " attr(data-branch);
            display: block;
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        
        /* Format table for better print layout */
        table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        
        th, td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }
        
        /* Ensure page breaks don't split rows */
        tr {
            page-break-inside: avoid !important;
        }
        
        /* Format summary rows */
        .table-primary, .table-secondary, .table-success {
            background-color: #f8f9fa !important;
            font-weight: bold !important;
        }
    }
</style>

<script>
    $(document).ready(function() {
        // Script to populate edit modal with expense data
        const editExpenseModal = document.getElementById('editExpenseModal');
        if (editExpenseModal) {
            editExpenseModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const expenseId = button.getAttribute('data-id');
                const description = button.getAttribute('data-description');
                const amount = button.getAttribute('data-amount');
                const date = button.getAttribute('data-date');
                const category = button.getAttribute('data-category');
                
                const modal = this;
                modal.querySelector('#edit_expense_id').value = expenseId;
                modal.querySelector('#edit_description').value = description;
                modal.querySelector('#edit_amount').value = amount;
                modal.querySelector('#edit_expense_date').value = date;
                modal.querySelector('#edit_category').value = category;
                
                // Disable category field if it's a Cash Fund entry
                const categoryField = modal.querySelector('#edit_category');
                if (category === 'Cash Fund') {
                    categoryField.setAttribute('readonly', 'readonly');
                } else {
                    categoryField.removeAttribute('readonly');
                }
            });
        }
        
        // Script to populate delete modal with expense data
        const deleteExpenseModal = document.getElementById('deleteExpenseModal');
        if (deleteExpenseModal) {
            deleteExpenseModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const expenseId = button.getAttribute('data-id');
                const description = button.getAttribute('data-description');
                
                const modal = this;
                modal.querySelector('#delete_expense_id').value = expenseId;
                modal.querySelector('#delete_expense_description').textContent = description;
            });
        }
        
        // Script to handle cash fund modal submission
        const addCashFundModal = document.getElementById('addCashFundModal');
        if (addCashFundModal) {
            const cashFundForm = addCashFundModal.querySelector('form');
            cashFundForm.addEventListener('submit', function() {
                // Store the form data to use after page reload
                const amount = document.getElementById('cf_amount').value;
                localStorage.setItem('lastCashFundAmount', amount);
                localStorage.setItem('cashFundAdded', 'true');
            });
        }
        
        // Check if we just added a cash fund and show a toast notification
        if (localStorage.getItem('cashFundAdded') === 'true') {
            const amount = localStorage.getItem('lastCashFundAmount');
            // Create and show toast
            const toastHTML = `
                <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1100">
                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header bg-success text-white">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong class="me-auto">Cash Fund Updated</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            Added ₱${parseFloat(amount).toLocaleString()} to Cash Fund successfully.
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', toastHTML);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const toast = document.querySelector('.toast');
                if (toast) {
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.hide();
                }
            }, 5000);
            
            // Clear the flag
            localStorage.removeItem('cashFundAdded');
            localStorage.removeItem('lastCashFundAmount');
        }
    });
</script>

<?php 
// Close the database connection at the end of the file
closeDB($conn);

require_once 'includes/footer.php'; 
?>
