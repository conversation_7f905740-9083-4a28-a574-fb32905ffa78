/* Main Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #212529;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #adb5bd;
    padding: 0.75rem 1rem;
    margin-bottom: 0.2rem;
    border-radius: 0.25rem;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: #0d6efd;
}

.sidebar .nav-link i {
    margin-right: 4px;
    color: #adb5bd;
}

.sidebar .nav-link.active i {
    color: #fff;
}

/* Dashboard Cards */
.card-dashboard {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease;
}

.card-dashboard:hover {
    transform: translateY(-5px);
}

.card-dashboard .card-body {
    padding: 1.5rem;
}

.card-dashboard .icon-shape {
    width: 48px;
    height: 48px;
    background-color: rgba(13, 110, 253, 0.1);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-dashboard .icon-shape i {
    font-size: 1.5rem;
    color: #0d6efd;
}

.card-dashboard.card-revenue .icon-shape {
    background-color: rgba(25, 135, 84, 0.1);
}

.card-dashboard.card-revenue .icon-shape i {
    color: #198754;
}

.card-dashboard.card-orders .icon-shape {
    background-color: rgba(13, 110, 253, 0.1);
}

.card-dashboard.card-orders .icon-shape i {
    color: #0d6efd;
}

.card-dashboard.card-expenses .icon-shape {
    background-color: rgba(220, 53, 69, 0.1);
}

.card-dashboard.card-expenses .icon-shape i {
    color: #dc3545;
}

.card-dashboard.card-alerts .icon-shape {
    background-color: rgba(255, 193, 7, 0.1);
}

.card-dashboard.card-alerts .icon-shape i {
    color: #ffc107;
}

/* Tables */
.table-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
}

.table-container .table {
    margin-bottom: 0;
}

.table-container .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* Alerts */
.alert-low-stock {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #664d03;
}

/* Mobile Responsive */
@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: -100%;
        z-index: 1000;
        width: 80% !important;
        max-width: 300px;
        transition: all 0.3s;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    main {
        width: 100% !important;
        margin-left: 0 !important;
    }
}

/* Charts */
.chart-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Forms */
.form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-container .form-label {
    font-weight: 500;
}

/* Status badges */
.badge-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge-processing {
    background-color: #0dcaf0;
    color: #212529;
}

.badge-completed {
    background-color: #198754;
    color: #fff;
}

.badge-cancelled {
    background-color: #dc3545;
    color: #fff;
}

/* Windows-style Toolbar */
.windows-toolbar {
    border: 2px solid;
    border-color: #f8f9fa #6c757d #6c757d #f8f9fa;
    background-color: #e9ecef;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
}

.windows-toolbar-header {
    background-color: #1a4b8e;
    color: white;
    padding: 5px 10px;
    font-weight: bold;
    border-bottom: 1px solid #0d3b6f;
    display: flex;
    align-items: center;
}

.windows-toolbar-body {
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

/* Windows 3D Button Style - Blue Gray Theme */
.windows-3d-btn {
    background-color: #e9ecef;
    color: #1a4b8e;
    border: 2px solid;
    border-color: #f8f9fa #6c757d #6c757d #f8f9fa;
    border-radius: 0;
    font-weight: normal;
    padding: 6px 12px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    box-shadow: 1px 1px 0px #495057;
    transition: all 0.1s;
}

.windows-3d-btn:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
    text-decoration: none;
}

.windows-3d-btn:active {
    border-color: #6c757d #f8f9fa #f8f9fa #6c757d;
    box-shadow: inset 1px 1px 2px #495057;
    transform: translateY(1px);
}

/* Modern Toolbar Styles - Simplified */
.modern-toolbar {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-toolbar:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.modern-toolbar-body {
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    justify-content: center;
}

.modern-action-btn {
    padding: 12px 20px;
    border-radius: 8px;
    background-color: #f8f9fa;
    color: #1a4b8e;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    text-decoration: none;
}

.modern-action-btn:hover {
    background-color: #e9ecef;
    color: #0d6efd;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modern-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-action-btn i {
    font-size: 16px;
}
