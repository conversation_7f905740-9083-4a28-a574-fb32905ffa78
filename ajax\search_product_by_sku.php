<?php
require_once '../config/database.php';

// Get database connection
$conn = connectDB();

// Check if SKU is provided
if (!isset($_GET['sku']) || empty($_GET['sku'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'No SKU provided']);
    exit;
}

$sku = trim($_GET['sku']);
$branch_name = $_SESSION['branch_name'] ?? 'Main Branch';

// Prepare query to search for product by SKU in the current branch
$query = "SELECT p.*, c.name as category_name, b.name as brand_name
          FROM products p
          LEFT JOIN categories c ON p.category_id = c.id
          LEFT JOIN brands b ON p.brand_id = b.id
          WHERE p.sku = ? AND p.branch_name = ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $sku, $branch_name);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $product = $result->fetch_assoc();
    
    // Format the response
    $response = [
        'success' => true,
        'product' => [
            'id' => $product['id'],
            'name' => $product['name'],
            'sku' => $product['sku'],
            'price' => $product['unit_price'],
            'quantity' => $product['quantity'],
            'category' => $product['category_name'] ?? 'Uncategorized',
            'brand' => $product['brand_name'] ?? 'Unbranded',
            'image_url' => $product['image_url'] ?? null
        ]
    ];
} else {
    $response = [
        'success' => false,
        'message' => 'No product found with the provided SKU'
    ];
}

// Close connection
$stmt->close();
$conn->close();

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit;