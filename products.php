<?php
// Start session if not already started (must be before any output)
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

// --- Handle POST requests BEFORE any HTML output ---
$conn = connectDB(); // Need connection early for POST handling

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_product'])) {
    $product_id = $_POST['product_id'];

    // Delete product
    $delete_query = "DELETE FROM products WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $product_id);

    if ($stmt->execute()) {
        // Redirect to refresh the page
        closeDB($conn); // Close connection before exiting
        header("Location: products.php?deleted=1");
        exit(); // Stop script execution after redirect
    } else {
        // Store error message in session to display after redirect/refresh
        // Or handle error differently if redirect isn't desired on failure
        $_SESSION['error_message'] = "Error deleting product: " . $conn->error;
        // Optionally redirect to show the error, or set a variable to display later
        // header("Location: products.php?delete_error=1");
        // exit();
    }
    $stmt->close();
    // Keep $conn open if not exiting, otherwise close it here too if needed.
}

// --- Now include the header ---
require_once 'includes/header.php';

// Get database connection (already connected above, ensure it's still open if not closed in POST block)
// $conn = connectDB(); // Remove this if connection is established above and not closed

// Initialize variables
$search = '';
$category_filter = '';
$sort_by = 'name';
$sort_order = 'ASC';
$page = 1;
$items_per_page = 10;

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['category'])) {
    $category_filter = $_GET['category'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Get categories for filter dropdown
$categories_query = "SELECT * FROM categories ORDER BY name ASC";
$categories_result = $conn->query($categories_query);

// Build query for products
$query = "SELECT p.*, c.name as category_name, b.name as brand_name FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          LEFT JOIN brands b ON p.brand_id = b.id 
          WHERE p.branch_name = '" . $conn->real_escape_string($branch_name) . "'";

$count_query = "SELECT COUNT(*) as total FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.branch_name = '" . $conn->real_escape_string($branch_name) . "'";

// Add search condition
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (p.name LIKE '%" . $conn->real_escape_string($search) . "%' 
               OR p.sku LIKE '%" . $conn->real_escape_string($search) . "%' 
               OR p.description LIKE '%" . $conn->real_escape_string($search) . "%')";
    $count_query .= " AND (p.name LIKE '%" . $conn->real_escape_string($search) . "%' 
                    OR p.sku LIKE '%" . $conn->real_escape_string($search) . "%' 
                    OR p.description LIKE '%" . $conn->real_escape_string($search) . "%')";
}

// Add category filter
if (!empty($category_filter)) {
    $query .= " AND p.category_id = " . $conn->real_escape_string($category_filter);
    $count_query .= " AND p.category_id = " . $conn->real_escape_string($category_filter);
}

// Get total count for pagination
$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Add sorting
$order_by_clause = '';
$sort_by_safe = $conn->real_escape_string($sort_by);
$sort_order_safe = $conn->real_escape_string($sort_order);

switch ($sort_by_safe) {
    case 'low_stock':
        // Prioritize low stock items (quantity > 0 and <= reorder_level), then sort by quantity based on selected order
        $order_by_clause = "CASE WHEN quantity > 0 AND quantity <= reorder_level THEN 0 ELSE 1 END ASC, quantity " . $sort_order_safe;
        break;
    case 'out_of_stock':
        // Prioritize out of stock items (quantity = 0), then sort by name based on selected order
        $order_by_clause = "CASE WHEN quantity = 0 THEN 0 ELSE 1 END ASC, name " . $sort_order_safe;
        break;
    case 'name':
    case 'sku':
    case 'quantity':
    case 'unit_price':
        // Standard column sorting
        $order_by_clause = $sort_by_safe . " " . $sort_order_safe;
        break;
    default:
        // Default sort if value is unexpected
        $order_by_clause = "name ASC";
}

$query .= " ORDER BY " . $order_by_clause;

// Add pagination
$offset = ($page - 1) * $items_per_page;
$query .= " LIMIT $items_per_page OFFSET $offset";

// Execute query
$result = $conn->query($query);

// Close connection at the very end of the script if it wasn't closed earlier
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Products</h1>
        <div>
            <button type="button" id="printProductListBtn" class="btn btn-secondary me-2">
                <i class="fas fa-print me-2"></i> Print Current List
            </button>
            <a href="product_form.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i> Add New Product
            </a>
        </div>
    </div>    <?php
        // Display error message stored in session (if any)
        if (isset($_SESSION['error_message'])) {
            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            echo htmlspecialchars($_SESSION['error_message']);
            echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
            echo '</div>';
            unset($_SESSION['error_message']); // Clear the message after displaying
        }
    ?>

    <?php if (isset($_GET['deleted']) && $_GET['deleted'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            Product has been deleted successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php /* Remove the old error message display block if you are using the session-based one above
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    */ ?>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="products.php" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search products..." name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category">
                        <option value="">All Categories</option>
                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_filter == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <select class="form-select" name="sort_by">
                            <option value="name" <?php echo ($sort_by == 'name') ? 'selected' : ''; ?>>Name</option>
                            <option value="sku" <?php echo ($sort_by == 'sku') ? 'selected' : ''; ?>>SKU</option>
                            <option value="quantity" <?php echo ($sort_by == 'quantity') ? 'selected' : ''; ?>>Quantity</option>
                            <option value="unit_price" <?php echo ($sort_by == 'unit_price') ? 'selected' : ''; ?>>Price</option>
                            <option value="low_stock" <?php echo ($sort_by == 'low_stock') ? 'selected' : ''; ?>>Low Stock</option>
                            <option value="out_of_stock" <?php echo ($sort_by == 'out_of_stock') ? 'selected' : ''; ?>>Out of Stock</option>
                        </select>
                        <select class="form-select" name="sort_order">
                            <option value="ASC" <?php echo ($sort_order == 'ASC') ? 'selected' : ''; ?>>Ascending</option>
                            <option value="DESC" <?php echo ($sort_order == 'DESC') ? 'selected' : ''; ?>>Descending</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Products Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>SKU</th>
                            <th>Category</th>
                            <th>Brand</th>
                            
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($product = $result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($product['image_url'])): ?>
                                            <img src="uploads/products/<?php echo $product['image_url']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-box text-secondary"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                    <td><?php echo htmlspecialchars($product['sku']); ?></td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></td>
                                    <td><?php echo htmlspecialchars($product['brand_name'] ?? 'No Brand'); ?></td>
                                    
                                    <td><?php echo $product['quantity']; ?></td>
                                    <td>₱<?php echo number_format($product['unit_price'], 2); ?></td>
                                    <td>
                                        <?php if ($product['quantity'] == 0): ?>
                                            <span class="badge bg-danger">Out of Stock</span>
                                        <?php elseif ($product['quantity'] <= $product['reorder_level']): ?>
                                            <span class="badge bg-warning text-dark">Low Stock</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">In Stock</span>
                                        <?php endif; ?>
                                    </td>                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="product_view.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="product_form.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $product['id']; ?>" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?php echo $product['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $product['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel<?php echo $product['id']; ?>">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Are you sure you want to delete <strong><?php echo htmlspecialchars($product['name']); ?></strong>? This action cannot be undone.
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form method="post" action="products.php">
                                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                            <button type="submit" name="delete_product" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <h5>No products found</h5>
                                        <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                        <?php if (!empty($search) || !empty($category_filter)): ?>
                                            <a href="products.php" class="btn btn-outline-primary mt-2">Clear all filters</a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        <?php
                        // Calculate range of page numbers to display (limit to 10)
                        $start_page = max(1, $page - 5);
                        $end_page = min($total_pages, $start_page + 9);
                        
                        // Adjust start_page if end_page is maxed out
                        if ($end_page == $total_pages) {
                            $start_page = max(1, $end_page - 9);
                        }
                        
                        // Show first page with ellipsis if needed
                        if ($start_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=1&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    1
                                </a>
                            </li>
                            <?php if ($start_page > 2): ?>
                                <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php 
                        // Show last page with ellipsis if needed
                        if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $total_pages; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    <?php echo $total_pages; ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer (assuming it contains JavaScript includes or is where you want the script)
require_once 'includes/footer.php';
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for the print button
    const printButton = document.getElementById('printProductListBtn');
    if (printButton) {
        printButton.addEventListener('click', function() {
            window.print(); // Trigger browser's print dialog
        });
    }

    // Initialize tooltips (if you are using Bootstrap tooltips)
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })
});
</script>