<?php
session_start();
require_once 'config/database.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: purchase_order.php');
    exit();
}

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$conn = connectDB();

try {
    // Start transaction
    $conn->begin_transaction();

    // Generate PO number (format: PO-YYYY-XXXXX)
    $year = date('Y');
    $query = "SELECT MAX(CAST(SUBSTRING_INDEX(po_number, '-', -1) AS UNSIGNED)) as last_number 
              FROM purchase_orders 
              WHERE po_number LIKE 'PO-$year-%'";
    $result = $conn->query($query);
    $row = $result->fetch_assoc();
    $last_number = $row['last_number'] ?? 0;
    $new_number = str_pad($last_number + 1, 5, '0', STR_PAD_LEFT);
    $po_number = "PO-$year-$new_number";

    // Insert purchase order
    $stmt = $conn->prepare("INSERT INTO purchase_orders (po_number, supplier_id, delivery_address, 
                           expected_delivery_date, notes, created_by, status) 
                           VALUES (?, ?, ?, ?, ?, ?, 'draft')");
    
    $stmt->bind_param("sissis", 
        $po_number,
        $_POST['supplier_id'],
        $_POST['delivery_address'],
        $_POST['expected_delivery_date'],
        $_POST['notes'],
        $_SESSION['user_id']
    );
    
    $stmt->execute();
    $po_id = $conn->insert_id;

    // Insert purchase order items
    $stmt = $conn->prepare("INSERT INTO purchase_order_items (purchase_order_id, product_id, 
                           quantity, unit_price) VALUES (?, ?, ?, ?)");
    
    $total_amount = 0;
    foreach ($_POST['product_id'] as $key => $product_id) {
        $quantity = $_POST['quantity'][$key];
        $unit_price = $_POST['unit_price'][$key];
        $total_amount += $quantity * $unit_price;

        $stmt->bind_param("iidd", 
            $po_id,
            $product_id,
            $quantity,
            $unit_price
        );
        $stmt->execute();
    }

    // Update total amount
    $stmt = $conn->prepare("UPDATE purchase_orders SET total_amount = ? WHERE id = ?");
    $stmt->bind_param("di", $total_amount, $po_id);
    $stmt->execute();

    // Commit transaction
    $conn->commit();
    
    $_SESSION['success'] = "Purchase order created successfully.";
    header('Location: purchase_order.php');
    exit();

} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    $_SESSION['error'] = "Error creating purchase order: " . $e->getMessage();
    header('Location: purchase_order.php');
    exit();
}

closeDB($conn);
?>