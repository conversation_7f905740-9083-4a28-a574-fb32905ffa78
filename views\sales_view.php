<?php require_once 'includes/header.php'; ?>

<?php
// This file assumes the following variables are set by the controller (sales.php):
// $branch_name (string)
// $products_data (array containing 'products', 'total_pages')
// $cart_data (array containing 'items', 'total')
// $page (int)
// $product_search (string|null)
// $error_message (string|null)
// $success_message (string|null) - For general messages
// $checkout_success_message (string|null) - Specifically for checkout success
// $added_message, $removed_message, $cleared_message (string|null) - For cart actions

// Extract data for easier access in the view
$products = $products_data['products'] ?? [];
$total_pages = $products_data['total_pages'] ?? 1;
$current_branch_cart_display = $cart_data['items'] ?? [];
$cart_total = $cart_data['total'] ?? 0;

?>
<div class="container-fluid py-4">
    <!-- Alert Messages -->
    <?php if (!empty($error_message)): ?>
        <?php echo getAlert('danger', $error_message); ?>
    <?php endif; ?>
    
    <?php if (!empty($success_message)): ?>
        <?php echo getAlert('success', $success_message); ?>
    <?php endif; ?>
    
    <?php if (!empty($checkout_success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle fa-2x me-3"></i>
                <div>
                    <h4 class="alert-heading">Sale Completed!</h4>
                    <p class="mb-0"><?php echo $checkout_success_message; ?></p>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Products and Cart Row -->
    <div class="row">
        <!-- Quick Actions with standard button styling -->
        <div class="col-lg-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="sales_list.php" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>Sales History
                        </a>
                        <a href="daily_sales_report.php" class="btn btn-primary">
                            <i class="fas fa-chart-bar me-2"></i>Daily Report
                        </a>
                        <a href="barcode_scanner.php" class="btn btn-primary">
                            <i class="fas fa-barcode me-2"></i>Scanner Mode
                        </a>
                        <button onclick="openCashDrawer()" class="btn btn-primary">
                            <i class="fas fa-cash-register me-2"></i>Open Drawer
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Column -->
        <div class="col-lg-7">
            <!-- Popular Products Carousel -->
            <?php if (!empty($popular_products)): ?>
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary bg-gradient text-white">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Popular Products</h5>
                </div>
                <div class="card-body p-3">
                    <div class="row">
                        <?php foreach ($popular_products as $product): ?>
                            <div class="col-md-4 col-sm-6 mb-3">
                                <div class="card h-100 product-card" style="transform: perspective(1000px) rotateX(2deg) rotateY(2deg); transition: all 0.3s ease;">
                                    <?php if (!empty($product['image_url'])): ?>
                                        <img src="uploads/products/<?php echo $product['image_url']; ?>" class="card-img-top product-image" height="120" style="object-fit: contain;" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 120px;">
                                            <i class="fas fa-box fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1"><?php echo htmlspecialchars($product['name']); ?></h6>
                                        <p class="card-text text-primary mb-1">₱<?php echo number_format($product['unit_price'], 2); ?></p>
                                        <p class="card-text small text-muted mb-2"><?php echo $product['quantity']; ?> in stock</p>
                                        <form method="POST" action="sales.php" class="d-grid">
                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                            <input type="hidden" name="quantity" value="1">
                                            <button type="submit" name="add_to_cart" class="btn btn-sm btn-outline-primary" <?php echo ($product['quantity'] <= 0) ? 'disabled' : ''; ?>>
                                                <i class="fas fa-cart-plus me-1"></i>Add to Cart
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Main Products List -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary bg-gradient text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-box-open me-2"></i>Products (<?php echo htmlspecialchars($branch_name); ?>)</h5>
                    <!-- Product Search Form -->
                    <form method="GET" action="sales.php" class="d-flex">
                        <input type="text" name="product_search" class="form-control form-control-sm me-2" placeholder="Search by Name or SKU" value="<?php echo htmlspecialchars($product_search); ?>">
                        <button type="submit" class="btn btn-light btn-sm"><i class="fas fa-search"></i></button>
                    </form>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($products)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Price</th>
                                        <th>Stock</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr class="<?php echo ($product['quantity'] <= 0) ? 'table-danger' : ''; ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($product['image_url'])): ?>
                                                        <img src="uploads/products/<?php echo $product['image_url']; ?>" class="me-2 rounded" width="40" height="40" style="object-fit: cover;" alt="">
                                                    <?php else: ?>
                                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                            <i class="fas fa-box text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($product['name']); ?></div>
                                                        <small class="text-muted"><?php echo htmlspecialchars($product['sku']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>₱<?php echo number_format($product['unit_price'], 2); ?></td>
                                            <td>
                                                <?php if ($product['quantity'] <= 0): ?>
                                                    <span class="badge bg-danger">Out of Stock</span>
                                                <?php elseif ($product['quantity'] <= $product['reorder_level']): ?>
                                                    <span class="badge bg-warning text-dark">Low Stock (<?php echo $product['quantity']; ?>)</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success"><?php echo $product['quantity']; ?> in stock</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <form method="POST" action="sales.php" class="d-inline">
                                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                    <div class="input-group input-group-sm">
                                                        <input type="number" name="quantity" class="form-control" value="1" min="1" max="<?php echo $product['quantity']; ?>" style="width: 60px;" <?php echo ($product['quantity'] <= 0) ? 'disabled' : ''; ?>>
                                                        <button type="submit" name="add_to_cart" class="btn btn-primary" <?php echo ($product['quantity'] <= 0) ? 'disabled' : ''; ?>>
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center p-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <p>No products found<?php echo !empty($product_search) ? ' matching your search' : ''; ?>.</p>
                            <?php if (!empty($product_search)): ?>
                                <a href="sales.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-undo me-1"></i>Clear Search
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-sm justify-content-center mb-0">
                            <!-- Previous page link -->
                            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                <a class="page-link" href="sales.php?page=<?php echo $page - 1; ?><?php echo !empty($product_search) ? '&product_search=' . urlencode($product_search) : ''; ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            
                            <?php
                            // Calculate range of page numbers to display (limit to 5)
                            $max_visible_pages = 5;
                            $start_page = max(1, $page - floor($max_visible_pages/2));
                            $end_page = min($total_pages, $start_page + $max_visible_pages - 1);
                            
                            // Adjust start_page if we're near the end
                            if ($end_page - $start_page + 1 < $max_visible_pages) {
                                $start_page = max(1, $end_page - $max_visible_pages + 1);
                            }
                            
                            // Show first page with ellipsis if needed
                            if ($start_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="sales.php?page=1<?php echo !empty($product_search) ? '&product_search=' . urlencode($product_search) : ''; ?>">
                                        1
                                    </a>
                                </li>
                                <?php if ($start_page > 2): ?>
                                    <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <!-- Page numbers -->
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                    <a class="page-link" href="sales.php?page=<?php echo $i; ?><?php echo !empty($product_search) ? '&product_search=' . urlencode($product_search) : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <!-- Show last page with ellipsis if needed -->
                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="sales.php?page=<?php echo $total_pages; ?><?php echo !empty($product_search) ? '&product_search=' . urlencode($product_search) : ''; ?>">
                                        <?php echo $total_pages; ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Next page link -->
                            <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                <a class="page-link" href="sales.php?page=<?php echo $page + 1; ?><?php echo !empty($product_search) ? '&product_search=' . urlencode($product_search) : ''; ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Low Stock Alerts -->
            <?php if (!empty($low_stock_products)): ?>
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Low Stock Alerts</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Stock</th>
                                    <th>Reorder Level</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($low_stock_products as $product): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></td>
                                        <td><span class="badge bg-warning text-dark"><?php echo $product['quantity']; ?></span></td>
                                        <td><?php echo $product['reorder_level']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Cart Column -->
        <div class="col-lg-5">
            <div class="card shadow-sm">
                <div class="card-header bg-success bg-gradient text-white">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Current Sale</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($current_branch_cart_display)): ?>
                        <form method="POST" action="sales.php" id="cart-form">
                            <div class="table-responsive mb-3">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Qty</th>
                                            <th>Price</th>
                                            <th>Total</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_branch_cart_display as $index => $item): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($item['image_url'])): ?>
                                                            <img src="uploads/products/<?php echo $item['image_url']; ?>" class="me-2 rounded" width="30" height="30" style="object-fit: cover;" alt="">
                                                        <?php endif; ?>
                                                        <div><?php echo htmlspecialchars($item['name']); ?></div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <input type="number" min="1" class="form-control form-control-sm cart-item-quantity"
                                                           name="updated_quantities[<?php echo $index; ?>]"
                                                           value="<?php echo $item['quantity']; ?>"
                                                           data-index="<?php echo $index; ?>"
                                                           data-price="<?php echo number_format($item['price'], 2, '.', ''); ?>"
                                                           style="width: 60px;">
                                                </td>
                                                <td>
                                                    <input type="number" step="0.01" min="0" class="form-control form-control-sm cart-item-price"
                                                           name="updated_prices[<?php echo $index; ?>]"
                                                           value="<?php echo number_format($item['price'], 2, '.', ''); ?>"
                                                           data-index="<?php echo $index; ?>"
                                                           data-quantity="<?php echo $item['quantity']; ?>"
                                                           style="width: 80px;">
                                                </td>
                                                <td class="cart-item-total">₱<?php echo number_format($item['total'], 2); ?></td>
                                                <td>
                                                    <button type="submit" name="remove_from_cart" class="btn btn-sm btn-danger" value="<?php echo $index; ?>">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-end">Total:</th>
                                            <th colspan="2" id="cart-total">₱<?php echo number_format($cart_total, 2); ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" name="update_cart" class="btn btn-info">
                                    <i class="fas fa-sync-alt me-2"></i>Update Cart
                                </button>
                                <button type="button" class="btn btn-warning" id="clear-cart-btn">
                                    <i class="fas fa-trash-alt me-2"></i>Clear Cart
                                </button>
                                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#checkoutForm">
                                    <i class="fas fa-check-circle me-2"></i>Proceed to Checkout
                                </button>
                            </div>

                            <!-- Clear Cart Confirmation Modal -->
                            <div class="modal fade" id="clearCartModal" tabindex="-1" aria-labelledby="clearCartModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="clearCartModalLabel">Confirm Clear Cart</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to clear all items from your cart?</p>
                                            <p class="text-danger"><strong>This action cannot be undone.</strong></p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form method="POST" action="sales.php">
                                                <button type="submit" name="clear_cart" class="btn btn-danger">Clear Cart</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Collapsible Checkout Form -->
                            <div class="collapse mt-3" id="checkoutForm">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">Complete Sale</h5>
                                    </div>
                                    <div class="card-body">
                                        <form method="POST" action="sales.php" id="checkout-form">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="customer_name" class="form-label">Customer/Student Name</label>
                                                        <input type="text" class="form-control" id="customer_name" name="customer_name">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="customer_email" class="form-label">Email/Teacher</label>
                                                        <input type="email" class="form-control" id="customer_email" name="customer_email">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="customer_phone" class="form-label">Phone/Lesson/Activity</label>
                                                        <input type="text" class="form-control" id="customer_phone" name="customer_phone">
                                                    </div>
                                                    <div class="mb-3 form-check">
                                                        <input type="checkbox" class="form-check-input" id="hide_receipt" name="hide_receipt" default checked>
                                                        <label class="form-check-label" for="hide_receipt">Hide receipt preview after sale</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="invoice_number" class="form-label">Invoice Number *</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" id="invoice_number" name="invoice_number" value="<?php echo $suggested_invoice_number; ?>" required>
                                                            <button class="btn btn-outline-secondary" type="button" id="refresh-invoice-btn" title="Generate new invoice number">
                                                                <i class="fas fa-sync-alt"></i>
                                                            </button>
                                                        </div>
                                                        <small class="form-text text-muted">Auto-generated from the last invoice number.</small>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="payment_type" class="form-label">Payment Method</label>
                                                        <select class="form-select" id="payment_type" name="payment_type" required>
                                                            <option value="Cash">Cash</option>
                                                            <option value="Credit Card">Credit Card</option>
                                                            <option value="Debit Card">Debit Card</option>
                                                            <option value="Cheque">Cheque</option>
                                                            <option value="Charge">Charge</option>
                                                            <option value="Bank Transfer">Bank Transfer</option>
                                                            <option value="GCash/Maya">GCash/Maya</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Total Amount</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">₱</span>
                                                            <input type="text" class="form-control" value="<?php echo number_format($cart_total, 2); ?>" readonly>
                                                            <input type="hidden" id="cart_total_input" name="cart_total" value="<?php echo number_format($cart_total, 2, '.', ''); ?>">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Hidden fields to store updated prices -->
                                            <div id="updated-prices-container" style="display: none;">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                            
                                            <div class="d-grid gap-2 mt-3">
                                                <button type="submit" name="checkout" value="1" class="btn btn-success btn-lg" id="complete-sale-btn">
                                                    <i class="fas fa-shopping-cart me-2"></i>Complete Sale
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p>Your cart is empty.</p>
                            <p class="text-muted">Add products from the list to proceed with the sale.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Branch Selector -->
            <!-- 
            <div class="card mt-4 shadow-sm">
                <div class="card-header bg-secondary bg-gradient text-white">
                    <h5 class="mb-0"><i class="fas fa-store me-2"></i>Branch Selection</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="change_branch.php" class="d-flex">
                        <select name="branch_name" class="form-select me-2">
                            <?php
                            // Get all branches
                            $branches_query = "SELECT name FROM branches ORDER BY name";
                            $branches_result = $conn->query($branches_query);
                            
                            if ($branches_result && $branches_result->num_rows > 0) {
                                while ($branch = $branches_result->fetch_assoc()) {
                                    $selected = ($branch['name'] == $branch_name) ? 'selected' : '';
                                    echo "<option value='" . htmlspecialchars($branch['name']) . "' $selected>" . htmlspecialchars($branch['name']) . "</option>";
                                }
                            } else {
                                echo "<option value='Main Branch'>Main Branch</option>";
                            }
                            ?>
                        </select>
                        <button type="submit" class="btn btn-primary">Switch</button>
                    </form>
                </div>
            </div>
            -->
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update cart totals when prices or quantities change
    const cartItemPrices = document.querySelectorAll('.cart-item-price');
    const cartItemQuantities = document.querySelectorAll('.cart-item-quantity');
    const cartTotal = document.getElementById('cart-total');
    const cartTotalInput = document.getElementById('cart_total_input');
    
    function updateCartTotal() {
        let total = 0;
        
        // Get all rows
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach((row, index) => {
            // Get price and quantity inputs for this row
            const priceInput = row.querySelector('.cart-item-price');
            const quantityInput = row.querySelector('.cart-item-quantity');
            
            if (priceInput && quantityInput) {
                const price = parseFloat(priceInput.value);
                const quantity = parseInt(quantityInput.value);
                const itemTotal = price * quantity;
                
                // Update the item total display
                const totalCell = row.querySelector('.cart-item-total');
                if (totalCell) {
                    totalCell.textContent = '₱' + itemTotal.toFixed(2);
                }
                
                total += itemTotal;
            }
        });
        
        // Update the cart total display
        if (cartTotal) {
            cartTotal.textContent = '₱' + total.toFixed(2);
        }
        
        // Update the hidden input for checkout
        if (cartTotalInput) {
            cartTotalInput.value = total.toFixed(2);
        }
    }
    
    // Add event listeners to price inputs
    if (cartItemPrices) {
        cartItemPrices.forEach(input => {
            input.addEventListener('change', updateCartTotal);
        });
    }
    
    // Add event listeners to quantity inputs
    if (cartItemQuantities) {
        cartItemQuantities.forEach(input => {
            input.addEventListener('change', updateCartTotal);
        });
    }
    
    // Generate new invoice number
    const generateInvoiceBtn = document.getElementById('generateInvoice');
    const invoiceInput = document.getElementById('invoice_number');
    
    if (generateInvoiceBtn && invoiceInput) {
        generateInvoiceBtn.addEventListener('click', function() {
            const prefix = 'INV';
            const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            const random = Math.floor(1000 + Math.random() * 9000);
            invoiceInput.value = `${prefix}-${date}-${random}`;
        });
    }
    
    // Product card hover effect
    const productCards = document.querySelectorAll('.product-card');
    
    if (productCards) {
        productCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.05)';
                this.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'perspective(1000px) rotateX(2deg) rotateY(2deg) scale(1)';
                this.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
            });
        });
    }
    
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-checkout-success)');
    
    if (alerts) {
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    }
    
    // Barcode scanner support
    let barcodeBuffer = '';
    let barcodeTimeout;
    
    document.addEventListener('keydown', function(e) {
        // Only process if not in an input field
        if (document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
            // Check if the key is a number, letter, or hyphen (common in barcodes)
            if (/^[0-9a-zA-Z\-]$/.test(e.key)) {
                barcodeBuffer += e.key;
                
                // Clear the timeout if it exists
                if (barcodeTimeout) {
                    clearTimeout(barcodeTimeout);
                }
                
                // Set a timeout to process the barcode
                barcodeTimeout = setTimeout(() => {
                    if (barcodeBuffer.length >= 5) {
                        // Search for product with this SKU
                        searchProductBySku(barcodeBuffer);
                    }
                    barcodeBuffer = '';
                }, 100);
            } else if (e.key === 'Enter' && barcodeBuffer.length >= 5) {
                // Process immediately on Enter key
                searchProductBySku(barcodeBuffer);
                barcodeBuffer = '';
                e.preventDefault();
            }
        }
    });
    
    function searchProductBySku(sku) {
        // Make an AJAX request to search for the product
        fetch(`ajax/search_product_by_sku.php?sku=${encodeURIComponent(sku)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.product) {
                    // Create a form to add the product to cart
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = 'sales.php';
                    
                    const productIdInput = document.createElement('input');
                    productIdInput.type = 'hidden';
                    productIdInput.name = 'product_id';
                    productIdInput.value = data.product.id;
                    
                    const quantityInput = document.createElement('input');
                    quantityInput.type = 'hidden';
                    quantityInput.name = 'quantity';
                    quantityInput.value = '1';
                    
                    const submitInput = document.createElement('input');
                    submitInput.type = 'hidden';
                    submitInput.name = 'add_to_cart';
                    submitInput.value = '1';
                    
                    form.appendChild(productIdInput);
                    form.appendChild(quantityInput);
                    form.appendChild(submitInput);
                    
                    document.body.appendChild(form);
                    form.submit();
                } else {
                    // Show error message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-warning alert-dismissible fade show';
                    alertDiv.setAttribute('role', 'alert');
                    alertDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No product found with SKU: ${sku}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    
                    // Insert at the top of the page
                    const container = document.querySelector('.container-fluid');
                    container.insertBefore(alertDiv, container.firstChild);
                    
                    // Auto dismiss after 3 seconds
                    setTimeout(() => {
                        const bsAlert = new bootstrap.Alert(alertDiv);
                        bsAlert.close();
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error searching for product:', error);
            });
    }
    
    // Clear cart button
    const clearCartBtn = document.getElementById('clear-cart-btn');
    const clearCartModal = new bootstrap.Modal(document.getElementById('clearCartModal'));
    
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function() {
            // Show confirmation modal
            clearCartModal.show();
        });
    }
    
    // Add event listener for the refresh invoice button
    const refreshInvoiceBtn = document.getElementById('refresh-invoice-btn');
    if (refreshInvoiceBtn) {
        refreshInvoiceBtn.addEventListener('click', function() {
            // Make an AJAX call to get a new invoice number
            fetch('api/get_next_invoice.php?branch=<?php echo urlencode($branch_name); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.invoice_number) {
                        document.getElementById('invoice_number').value = data.invoice_number;
                    }
                })
                .catch(error => {
                    console.error('Error fetching new invoice number:', error);
                    // Fallback: Generate a simple incremented number client-side
                    const currentInvoice = document.getElementById('invoice_number').value;
                    if (isNaN(currentInvoice)) {
                        // If not a number, just append "-new"
                        document.getElementById('invoice_number').value = currentInvoice + "-new";
                    } else {
                        // If numeric, increment by 1
                        document.getElementById('invoice_number').value = (parseInt(currentInvoice) + 1).toString().padStart(5, '0');
                    }
                });
        });
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>

<!-- Receipt Modal -->
<?php if ($receipt_data): ?>
<div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiptModalLabel">Receipt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body d-flex justify-content-center">
                <div id="receipt-container" class="thermal-receipt">
                    <div class="receipt-header">
                        <div class="text-center">
                            <h6 class="mb-0"><?php echo htmlspecialchars($company['name']); ?></h6>
                            <p class="small mb-0"><?php echo htmlspecialchars($company['address']); ?></p>
                            <p class="small mb-0">Tel: <?php echo htmlspecialchars($company['phone']); ?></p>
                            <p class="small mb-0"><?php echo htmlspecialchars($company['email']); ?></p>
                            <div class="receipt-title">RECEIPT</div>
                            <div class="receipt-divider">* * * * * * * * * * * * * * * * * * * *</div>
                        </div>
                    </div>
                    
                    <div class="receipt-info">
                        <div class="row g-0">
                            <div class="col-5">Invoice:</div>
                            <div class="col-7"><?php echo htmlspecialchars($receipt_data['invoice_number']); ?></div>
                        </div>
                        <div class="row g-0">
                            <div class="col-5">Date:</div>
                            <div class="col-7"><?php echo date('Y-m-d h:i A', strtotime($receipt_data['created_at'])); ?></div>
                        </div>
                        <div class="row g-0">
                            <div class="col-5">Customer:</div>
                            <div class="col-7"><?php echo htmlspecialchars($receipt_data['customer_name']); ?></div>
                        </div>
                        <div class="row g-0">
                            <div class="col-5">Payment:</div>
                            <div class="col-7"><?php echo htmlspecialchars($receipt_data['payment_type']); ?></div>
                        </div>
                        <?php if (!empty($receipt_data['customer_email'])): ?>
                        <div class="row g-0">
                            <div class="col-5">Teacher:</div>
                            <div class="col-7"><?php echo htmlspecialchars($receipt_data['customer_email']); ?></div>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($receipt_data['customer_phone'])): ?>
                        <div class="row g-0">
                            <div class="col-5">Lesson:</div>
                            <div class="col-7"><?php echo htmlspecialchars($receipt_data['customer_phone']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="receipt-divider">* * * * * * * * * * * * * * * * * * * *</div>
                    
                    <div class="receipt-items">
                        <div class="row g-0 fw-bold">
                            <div class="col-5">Item</div>
                            <div class="col-2 text-center">Qty</div>
                            <div class="col-2 text-end">Price</div>
                            <div class="col-3 text-end">Total</div>
                        </div>
                        <div class="receipt-divider">- - - - - - - - - - - - - - - - - - - -</div>
                        
                        <?php foreach ($receipt_items as $item): ?>
                        <div class="row g-0">
                            <div class="col-5"><?php echo htmlspecialchars($item['product_name']); ?></div>
                            <div class="col-2 text-center"><?php echo $item['quantity']; ?></div>
                            <div class="col-2 text-end"><?php echo number_format($item['unit_price'], 2); ?></div>
                            <div class="col-3 text-end"><?php echo number_format($item['total_price'], 2); ?></div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="receipt-divider">* * * * * * * * * * * * * * * * * * * *</div>
                    
                    <div class="receipt-total">
                        <div class="row g-0 fw-bold">
                            <div class="col-9 text-end">TOTAL:</div>
                            <div class="col-3 text-end"><?php echo number_format($receipt_data['total_amount'], 2); ?></div>
                        </div>
                    </div>
                    
                    <div class="receipt-divider">* * * * * * * * * * * * * * * * * * * *</div>
                    
                    <div class="receipt-footer text-center">
                        <p class="small mb-0">Cashier: <?php echo htmlspecialchars($receipt_data['created_by_username']); ?></p>
                        <p class="small mb-0">Branch: <?php echo htmlspecialchars($receipt_data['branch_name']); ?></p>
                        <p class="small mb-0">Thank you for your purchase!</p>
                        <p class="small mb-0"><?php echo date('Y-m-d h:i:s A'); ?></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printReceipt()">Print Receipt</button>
            </div>
        </div>
    </div>
</div>

<!-- Add CSS for the thermal receipt -->
<style>
    .thermal-receipt {
        width: 58mm;
        font-family: 'Courier New', monospace;
        background-color: white;
        padding: 5mm;
        border: 1px solid #ddd;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        font-size: 9pt;
        line-height: 1.2;
    }
    
    .receipt-header {
        margin-bottom: 3mm;
    }
    
    .receipt-title {
        font-weight: bold;
        font-size: 10pt;
        margin: 2mm 0;
    }
    
    .receipt-divider {
        margin: 2mm 0;
        font-size: 8pt;
        color: #777;
    }
    
    .receipt-info {
        margin-bottom: 3mm;
    }
    
    .receipt-items {
        margin-bottom: 3mm;
    }
    
    .receipt-total {
        margin: 3mm 0;
        font-weight: bold;
    }
    
    .receipt-footer {
        margin-top: 3mm;
        font-size: 8pt;
        color: #555;
    }
    
    @media print {
        body * {
            visibility: hidden;
        }
        
        #receipt-container, #receipt-container * {
            visibility: visible;
        }
        
        #receipt-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 58mm;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
        }
        
        .modal {
            position: absolute;
            left: 0;
            top: 0;
            margin: 0;
            padding: 0;
            overflow: visible;
        }
    }
</style>

<script>
    // Show receipt modal on page load if receipt data exists
    document.addEventListener('DOMContentLoaded', function() {
        <?php if ($receipt_data): ?>
        var receiptModal = new bootstrap.Modal(document.getElementById('receiptModal'));
        receiptModal.show();
        <?php endif; ?>
    });
    
    // Function to print receipt
    function printReceipt() {
        const receiptContent = document.getElementById('receipt-container').innerHTML;
        
        // Create a hidden iframe for printing
        const printFrame = document.createElement('iframe');
        printFrame.style.display = 'none';
        printFrame.name = 'print_frame';
        document.body.appendChild(printFrame);
        
        // Create print content
        const printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt</title>
                <style>
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 9pt;
                        line-height: 1.2;
                        margin: 0;
                        padding: 0;
                    }
                    
                    .thermal-receipt {
                        width: 58mm;
                        padding: 5mm;
                    }
                    
                    .receipt-header {
                        margin-bottom: 3mm;
                    }
                    
                    .receipt-title {
                        font-weight: bold;
                        font-size: 10pt;
                        margin: 2mm 0;
                    }
                    
                    .receipt-divider {
                        margin: 2mm 0;
                        font-size: 8pt;
                        color: #777;
                    }
                    
                    .receipt-info {
                        margin-bottom: 3mm;
                    }
                    
                    .receipt-items {
                        margin-bottom: 3mm;
                    }
                    
                    .receipt-total {
                        margin: 3mm 0;
                        font-weight: bold;
                    }
                    
                    .receipt-footer {
                        margin-top: 3mm;
                        font-size: 8pt;
                        color: #555;
                    }
                    
                    .row {
                        display: flex;
                        flex-wrap: wrap;
                    }
                    
                    .col-2 {
                        flex: 0 0 16.666667%;
                        max-width: 16.666667%;
                    }
                    
                    .col-3 {
                        flex: 0 0 25%;
                        max-width: 25%;
                    }
                    
                    .col-5 {
                        flex: 0 0 41.666667%;
                        max-width: 41.666667%;
                    }
                    
                    .col-7 {
                        flex: 0 0 58.333333%;
                        max-width: 58.333333%;
                    }
                    
                    .col-9 {
                        flex: 0 0 75%;
                        max-width: 75%;
                    }
                    
                    .g-0 {
                        margin-right: 0;
                        margin-left: 0;
                    }
                    
                    .text-center {
                        text-align: center;
                    }
                    
                    .text-end {
                        text-align: right;
                    }
                    
                    .small {
                        font-size: 8pt;
                    }
                    
                    .mb-0 {
                        margin-bottom: 0;
                    }
                    
                    .fw-bold {
                        font-weight: bold;
                    }
                    
                    @page {
                        size: 58mm auto;
                        margin: 0;
                    }
                </style>
            </head>
            <body>
                <div class="thermal-receipt">
                    ${receiptContent}
                </div>
            </body>
            </html>
        `;
        
        // Write the content to the iframe
        printFrame.contentWindow.document.open();
        printFrame.contentWindow.document.write(printContent);
        printFrame.contentWindow.document.close();
        
        // Wait for the iframe to load, then print
        printFrame.onload = function() {
            printFrame.contentWindow.focus();
            printFrame.contentWindow.print();
            
            // Remove the iframe after printing
            setTimeout(function() {
                document.body.removeChild(printFrame);
            }, 1000);
        };
    }
</script>
<?php endif; ?>

<!-- Debug Cart Contents (Only visible to admins) -->
<?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
<div class="card mt-3">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">Debug: Cart Contents</h5>
    </div>
    <div class="card-body">
        <h6>Session Cart Data:</h6>
        <pre><?php 
            echo "Branch: $branch_name\n";
            echo "Cart Items: " . count($cart_items) . "\n\n";
            echo json_encode($cart_items, JSON_PRETTY_PRINT);
        ?></pre>
        
        <h6 class="mt-3">Raw Session Cart:</h6>
        <pre><?php 
            echo json_encode($_SESSION['cart'] ?? [], JSON_PRETTY_PRINT);
        ?></pre>
    </div>
</div>
<?php endif; ?>
