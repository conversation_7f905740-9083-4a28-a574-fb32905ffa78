<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Generate transfer control number
function generateTransferNumber() {
    return 'TRF-' . date('Ymd') . '-' . sprintf('%04d', rand(1, 9999));
}

$transfer_number = generateTransferNumber();
?>

<style>    /* Custom styles for fonts */
    .card-body {
        font-size: 12px;
    }
    .form-control, .form-select {
        font-size: 12px;
    }
    .btn {
        font-size: 12px;
    }
    .form-label {
        font-size: 12px;
        margin-bottom: 0.2rem;
    }
    .table {
        font-size: 12px;
    }
    .form-control-lg, .form-select-lg {
        font-size: 12px !important;
        padding: 0.5rem !important;
    }
    .input-group-text {
        font-size: 12px;
    }
    /* Ensure footer sticks to bottom */
    .container-fluid {
        min-height: calc(100vh - 160px);
        padding-bottom: 2rem;
    }
</style>

<div class="container-fluid py-4">
    <!-- Transfer Form Card -->
    <div class="card shadow">
        <div class="card-header bg-primary bg-gradient text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Create Stock Transfer</h5>
            <a href="transfers.php" class="btn btn-light btn-sm">
                <i class="fas fa-list me-1"></i> View All Transfers
            </a>
        </div>
        <div class="card-body">
            <form id="transferForm" method="post" action="process_transfer_v2.php">
                <!-- Transfer Details Section -->
                <div class="row g-3 mb-4">
                    <div class="col-md-3">
                        <label class="form-label text-muted">Transfer Control Number</label>
                        <input type="text" class="form-control form-control-lg bg-light" 
                               value="<?php echo $transfer_number; ?>" readonly name="transfer_number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Source Branch</label>
                        <select class="form-select form-select-lg" id="sourceBranch" name="source_branch" required>
                            <option value="">Select Source Branch</option>
                            <option value="Musar Brower" <?php echo $branch_name === 'Musar Brower' ? 'selected' : ''; ?>>Musar Brower</option>
                            <option value="Musar Maharlika" <?php echo $branch_name === 'Musar Maharlika' ? 'selected' : ''; ?>>Musar Maharlika</option>
                            <option value="Musar Parking" <?php echo $branch_name === 'Musar Parking' ? 'selected' : ''; ?>>Musar Parking</option>
                            <option value="Musar SM" <?php echo $branch_name === 'Musar SM' ? 'selected' : ''; ?>>Musar SM</option>
                            <option value="Musar SM Sn Fdo" <?php echo $branch_name === 'Musar SM Sn Fdo' ? 'selected' : ''; ?>>Musar SM Sn Fdo</option>
                            <option value="Online Store" <?php echo $branch_name === 'Online Store' ? 'selected' : ''; ?>>Online Store</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Destination Branch</label>
                        <select class="form-select form-select-lg" id="destinationBranch" name="destination_branch" required>
                            <option value="">Select Destination Branch</option>
                            <option value="Musar Brower" <?php echo $branch_name === 'Musar Brower' ? 'disabled' : ''; ?>>Musar Brower</option>
                            <option value="Musar Maharlika" <?php echo $branch_name === 'Musar Maharlika' ? 'disabled' : ''; ?>>Musar Maharlika</option>
                            <option value="Musar Parking" <?php echo $branch_name === 'Musar Parking' ? 'disabled' : ''; ?>>Musar Parking</option>
                            <option value="Musar SM" <?php echo $branch_name === 'Musar SM' ? 'disabled' : ''; ?>>Musar SM</option>
                            <option value="Musar SM Sn Fdo" <?php echo $branch_name === 'Musar SM Sn Fdo' ? 'disabled' : ''; ?>>Musar SM Sn Fdo</option>
                            <option value="Online Store" <?php echo $branch_name === 'Online Store' ? 'disabled' : ''; ?>>Online Store</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label text-muted">Date</label>
                        <input type="date" class="form-control form-control-lg bg-light" 
                               value="<?php echo date('Y-m-d'); ?>" readonly name="transfer_date">
                    </div>
                </div>

                <!-- Product Search Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-primary text-white">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" id="productSearch" class="form-control" 
                                   placeholder="Search products by name, SKU, or barcode...">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Available Products Section -->
                    <div class="col-md-6">                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">Available Products</h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive" style="max-height: 400px;">
                                    <table class="table table-hover mb-0" id="availableProducts">
                                        <thead class="sticky-top bg-white">
                                            <tr>
                                                <th>Product</th>
                                                <th>SKU</th>
                                                <th class="text-center">Stock</th>
                                                <th class="text-end">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productResults">
                                            <!-- Products will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Products Section -->
                    <div class="col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Selected Products for Transfer</h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive" style="max-height: 400px;">
                                    <table class="table mb-0" id="selectedProducts">
                                        <thead class="sticky-top bg-white">
                                            <tr>
                                                <th>Product</th>
                                                <th class="text-center">Quantity</th>
                                                <th class="text-end">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Selected products will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="mt-4">
                    <label class="form-label">Notes</label>
                    <textarea class="form-control" name="notes" rows="3" 
                              placeholder="Add any additional notes about this transfer..."></textarea>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2 mt-4">
                    <button type="button" class="btn btn-light btn-lg" onclick="window.history.back()">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg" id="submitTransfer">
                        <i class="fas fa-paper-plane me-1"></i> Create Transfer
                    </button>
                </div>
            </form>        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
// Utility function to escape HTML special characters
function escapeHtml(unsafe) {
    return unsafe
        ? unsafe.toString()
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;")
        : '';
}

document.addEventListener('DOMContentLoaded', function() {
    let selectedProducts = new Map();
    const productSearch = document.getElementById('productSearch');
    const sourceBranch = document.getElementById('sourceBranch');
    const destinationBranch = document.getElementById('destinationBranch');
    const submitButton = document.getElementById('submitTransfer');

    // Load initial products from current branch
    if (sourceBranch.value) {
        fetchProducts(sourceBranch.value);
        productSearch.disabled = false;
    }

    // Initialize with current branch's products
    if (sourceBranch.value) {
        productSearch.disabled = false;
        fetchProducts(sourceBranch.value);
    }

    // Source branch change handler
    sourceBranch.addEventListener('change', function() {
        if (this.value) {
            productSearch.disabled = false;
            fetchProducts(this.value);
            
            // Update destination branch options
            Array.from(destinationBranch.options).forEach(option => {
                if (option.value) {
                    option.disabled = (option.value === this.value);
                }
            });
        } else {
            productSearch.disabled = true;
            clearProductResults();
        }
    });

    // Prevent same branch selection
    destinationBranch.addEventListener('change', function() {
        if (this.value === sourceBranch.value) {
            alert('Destination branch must be different from source branch');
            this.value = '';
        }
    });    // Product search handler with debounce
    let searchTimeout;
    productSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            fetchProducts(sourceBranch.value);
        }, 300);
    });    // Fetch products from source branch
    function fetchProducts(branch) {
        if (!branch) return;
        
        const tbody = document.getElementById('productResults');
        tbody.innerHTML = '<tr><td colspan="4" class="text-center"><div class="spinner-border spinner-border-sm text-primary" role="status"></div> Loading products...</td></tr>';

        const searchQuery = productSearch.value;
        const url = new URL('ajax/search_products.php', window.location.href);
        url.searchParams.append('branch', branch);
        if (searchQuery) {
            url.searchParams.append('search', searchQuery);
        }

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(products => {
                displayProducts(products);
            })
            .catch(error => {
                console.error('Error:', error);
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">Error loading products: ' + error.message + '</td></tr>';
            });
    }    // Function to display products in table
    function displayProducts(products) {
        const tbody = document.getElementById('productResults');
        
        if (!Array.isArray(products) || products.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">No products found</td></tr>';
            return;
        }

        tbody.innerHTML = products.map(product => `
            <tr>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-bold">${escapeHtml(product.name)}</span>
                        <small class="text-muted">SKU: ${escapeHtml(product.sku || 'N/A')}</small>
                        ${product.category ? `<small class="text-muted">Category: ${escapeHtml(product.category)}</small>` : ''}
                        ${product.brand ? `<small class="text-muted">Brand: ${escapeHtml(product.brand)}</small>` : ''}
                        <small class="text-muted">${product.category || ''} ${product.brand ? `| ${product.brand}` : ''}</small>
                    </div>
                </td>
                <td>${product.sku}</td>
                <td class="text-center">${product.quantity}</td>
                <td class="text-end">
                    <button type="button" class="btn btn-sm btn-outline-primary add-product" 
                            data-id="${product.id}"
                            data-name="${product.name}"
                            data-sku="${product.sku}"
                            data-stock="${product.quantity}">
                        <i class="fas fa-plus"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        // Attach click handlers to add buttons
        tbody.querySelectorAll('.add-product').forEach(button => {
            button.addEventListener('click', function() {
                const productData = this.dataset;
                addToSelected(productData);
            });
        });
    }

    // Function to add product to selected list
    function addToSelected(productData) {
        if (selectedProducts.has(productData.id)) {
            alert('Product already added to transfer list');
            return;
        }

        const tbody = document.querySelector('#selectedProducts tbody');
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <div class="d-flex flex-column">
                    <span class="fw-bold">${productData.name}</span>
                    <small class="text-muted">${productData.sku}</small>
                </div>
                <input type="hidden" name="products[]" value="${productData.id}">
            </td>
            <td class="text-center">
                <input type="number" name="quantities[]" class="form-control form-control-sm quantity-input" 
                       min="1" max="${productData.stock}" value="1" required
                       style="width: 80px; margin: 0 auto;">
            </td>
            <td class="text-end">
                <button type="button" class="btn btn-sm btn-outline-danger remove-product">
                    <i class="fas fa-minus"></i>
                </button>
            </td>
        `;

        tbody.appendChild(tr);
        selectedProducts.set(productData.id, tr);

        // Add remove handler
        tr.querySelector('.remove-product').addEventListener('click', function() {
            selectedProducts.delete(productData.id);
            tr.remove();
            validateForm();
        });

        // Add quantity change handler
        tr.querySelector('.quantity-input').addEventListener('change', function() {
            if (this.value > parseInt(productData.stock)) {
                alert('Quantity cannot exceed available stock');
                this.value = productData.stock;
            }
            if (this.value < 1) {
                this.value = 1;
            }
            validateForm();
        });

        validateForm();
    }

    // Function to validate form before submission
    function validateForm() {
        submitButton.disabled = selectedProducts.size === 0;
    }

    // Function to search products
    function searchProducts(term) {
        if (!sourceBranch.value) {
            alert('Please select a source branch first');
            return;
        }
        fetchProducts(sourceBranch.value);
    }

    // Function to clear product results
    function clearProductResults() {
        const tbody = document.getElementById('productResults');
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">Select a source branch to view products</td></tr>';
    }

    // Initialize form validation
    validateForm();
});
</script>

<?php
require_once 'includes/footer.php';
?>
