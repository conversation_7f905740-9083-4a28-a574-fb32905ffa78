<?php

// Add this at the very top to see detailed errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start the session at the beginning of the file
session_start();

// Check if user is logged in before any output
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Initialize pagination variables for products
$page = 1;
$items_per_page = 5; // Show 5 products per page

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM products p
              WHERE p.branch_name = '" . $conn->real_escape_string($branch_name) . "'";

// Add search condition if provided
if (isset($_GET['product_search']) && !empty($_GET['product_search'])) {
    $search = trim($_GET['product_search']);
    $count_query .= " AND (p.name LIKE '%" . $conn->real_escape_string($search) . "%'
                   OR p.sku LIKE '%" . $conn->real_escape_string($search) . "%')";
}

$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get all products for the product selection (filtered by branch)
$products_query = "SELECT p.*, c.name as category_name, b.name as brand_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN brands b ON p.brand_id = b.id
                WHERE p.branch_name = '" . $conn->real_escape_string($branch_name) . "'";

// Add search condition if provided
if (isset($_GET['product_search']) && !empty($_GET['product_search'])) {
    $search = trim($_GET['product_search']);
    $products_query .= " AND (p.name LIKE '%" . $conn->real_escape_string($search) . "%'
                      OR p.sku LIKE '%" . $conn->real_escape_string($search) . "%')";
}

$products_query .= " ORDER BY p.name ASC";

// Add pagination
$offset = ($page - 1) * $items_per_page;
$products_query .= " LIMIT $items_per_page OFFSET $offset";

$products_result = $conn->query($products_query);

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Process add to cart
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_to_cart'])) {
    $product_id = $_POST['product_id'];
    $quantity = $_POST['quantity'];

    // Get product details
    $product_query = "SELECT * FROM products WHERE id = ?";
    $stmt = $conn->prepare($product_query);
    $stmt->bind_param("i", $product_id);
    $stmt->execute();
    $product_result = $stmt->get_result();

    if ($product_result->num_rows > 0) {
        $product = $product_result->fetch_assoc();
        $is_tuition = stripos($product['name'], 'tuition') !== false;

        // Check if product is already in cart
        $found = false;
        foreach ($_SESSION['cart'] as $key => $item) {
            if ($item['id'] == $product_id) {
                $_SESSION['cart'][$key]['quantity'] += $quantity;

                // For non-tuition products, always use the actual price from database
                if (!$is_tuition) {
                    $_SESSION['cart'][$key]['price'] = $product['unit_price'];
                }

                $_SESSION['cart'][$key]['total'] = $_SESSION['cart'][$key]['quantity'] * $_SESSION['cart'][$key]['price'];
                $found = true;
                break;
            }
        }

        // If not found, add to cart
        if (!$found) {
            $_SESSION['cart'][] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'price' => $product['unit_price'],
                'quantity' => $quantity,
                'total' => $product['unit_price'] * $quantity,
                'branch_name' => $product['branch_name'],
                'is_tuition' => $is_tuition // Store whether this is a tuition product
            ];
        }
    }

    $stmt->close();

    // Redirect to refresh the page
    header("Location: sales.php?added=1");
    exit();
}

// Process remove from cart
if (isset($_GET['remove']) && isset($_GET['index'])) {
    $index = $_GET['index'];
    if (isset($_SESSION['cart'][$index])) {
        unset($_SESSION['cart'][$index]);
        $_SESSION['cart'] = array_values($_SESSION['cart']); // Reindex array
    }

    // Redirect to refresh the page
    header("Location: sales.php?removed=1");
    exit();
}

// Process clear cart
if (isset($_GET['clear_cart'])) {
    $_SESSION['cart'] = [];

    // Redirect to refresh the page
    header("Location: sales.php?cleared=1");
    exit();
}

// Process checkout
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['checkout'])) {
    // Filter cart items to only include products from the current branch
    $current_branch_cart = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
        return (!isset($item['branch_name']) || $item['branch_name'] == $branch_name);
    });

    // Debug: Log cart contents before processing
    error_log("CHECKOUT - Cart contents before processing:");
    foreach ($current_branch_cart as $index => $item) {
        error_log("Item #{$index}: ID={$item['id']}, Name={$item['name']}, Quantity={$item['quantity']}");
    }

    if (count($current_branch_cart) > 0) {
        $customer_name = $_POST['customer_name'];
        $customer_email = $_POST['customer_email'] ?? '';
        $customer_phone = $_POST['customer_phone'] ?? '';

        $invoice_number = $_POST['invoice_number'] ?? '';
        $payment_type = $_POST['payment_type'] ?? 'Cash';

        // Check if we have updated prices from the form
        $updated_prices = isset($_POST['updated_prices']) ? $_POST['updated_prices'] : [];

        // Debug: Log updated prices
        error_log("Updated prices from form: " . json_encode($updated_prices));

        // Update cart items with new prices if provided
        if (!empty($updated_prices)) {
            foreach ($updated_prices as $index => $price) {
                if (isset($_SESSION['cart'][$index]) &&
                    (!isset($_SESSION['cart'][$index]['branch_name']) || $_SESSION['cart'][$index]['branch_name'] == $branch_name)) {
                    $_SESSION['cart'][$index]['price'] = (float)$price;
                    $_SESSION['cart'][$index]['total'] = $_SESSION['cart'][$index]['quantity'] * (float)$price;

                    // Debug: Log price update
                    error_log("Updated price for item #{$index}: New price={$price}, Total={$_SESSION['cart'][$index]['total']}");
                }
            }
        }

        // Reindex the cart array to ensure proper indexing after filtering
        $_SESSION['cart'] = array_values($_SESSION['cart']);

        // Get the updated cart items for the current branch after price updates
        $current_branch_cart = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
            return (!isset($item['branch_name']) || $item['branch_name'] == $branch_name);
        });

        // Reindex the filtered cart to ensure sequential keys
        $current_branch_cart = array_values($current_branch_cart);

        // Debug: Log cart contents after reindexing
        error_log("CHECKOUT - Cart contents after reindexing:");
        foreach ($current_branch_cart as $index => $item) {
            error_log("Item #{$index}: ID={$item['id']}, Name={$item['name']}, Quantity={$item['quantity']}");
        }

        // Always use the cart_total from the form submission to ensure we save exactly what's displayed in the cart
        if (isset($_POST['cart_total']) && is_numeric($_POST['cart_total'])) {
            $total_amount = (float)$_POST['cart_total'];
        } else {
            // Fallback: Calculate total amount with updated prices (only for current branch)
            // This should rarely be needed as cart_total should always be submitted with the form
            $total_amount = 0;
            foreach ($current_branch_cart as $item) {
                $total_amount += $item['total'];
            }
        }

        // Generate order number
        $order_number = 'ORD-' . date('Ymd') . '-' . rand(1000, 9999);

        // Get branch name from session
        $branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

        // Verify user_id exists in users table before inserting
        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

        // Check if user exists in the database
        if ($user_id) {
            $user_check = "SELECT id FROM users WHERE id = ?";
            $user_stmt = $conn->prepare($user_check);
            $user_stmt->bind_param("i", $user_id);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();

            if ($user_result->num_rows == 0) {
                // User doesn't exist, set to NULL to avoid foreign key constraint
                $user_id = null;
            }

            $user_stmt->close();
        }

        // Begin transaction
        $conn->begin_transaction();

        try {
            // Insert order
            if ($user_id === null) {
                // If user_id is null, use a different query that allows NULL for created_by
                $order_query = "INSERT INTO orders (order_number, invoice_number, customer_name, customer_email, customer_phone, total_amount, status, created_by, payment_type, branch_name)
                              VALUES (?, ?, ?, ?, ?, ?, 'completed', NULL, ?, ?)";
                $stmt = $conn->prepare($order_query);
                $stmt->bind_param("sssssss", $order_number, $invoice_number, $customer_name, $customer_email, $customer_phone, $total_amount, $payment_type, $branch_name);
            } else {
                // If user_id is not null, use the original query
                $order_query = "INSERT INTO orders (order_number, invoice_number, customer_name, customer_email, customer_phone, total_amount, status, created_by, payment_type, branch_name)
                              VALUES (?, ?, ?, ?, ?, ?, 'completed', ?, ?, ?)";
                $stmt = $conn->prepare($order_query);
                $stmt->bind_param("sssssdiss", $order_number, $invoice_number, $customer_name, $customer_email, $customer_phone, $total_amount, $user_id, $payment_type, $branch_name);
            }

            if ($stmt->execute()) {
                $order_id = $stmt->insert_id;
                $stmt->close();

                // Debug: Log order creation
                error_log("Order created with ID: {$order_id}");

                // Insert order items and update product quantities (only for current branch)
                foreach ($current_branch_cart as $item_index => $item) {
                    // Debug: Log current item being processed
                    error_log("Processing item #{$item_index}: ID={$item['id']}, Name={$item['name']}, Quantity={$item['quantity']}");

                    // Insert order item - use separate variables to avoid reference issues
                    $product_id = $item['id'];
                    $quantity = $item['quantity'];
                    $unit_price = $item['price'];
                    $total_price = $item['total'];

                    $item_query = "INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
                                 VALUES (?, ?, ?, ?, ?)";
                    $item_stmt = $conn->prepare($item_query);
                    $item_stmt->bind_param("iiddd", $order_id, $product_id, $quantity, $unit_price, $total_price);

                    if (!$item_stmt->execute()) {
                        // Log error and rollback
                        error_log("Error inserting order item: " . $item_stmt->error);
                        $conn->rollback();
                        throw new Exception("Error inserting order item: " . $item_stmt->error);
                    }

                    $item_stmt->close();

                    // Debug: Log item insertion
                    error_log("Inserted order item: Product ID={$product_id}, Quantity={$quantity}, Price={$unit_price}");

                    // Update product quantity
                    $update_query = "UPDATE products SET quantity = quantity - ? WHERE id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    $update_stmt->bind_param("ii", $quantity, $product_id);

                    if (!$update_stmt->execute()) {
                        // Log error and rollback
                        error_log("Error updating product quantity: " . $update_stmt->error);
                        $conn->rollback();
                        throw new Exception("Error updating product quantity: " . $update_stmt->error);
                    }

                    $update_stmt->close();

                    // Debug: Log inventory update
                    error_log("Updated inventory: Product ID={$product_id}, Reduced by={$quantity}");
                }

                // Commit transaction
                $conn->commit();

                // Clear cart
                $_SESSION['cart'] = [];

                // Debug: Log cart cleared
                error_log("Cart cleared after successful checkout");

                // Redirect to success page
                header("Location: sales.php?success=1&order_id=" . $order_id);
                exit();
            } else {
                // Rollback on error
                $conn->rollback();
                $error_message = "Error creating order: " . $conn->error;
                error_log($error_message);
            }

            $stmt->close();
        } catch (Exception $e) {
            // Rollback on exception
            $conn->rollback();
            $error_message = "Error processing checkout: " . $e->getMessage();
            error_log($error_message);
        }
    } else {
        $error_message = "Cart is empty. Please add products before checkout.";
        error_log($error_message);
    }
}

// Filter cart items to only include products from the current branch
$current_branch_cart = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
    return (!isset($item['branch_name']) || $item['branch_name'] == $branch_name);
});

// Calculate cart total (only for current branch)
$cart_total = 0;
foreach ($current_branch_cart as $item) {
    $cart_total += $item['total'];
}

// Reindex the filtered cart array to ensure proper indexing
$current_branch_cart = array_values($current_branch_cart);

// Close connection
closeDB($conn);

// Include header.php after all potential redirects
require_once 'includes/header.php';
?>

<style>
        /* Product List Styles */        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .product-list-item {
            position: relative;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 0.75rem;
            display: flex;
            flex-direction: column;
            gap: 0.4rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .product-list-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .product-image-container {
            width: 100%;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 4px;
            overflow: hidden;
        }

        .product-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .product-info {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }        .product-name {
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0;
            color: #2c3e50;
            line-height: 1.2;
        }

        .product-details {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .product-details span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .product-actions {
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stock-badge {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            font-size: 0.75rem;
        }

        .out-of-stock {
            opacity: 0.7;
        }

        .out-of-stock .product-actions button {
            opacity: 0.5;
            pointer-events: none;
        }

        .product-quantity {
            width: 70px !important;
            text-align: center;
        }

        @media (max-width: 768px) {
            .product-list {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
    </style>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Point of Sale (POS)</h1>
        <a href="sales_list.php" class="btn btn-primary">
            <i class="fas fa-chart-bar me-2"></i> View Sales Entries
        </a>
    </div>

    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Order has been created successfully. Order ID: <?php echo $_GET['order_id']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['added']) && $_GET['added'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            Product has been added to cart.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['removed']) && $_GET['removed'] == 1): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            Product has been removed from cart.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['cleared']) && $_GET['cleared'] == 1): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            Cart has been cleared.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Product Selection -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Select Products</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <form method="get" action="sales.php" class="d-flex">
                            <input type="text" name="product_search" id="product-search" class="form-control" placeholder="Search products..." value="<?php echo isset($_GET['product_search']) ? htmlspecialchars($_GET['product_search']) : ''; ?>">
                            <button type="submit" class="btn btn-primary ms-2">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>

                    <div class="product-list">
                        <?php while ($product = $products_result->fetch_assoc()):
                            // Determine stock status class
                            $stock_class = 'in-stock';
                            $stock_badge = '';

                            if ($product['quantity'] == 0) {
                                $stock_class = 'out-of-stock';
                                $stock_badge = '<span class="badge bg-danger stock-badge">Out of Stock</span>';
                            } elseif ($product['quantity'] <= $product['reorder_level']) {
                                $stock_class = 'low-stock';
                                $stock_badge = '<span class="badge bg-warning text-dark stock-badge">Low Stock (' . $product['quantity'] . ')</span>';
                            } else {
                                $stock_badge = '<span class="badge bg-success stock-badge">' . $product['quantity'] . ' in stock</span>';
                            }
                        ?>
                        <div class="product-list-item <?php echo $stock_class; ?>">
                            <?php echo $stock_badge; ?>

                            <div class="product-image-container">
                                <?php if (!empty($product['image_url'])): ?>
                                    <img src="uploads/products/<?php echo htmlspecialchars($product['image_url']); ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         class="product-image">
                                <?php else: ?>
                                    <i class="fas fa-box fa-3x text-muted"></i>
                                <?php endif; ?>
                            </div>

                            <div class="product-info">
                                <h6 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h6>
                                <div class="product-details">
                                    <span><i class="fas fa-barcode fa-fw"></i><?php echo htmlspecialchars($product['sku'] ?? 'N/A'); ?></span>
                                    <span><i class="fas fa-tag fa-fw"></i><?php echo htmlspecialchars($product['brand_name'] ?? 'No Brand'); ?></span>
                                    <span><i class="fas fa-peso-sign fa-fw"></i><?php echo number_format($product['unit_price'], 2); ?></span>
                                </div>
                            </div>

                            <div class="product-actions">
                                <form method="post" action="sales.php" class="d-flex align-items-center gap-2 w-100">
                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                    <input type="number" name="quantity" value="1" min="1" 
                                           class="form-control form-control-sm product-quantity" 
                                           <?php echo $product['quantity'] == 0 ? 'disabled' : ''; ?>>
                                    <button type="submit" name="add_to_cart" class="btn btn-primary flex-grow-1" 
                                            <?php echo $product['quantity'] == 0 ? 'disabled' : ''; ?>>
                                        <i class="fas fa-plus me-1"></i> Add
                                    </button>
                                </form>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>

                    <!-- Pagination Controls -->
                    <?php if ($total_pages > 1): ?>
                    <div class="mt-4">
                        <nav aria-label="Product pagination">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="sales.php?page=<?php echo $page - 1; ?>&product_search=<?php echo urlencode(isset($_GET['product_search']) ? $_GET['product_search'] : ''); ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>

                                <?php
                                // Limit to showing only 15 page numbers
                                $max_visible_pages = 15;
                                $half_visible = floor($max_visible_pages / 2);

                                // Calculate start and end page numbers to display
                                if ($total_pages <= $max_visible_pages) {
                                    // If total pages is less than max visible, show all pages
                                    $start_page = 1;
                                    $end_page = $total_pages;
                                } else {
                                    // Calculate start and end based on current page
                                    $start_page = max(1, $page - $half_visible);
                                    $end_page = min($total_pages, $start_page + $max_visible_pages - 1);

                                    // Adjust if we're near the end
                                    if ($end_page - $start_page + 1 < $max_visible_pages) {
                                        $start_page = max(1, $end_page - $max_visible_pages + 1);
                                    }
                                }

                                // Always show first page
                                if ($start_page > 1) {
                                    echo '<li class="page-item ' . ($page == 1 ? 'active' : '') . '">';
                                    echo '<a class="page-link" href="sales.php?page=1&product_search=' . urlencode(isset($_GET['product_search']) ? $_GET['product_search'] : '') . '">';
                                    echo '1';
                                    echo '</a></li>';

                                    // Show ellipsis if not starting from page 2
                                    if ($start_page > 2) {
                                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                    }
                                }

                                // Show page numbers
                                for ($i = $start_page; $i <= $end_page; $i++) {
                                    if ($i == 1 || $i == $total_pages) {
                                        // Skip first and last page as they're handled separately
                                        continue;
                                    }
                                    echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '">';
                                    echo '<a class="page-link" href="sales.php?page=' . $i . '&product_search=' . urlencode(isset($_GET['product_search']) ? $_GET['product_search'] : '') . '">';
                                    echo $i;
                                    echo '</a></li>';
                                }

                                // Always show last page
                                if ($end_page < $total_pages) {
                                    // Show ellipsis if not ending at second-to-last page
                                    if ($end_page < $total_pages - 1) {
                                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                    }

                                    echo '<li class="page-item ' . ($page == $total_pages ? 'active' : '') . '">';
                                    echo '<a class="page-link" href="sales.php?page=' . $total_pages . '&product_search=' . urlencode(isset($_GET['product_search']) ? $_GET['product_search'] : '') . '">';
                                    echo $total_pages;
                                    echo '</a></li>';
                                }
                                ?>

                                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="sales.php?page=<?php echo $page + 1; ?>&product_search=<?php echo urlencode(isset($_GET['product_search']) ? $_GET['product_search'] : ''); ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Shopping Cart -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Product Sold</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Filter cart items to only show products from the current branch
                    $current_branch_cart = array_filter($_SESSION['cart'], function($item) use ($branch_name) {
                        return (!isset($item['branch_name']) || $item['branch_name'] == $branch_name);
                    });

                    if (count($current_branch_cart) > 0):
                    ?>
                        <div class="table-responsive mb-3">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Qty</th>
                                        <th>Price</th>
                                        <th>Total</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($current_branch_cart as $index => $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <span class="input-group-text">₱</span>
                                                <input type="number" class="form-control form-control-sm item-price"
                                                    data-index="<?php echo $index; ?>"
                                                    data-quantity="<?php echo $item['quantity']; ?>"
                                                    value="<?php echo number_format($item['price'], 2, '.', ''); ?>"
                                                    step="0.01" min="0.01">
                                            </div>
                                        </td>
                                        <td>₱<span class="item-total"><?php echo number_format($item['total'], 2); ?></span></td>
                                        <td>
                                            <a href="sales.php?remove=1&index=<?php echo $index; ?>" class="text-danger">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3" class="text-end">Total:</th>
                                        <th>₱<span id="cart-total"><?php echo number_format($cart_total, 2); ?></span></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="sales.php?clear_cart=1" class="btn btn-warning">
                                <i class="fas fa-trash me-2"></i> Clear Cart
                            </a>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#checkoutModal">
                                <i class="fas fa-shopping-cart me-2"></i> Checkout
                            </button>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p>Your cart is empty.</p>
                            <p class="text-muted">Add products from the list to proceed with the sale.</p>
                        </div>
                    <?php endif; ?>
                </div>

            </div>

        </div>
        <!-- <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#dailyOrdersModal">
           <i class="fas fa-print me-2"></i> Daily Orders Preview
        </button> -->
    </div>
</div>

<!-- Checkout Modal -->
<div class="modal fade" id="checkoutModal" tabindex="-1" aria-labelledby="checkoutModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="checkoutModalLabel">Complete Sale</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="sales.php">
                <div class="modal-body">
                    <div class="row">
                        <!-- Left Column: Form Fields -->
                        <div class="col-md-6">
                            <?php
                            // Check if any product in the cart contains "Tuition" in its name
                            $has_tuition = false;
                            foreach ($current_branch_cart as $item) {
                                if (stripos($item['name'], 'tuition') !== false) {
                                    $has_tuition = true;
                                    break;
                                }
                            }

                            // Show different fields based on whether there's a tuition product
                            if ($has_tuition):
                            ?>
                            <!-- Fields for tuition products -->
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">Student Name *</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="customer_email" class="form-label">Email/Teacher</label>
                                <input type="text" class="form-control" id="customer_email" name="customer_email">
                            </div>
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">Lesson/Activity</label>
                                <input type="text" class="form-control" id="customer_phone" name="customer_phone">
                            </div>
                            <?php else: ?>
                            <!-- Fields for non-tuition products -->
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">Customer Name *</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" value="Walk-in" required>
                            </div>
                            <input type="hidden" name="customer_email" value="">
                            <input type="hidden" name="customer_phone" value="">
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="invoice_number" class="form-label">Invoice Number *</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" value="<?php
                                // Get the last invoice number from the database
                                $conn = connectDB();
                                $branch_name = $_SESSION['branch_name'];

                                $last_invoice_query = "SELECT id,invoice_number FROM orders where branch_name='$branch_name'  ORDER BY id DESC LIMIT 1";
                                $result = $conn->query($last_invoice_query);

                                if ($result && $result->num_rows > 0) {
                                    // Extract the numeric portion and increment
                                    $last_invoice = $result->fetch_assoc()['invoice_number'];
                                    $parts = explode('-', $last_invoice);
                                    $last_number = intval(end($parts));
                                    $new_number = $last_number + 1;
                                    echo sprintf('%04d', $new_number);
                                } else {
                                    // No invoice for today yet, start with 0001
                                    echo "00001";
                                }
                                ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="payment_type" class="form-label">Payment Method *</label>
                                <select class="form-select" id="payment_type" name="payment_type" required>
                                    <option value="Card (Debit)">Card Payments (Debit)</option>
                                    <option value="Card (Credit)">Card Payment (Credit)</option>
                                    <option value="Cheque">Cheque Payments</option>
                                    <option value="Charge">Charge Transactions</option>
                                    <option value="Cash">Cash Transactions</option>
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="GCash/Maya">GCash/Maya</option>
                                </select>
                            </div>
                        </div>

                        <!-- Right Column: Receipt Preview -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Receipt Preview</h6>
                                <div id="receipt-preview" style="width: 57mm; min-width: 57mm; max-width: 57mm; background: #fff; border: 1px solid #eee; padding: 8px 6px; font-family: 'Courier New', 'Lucida Console', 'Monaco', monospace; font-size: 10px; font-stretch: condensed; letter-spacing: -0.5px; margin: 0 auto;">
                                    <div style="text-align: center; font-weight: bold; font-size: 11px; font-stretch: condensed;">
                                        <div style="font-size: 9px; font-weight: normal;">
                                            (<?php echo htmlspecialchars($branch_name); ?>)
                                        </div>
                                        Musar Music Corporation
                                        
                                        <div style="font-size: 8px; font-weight: normal; margin-top:2px;">
                                            Invoice #: <?php
                                                // Always use the value from the invoice_number input field if set, otherwise fallback to the PHP variable
                                                if (isset($_POST['invoice_number']) && $_POST['invoice_number'] !== '') {
                                                    echo htmlspecialchars($_POST['invoice_number']);
                                                } elseif (isset($_GET['invoice_number']) && $_GET['invoice_number'] !== '') {
                                                    echo htmlspecialchars($_GET['invoice_number']);
                                                } elseif (isset($invoice_number) && $invoice_number !== '') {
                                                    echo htmlspecialchars($invoice_number);
                                                } else {
                                                    // Try to get the value from the input field if available in the DOM (for preview before submit)
                                                    echo isset($_SESSION['last_invoice_number']) ? htmlspecialchars($_SESSION['last_invoice_number']) : '';
                                                }
                                            ?>
                                        </div>
                                    </div>
                                    <hr style="margin: 4px 0; border-top: 1px dashed #bbb;">
                                    <div style="margin-bottom: 3px; font-weight: bold; font-size:9px; font-stretch: condensed;">
                                        <span style="float:left; width: 55%;">Description</span>
                                        <span style="float:left; width: 20%; text-align:center;">Qty</span>
                                        <span style="float:right; width: 25%; text-align:right;">Total</span>
                                        <div style="clear:both;"></div>
                                    </div>
                                    <hr style="margin: 3px 0; border-top: 1px dashed #bbb;">
                                    <?php foreach ($current_branch_cart as $item): ?>
                                        <div style="margin-bottom: 1px;">
                                            <div>
                                                <span style="float:left; width: 55%; word-break: break-all; white-space: normal; font-size:9px; font-stretch: condensed; line-height: 1.1;">
                                                    <?php echo htmlspecialchars($item['name']); ?>
                                                </span>
                                                <span style="float:left; width: 20%; text-align:center; font-size:9px; font-stretch: condensed;">
                                                    <?php echo $item['quantity']; ?>
                                                </span>
                                                <span style="float:right; width: 25%; text-align:right; font-size:9px; font-stretch: condensed;">
                                                    ₱<?php echo number_format($item['total'], 2); ?>
                                                </span>
                                                <div style="clear:both;"></div>
                                            </div>
                                            <div style="font-size: 8px; color: #555; margin-left: 2px; font-stretch: condensed;">
                                                @ ₱<?php echo number_format($item['price'], 2); ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <hr style="margin: 4px 0; border-top: 1px dashed #bbb;">
                                    <div style="font-weight: bold; font-size: 10px; text-align: right; font-stretch: condensed;">
                                        Total: ₱<span id="checkout-total-amount"><?php echo number_format($cart_total, 2); ?></span>
                                    </div>
                                </div>
                                <input type="hidden" id="checkout-total-input" name="cart_total" value="<?php echo number_format($cart_total, 2, '.', ''); ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="checkout" class="btn btn-success">Complete Sale</button>
                </div>
                <!-- Container for updated prices hidden fields -->
                <div id="updated-prices-container">
                    <?php foreach ($current_branch_cart as $index => $item): ?>
                    <input type="hidden" name="updated_prices[<?php echo $index; ?>]" value="<?php echo $item['price']; ?>">
                    <?php endforeach; ?>
                </div>
                <!-- Hidden fields for product IDs to ensure correct order -->
                <?php foreach ($current_branch_cart as $index => $item): ?>
                <input type="hidden" name="product_ids[<?php echo $index; ?>]" value="<?php echo $item['id']; ?>">
                <?php endforeach; ?>
            </form>
        </div>
    </div>
</div>

<!-- Daily Orders Modal -->
<div class="modal fade" id="dailyOrdersModal" tabindex="-1" aria-labelledby="dailyOrdersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background-color: transparent; border: none; box-shadow: none;">
            <div class="modal-body p-0">
                <!-- Floating paper effect -->
                <div id="print-area" class="p-5" style="background-color: white; box-shadow: 0 10px 30px rgba(0,0,0,0.15); border-radius: 8px; min-height: 500px; transform: rotate(-0.5deg); position: relative; border: 1px solid #e0e0e0;">
                    <!-- Paper texture overlay -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('data:image/png;base64,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'); opacity: 0.03; pointer-events: none; z-index: 0;"></div>

                    <!-- Close button positioned on the paper -->
                    <button type="button" class="btn-close position-absolute" style="top: 15px; right: 15px; z-index: 1;" data-bs-dismiss="modal" aria-label="Close"></button>

                    <div class="text-center mb-4">
                        <h3 style="font-family: 'Times New Roman', serif;"><?php echo isset($_SESSION['branch_name']) ? htmlspecialchars($_SESSION['branch_name']) : 'Main Branch'; ?></h3>
                        <h4 style="font-family: 'Times New Roman', serif;">Daily Sales Preview</h4>
                        <p class="mb-1">Date: <?php echo date('F d, Y'); ?></p>
                        <hr style="width: 50%; margin: 15px auto; border-top: 1px dashed #ccc;">
                    </div>

                    <div class="table-responsive">
                        <table class="table" style="border-collapse: collapse;">
                            <thead>
                                <tr style="border-bottom: 2px solid #000;">
                                    <th style="padding: 10px;">Description</th>
                                    <th style="padding: 10px; text-align: center;">Quantity</th>
                                    <th style="padding: 10px; text-align: right;">Amount</th>
                                    <th style="padding: 10px; text-align: right;">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Get database connection
                                $conn = connectDB();

                                // Get today's date
                                $today = date('Y-m-d');
                                $branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

                                // Query to get today's orders with items
                                $query = "SELECT o.id, o.order_number, o.invoice_number, o.customer_name,
                                         o.total_amount, o.created_at, p.name as product_name,
                                         oi.quantity, oi.unit_price, oi.total_price
                                         FROM orders o
                                         JOIN order_items oi ON o.id = oi.order_id
                                         JOIN products p ON oi.product_id = p.id
                                         WHERE DATE(o.created_at) = ? AND o.branch_name = ?
                                         ORDER BY o.created_at DESC";

                                $stmt = $conn->prepare($query);
                                $stmt->bind_param("ss", $today, $branch_name);
                                $stmt->execute();
                                $result = $stmt->get_result();

                                $total_sales = 0;
                                $total_items = 0;

                                if ($result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()) {
                                        echo "<tr style='border-bottom: 1px solid #eee;'>";
                                        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['product_name']) . "</td>";
                                        echo "<td style='padding: 8px; text-align: center;'>" . $row['quantity'] . "</td>";
                                        echo "<td style='padding: 8px; text-align: right;'>₱" . number_format($row['unit_price'], 2) . "</td>";
                                        echo "<td style='padding: 8px; text-align: right;'>₱" . number_format($row['total_price'], 2) . "</td>";
                                        echo "</tr>";

                                        $total_sales += $row['total_price'];
                                        $total_items += $row['quantity'];
                                    }
                                } else {
                                    echo "<tr><td colspan='4' class='text-center' style='padding: 20px;'>No orders found for today</td></tr>";
                                }

                                $stmt->close();
                                closeDB($conn);
                                ?>
                            </tbody>
                            <tfoot>
                                <tr style="border-top: 2px solid #000;">
                                    <th colspan="3" style="padding: 10px; text-align: right;">Total Sales:</th>
                                    <th style="padding: 10px; text-align: right;">₱<?php echo number_format($total_sales, 2); ?></th>
                                </tr>
                                <tr>
                                    <th colspan="3" style="padding: 10px; text-align: right;">Total Items Sold:</th>
                                    <th style="padding: 10px; text-align: right;"><?php echo $total_items; ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div class="mt-5 text-center" style="font-size: 0.9em; color: #666;">
                        <p>Report generated on: <?php echo date('Y-m-d H:i:s'); ?></p>
                    </div>

                    <!-- Print button positioned at bottom right of the paper -->
                    <button type="button" class="btn btn-primary position-absolute" id="print-button" style="bottom: 20px; right: 20px; z-index: 1;">
                        <i class="fas fa-print me-2"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Product search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('product-search');
    const productsTable = document.getElementById('products-table');

    if (productsTable) {
        const rows = productsTable.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();

            for (let i = 0; i < rows.length; i++) {
                const productName = rows[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                const categoryName = rows[i].getElementsByTagName('td')[1].textContent.toLowerCase();

                if (productName.indexOf(searchTerm) > -1 || categoryName.indexOf(searchTerm) > -1) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        });
    }

    // Price editing functionality
    const priceInputs = document.querySelectorAll('.item-price');
    const cartTotalElement = document.getElementById('cart-total');

    // Function to update item total and cart total
    function updateTotals() {
        let newCartTotal = 0;

        // Get all price inputs to ensure we update all items
        const allPriceInputs = document.querySelectorAll('.item-price');

        allPriceInputs.forEach(input => {
            const index = input.getAttribute('data-index');
            const quantity = parseFloat(input.getAttribute('data-quantity'));
            const price = parseFloat(input.value);

            // Calculate item total - ensure we're only multiplying once
            const itemTotal = price * quantity;

            // Update item total display
            const itemTotalElement = input.closest('tr').querySelector('.item-total');
            itemTotalElement.textContent = itemTotal.toFixed(2);

            // Add to cart total
            newCartTotal += itemTotal;
        });

        // Update cart total display
        cartTotalElement.textContent = newCartTotal.toFixed(2);

        // Update the order summary in the checkout modal
        const checkoutTotalElement = document.getElementById('checkout-total-amount');
        if (checkoutTotalElement) {
            // Make sure we're only updating the amount, not the entire text
            checkoutTotalElement.textContent = newCartTotal.toFixed(2);

            // Also update the hidden input for checkout total
            const checkoutTotalInput = document.getElementById('checkout-total-input');
            if (checkoutTotalInput) {
                checkoutTotalInput.value = newCartTotal.toFixed(2);
            }
        }

        // Return the new cart total for use in other functions
        return newCartTotal;
    }

    // Store original prices when page loads to preserve modified prices
    const originalPrices = {};
    document.querySelectorAll('.item-price').forEach(input => {
        const index = input.getAttribute('data-index');
        originalPrices[index] = parseFloat(input.value);
    });

    // Remove any existing event listeners to prevent duplicates
    priceInputs.forEach(input => {
        const newInput = input.cloneNode(true);
        input.parentNode.replaceChild(newInput, input);
    });

    // Get the fresh collection of price inputs after replacement
    const freshPriceInputs = document.querySelectorAll('.item-price');

    // Add event listeners to price inputs (only once per input)
    freshPriceInputs.forEach(input => {
        // Add event listeners for both change and input events
        input.addEventListener('change', function() {
            updateTotals();
            saveModifiedPrices();
        });
        input.addEventListener('input', updateTotals);

        // Add blur event to ensure update happens when focus leaves the input
        input.addEventListener('blur', function() {
            updateTotals();
            saveModifiedPrices();
        });
    });

    // Function to save modified prices to localStorage
    function saveModifiedPrices() {
        const modifiedPrices = {};
        document.querySelectorAll('.item-price').forEach(input => {
            const index = input.getAttribute('data-index');
            modifiedPrices[index] = input.value;
        });
        localStorage.setItem('modifiedCartPrices', JSON.stringify(modifiedPrices));
    }

    // Check if we just added a product to the cart (URL has 'added=1')
    const urlParams = new URLSearchParams(window.location.search);
    const justAddedProduct = urlParams.has('added') && urlParams.get('added') === '1';

    // If we just added a product, restore any previously modified prices from localStorage
    if (justAddedProduct) {
        try {
            const savedPrices = JSON.parse(localStorage.getItem('modifiedCartPrices') || '{}');

            // Apply saved prices to inputs
            freshPriceInputs.forEach(input => {
                const index = input.getAttribute('data-index');
                if (savedPrices[index]) {
                    input.value = savedPrices[index];
                }
            });
        } catch (e) {
            console.error('Error restoring modified prices:', e);
        }
    }

    // Run updateTotals once on page load to ensure everything is in sync
    updateTotals();

    // Add event listener to checkout form submission
    const checkoutForm = document.querySelector('#checkoutModal form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
            // Update totals one final time before submission to ensure latest values
            const finalTotal = updateTotals();

            // Clear previous hidden fields
            const container = document.getElementById('updated-prices-container');
            container.innerHTML = '';

            // Add hidden fields for each item's updated price
            // Get all price inputs from the cart table
            const allPriceInputs = document.querySelectorAll('.item-price');

            allPriceInputs.forEach(input => {
                // Use the index from the session cart array
                const index = input.getAttribute('data-index');
                const price = input.value;

                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.name = 'updated_prices[' + index + ']';
                hiddenField.value = price;

                container.appendChild(hiddenField);
            });

            // Update the existing hidden input for the cart total instead of creating a new one
            // This ensures we're using the exact same value displayed in the cart
            const checkoutTotalInput = document.getElementById('checkout-total-input');
            if (checkoutTotalInput) {
                checkoutTotalInput.value = finalTotal.toFixed(2);
            }

            // Open cash drawer after form submission
            setTimeout(() => {
                openCashDrawer();
            }, 500); // Small delay to ensure the form submission completes first
        });
    }

    // Function to open USB cash drawer
    async function openCashDrawer() {
        try {
            // Check if Web Serial API is supported
            if (!navigator.serial) {
                console.warn('Web Serial API is not supported in this browser. Trying alternative method...');
                openCashDrawerAlternative();
                return;
            }

            // Create notification element
            showNotification('Connecting to cash drawer...', 'info');

            // Get drawer settings from localStorage or use defaults
            const drawerSettings = getDrawerSettings();

            // Request port access
            const port = await navigator.serial.requestPort();
            await port.open({ baudRate: drawerSettings.baudRate }); // Use configured baud rate

            // Get the appropriate command based on drawer type
            const openCommand = getDrawerCommand(drawerSettings.drawerType);

            // Create a writer and send the command
            const writer = port.writable.getWriter();
            await writer.write(openCommand);
            writer.releaseLock();

            // Close the port when done
            await port.close();

            console.log('Cash drawer open command sent successfully');
            showNotification('Cash drawer opened successfully', 'success');
        } catch (error) {
            console.error('Error opening cash drawer:', error);
            showNotification('Error opening cash drawer. Please try again.', 'error');

            // Try alternative method as fallback
            openCashDrawerAlternative();
        }
    }

    // Function to get drawer settings from localStorage or use defaults
    function getDrawerSettings() {
        const defaultSettings = {
            drawerType: 'standard', // standard, epson, star, custom
            baudRate: 9600,
            customCommand: [0x1B, 0x70, 0x00, 0x19, 0xFA] // Default ESC/POS command
        };

        try {
            const savedSettings = localStorage.getItem('cashDrawerSettings');
            return savedSettings ? JSON.parse(savedSettings) : defaultSettings;
        } catch (e) {
            console.warn('Could not load drawer settings, using defaults');
            return defaultSettings;
        }
    }

    // Function to get the appropriate command based on drawer type
    function getDrawerCommand(drawerType) {
        const settings = getDrawerSettings();

        switch (drawerType) {
            case 'epson':
                // Epson printer command
                return new Uint8Array([0x1B, 0x70, 0x00, 0x32, 0x32]);
            case 'star':
                // Star Micronics command
                return new Uint8Array([0x07]);
            case 'custom':
                // Use custom command from settings
                return new Uint8Array(settings.customCommand);
            case 'standard':
            default:
                // Standard ESC/POS command (works with most cash drawers)
                return new Uint8Array([0x1B, 0x70, 0x00, 0x19, 0xFA]);
        }
    }

    // Alternative method for browsers without Web Serial API support
    function openCashDrawerAlternative() {
        // Create a hidden iframe to send commands to a local service
        // This approach requires a local service running on the POS machine
        try {
            // Try to use a local service on a predefined port
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'http://localhost:8090/opendrawer';
            document.body.appendChild(iframe);

            // Remove the iframe after a short delay
            setTimeout(() => {
                document.body.removeChild(iframe);
                showNotification('Cash drawer command sent via local service', 'success');
            }, 2000);
        } catch (error) {
            console.error('Alternative method failed:', error);
            showNotification('Could not open cash drawer. Please check your setup.', 'error');
        }
    }

    // Function to show notifications to the user
    function showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('cash-drawer-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'cash-drawer-notification';
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.right = '20px';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '5px';
            notification.style.zIndex = '9999';
            notification.style.transition = 'all 0.3s ease';
            document.body.appendChild(notification);
        }

        // Set styles based on notification type
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#28a745';
                notification.style.color = 'white';
                break;
            case 'error':
                notification.style.backgroundColor = '#dc3545';
                notification.style.color = 'white';
                break;
            case 'info':
            default:
                notification.style.backgroundColor = '#17a2b8';
                notification.style.color = 'white';
                break;
        }

        // Set message and show notification
        notification.textContent = message;
        notification.style.opacity = '1';

        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }


});

</script>

<!-- Include sales.js for product card click functionality -->
<script src="assets/js/sales.js"></script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Product Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="enlargedImage" src="" alt="Enlarged product image" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle image click events
    document.querySelectorAll('.product-image').forEach(function(img) {
        img.addEventListener('click', function() {
            document.getElementById('enlargedImage').src = this.src;
            document.getElementById('imageModalLabel').textContent = this.dataset.productName || 'Product Image';
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
