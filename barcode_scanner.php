<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Get database connection
$conn = connectDB();

// Function to add to cart
function addToCart($conn) {
    if (isset($_POST['product_id']) && isset($_POST['quantity'])) {
        $product_id = (int)$_POST['product_id'];
        $quantity = (int)$_POST['quantity'];
        
        if ($product_id > 0 && $quantity > 0) {
            // Get product details
            $query = "SELECT * FROM products WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $product_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $product = $result->fetch_assoc();
                
                // Get current branch
                $branch_name = $_SESSION['branch_name'] ?? 'Main Branch';
                
                // Initialize branch carts if not exists
                if (!isset($_SESSION['branch_carts'])) {
                    $_SESSION['branch_carts'] = [];
                }
                
                // Initialize current branch cart if not exists
                if (!isset($_SESSION['branch_carts'][$branch_name])) {
                    $_SESSION['branch_carts'][$branch_name] = [];
                }
                
                // Check if product already in cart
                $found = false;
                foreach ($_SESSION['branch_carts'][$branch_name] as $key => $item) {
                    if ($item['id'] == $product_id) {
                        // Update quantity
                        $_SESSION['branch_carts'][$branch_name][$key]['quantity'] += $quantity;
                        $_SESSION['branch_carts'][$branch_name][$key]['total'] = 
                            $_SESSION['branch_carts'][$branch_name][$key]['quantity'] * 
                            $_SESSION['branch_carts'][$branch_name][$key]['price'];
                        $found = true;
                        break;
                    }
                }
                
                // If product not in cart, add it
                if (!$found) {
                    $_SESSION['branch_carts'][$branch_name][] = [
                        'id' => $product_id,
                        'name' => $product['name'],
                        'price' => $product['unit_price'],
                        'quantity' => $quantity,
                        'total' => $quantity * $product['unit_price']
                    ];
                }
                
                $_SESSION['success_message'] = "Product added to cart successfully.";
            } else {
                $_SESSION['error_message'] = "Product not found.";
            }
            
            $stmt->close();
        } else {
            $_SESSION['error_message'] = "Invalid product ID or quantity.";
        }
    }
}

// Handle direct barcode scan submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['barcode'])) {
    $barcode = trim($_POST['barcode']);
    
    if (!empty($barcode)) {
        // Search for product with this SKU
        $query = "SELECT * FROM products WHERE sku = ? LIMIT 1";
        $stmt = $conn->prepare($query);
        
        if ($stmt) {
            $stmt->bind_param("s", $barcode);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $product = $result->fetch_assoc();
                
                // Add product to cart
                $_POST['product_id'] = $product['id'];
                $_POST['quantity'] = 1;
                $_POST['add_to_cart'] = true;
                
                addToCart($conn);
                
                // Update last_scanned timestamp if the column exists
                $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'last_scanned'");
                if ($check_column->num_rows > 0) {
                    $update_query = "UPDATE products SET last_scanned = NOW() WHERE id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    if ($update_stmt) {
                        $update_stmt->bind_param("i", $product['id']);
                        $update_stmt->execute();
                        $update_stmt->close();
                    }
                }
            } else {
                $_SESSION['error_message'] = "Product with SKU '" . htmlspecialchars($barcode) . "' not found.";
            }
            
            $stmt->close();
        } else {
            $_SESSION['error_message'] = "Database error: " . $conn->error;
        }
    } else {
        $_SESSION['error_message'] = "Please enter a SKU.";
    }
    
    // Redirect to prevent form resubmission
    header("Location: barcode_scanner.php");
    exit();
}

// Handle add to cart action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_to_cart'])) {
    addToCart($conn);
    
    // Redirect to prevent form resubmission
    header("Location: barcode_scanner.php");
    exit();
}

// Handle clear cart action
if (isset($_GET['clear_cart'])) {
    // Get current branch
    $current_branch = $_SESSION['branch_name'] ?? 'Main Branch';
    
    // Clear only the current branch's cart
    if (isset($_SESSION['branch_carts'][$current_branch])) {
        $_SESSION['branch_carts'][$current_branch] = [];
    }
    
    // Set success message
    $_SESSION['success_message'] = "Cart has been cleared successfully.";
    
    // Redirect to prevent form resubmission
    header("Location: barcode_scanner.php");
    exit();
}

// Get current branch
$branch_name = $_SESSION['branch_name'] ?? 'Main Branch';

// Get cart items for current branch
$current_branch_cart = [];
if (isset($_SESSION['branch_carts'][$branch_name])) {
    $current_branch_cart = $_SESSION['branch_carts'][$branch_name];
}

// Calculate cart total
$cart_total = 0;
foreach ($current_branch_cart as $item) {
    $cart_total += $item['total'];
}

// Page title
$page_title = "Barcode Scanner Mode";

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-dark text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>Barcode Scanner Mode</h5>
                        <a href="sales.php" class="btn btn-sm btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i> Back to Sales
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success_message'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php unset($_SESSION['success_message']); ?>
                    <?php endif; ?>
                    
                    <?php if (isset($_SESSION['error_message'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php unset($_SESSION['error_message']); ?>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-search me-2"></i>Scan Barcode</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> Place cursor in the input field below and scan a barcode, or enter it manually and press Enter.
                                    </div>
                                    
                                    <form method="POST" action="barcode_scanner.php" id="barcodeForm" class="mb-4">
                                        <div class="input-group mb-3">
                                            <input type="text" name="barcode" id="barcodeInput" class="form-control form-control-lg" 
                                                   placeholder="Scan or enter barcode/SKU" autofocus>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-1"></i> Find
                                            </button>
                                        </div>
                                    </form>
                                    
                                    <div class="d-grid gap-2">
                                        <a href="sales.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-th-large me-1"></i> Switch to Product Grid
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Current Cart</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($current_branch_cart)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Product</th>
                                                        <th>Qty</th>
                                                        <th>Price</th>
                                                        <th>Total</th>
                                                        <th></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($current_branch_cart as $index => $item): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($item['name']); ?></td>
                                                            <td><?php echo $item['quantity']; ?></td>
                                                            <td>₱<?php echo number_format($item['price'], 2); ?></td>
                                                            <td>₱<?php echo number_format($item['total'], 2); ?></td>
                                                            <td>
                                                                <a href="barcode_scanner.php?remove=<?php echo $index; ?>" class="text-danger">
                                                                    <i class="fas fa-times"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <th colspan="3" class="text-end">Total:</th>
                                                        <th>₱<?php echo number_format($cart_total, 2); ?></th>
                                                        <th></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                        
                                        <div class="d-grid gap-2 mt-3">
                                            <a href="sales.php" class="btn btn-primary">
                                                <i class="fas fa-cash-register me-1"></i> Go to Checkout
                                            </a>
                                            <a href="barcode_scanner.php?clear_cart=1" class="btn btn-outline-danger" 
                                               onclick="return confirm('Are you sure you want to clear the cart?')">
                                                <i class="fas fa-trash-alt me-1"></i> Clear Cart
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                            <p class="mb-0">Your cart is empty</p>
                                            <p class="text-muted">Scan products to add them to the cart</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recently Scanned Products</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>SKU/Barcode</th>
                                                    <th>Product</th>
                                                    <th>Price</th>
                                                    <th>Stock</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recentProducts">
                                                <?php
                                                // Check if last_scanned column exists
                                                $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'last_scanned'");
                                                
                                                if ($check_column->num_rows == 0) {
                                                    // Add last_scanned column if it doesn't exist
                                                    $conn->query("ALTER TABLE products ADD COLUMN last_scanned TIMESTAMP NULL DEFAULT NULL");
                                                }
                                                
                                                // Check if branch_name column exists in products table
                                                $check_branch_column = $conn->query("SHOW COLUMNS FROM products LIKE 'branch_name'");
                                                
                                                // Get recently scanned products (last 5)
                                                if ($check_branch_column->num_rows > 0) {
                                                    // If branch_name column exists, use it for filtering
                                                    $query = "SELECT * FROM products 
                                                              WHERE branch_name = ? 
                                                              ORDER BY last_scanned DESC 
                                                              LIMIT 5";
                                                    $stmt = $conn->prepare($query);
                                                    $stmt->bind_param("s", $branch_name);
                                                } else {
                                                    // If branch_name column doesn't exist, don't filter by branch
                                                    $query = "SELECT * FROM products 
                                                              ORDER BY last_scanned DESC 
                                                              LIMIT 5";
                                                    $stmt = $conn->prepare($query);
                                                }
                                                
                                                if ($stmt) {
                                                    $stmt->execute();
                                                    $result = $stmt->get_result();
                                                    
                                                    if ($result->num_rows > 0) {
                                                        while ($product = $result->fetch_assoc()) {
                                                            echo '<tr>';
                                                            echo '<td>' . htmlspecialchars($product['sku']) . '</td>';
                                                            echo '<td>' . htmlspecialchars($product['name']) . '</td>';
                                                            echo '<td>₱' . number_format($product['unit_price'], 2) . '</td>';
                                                            echo '<td>' . $product['quantity'] . '</td>';
                                                            echo '<td>
                                                                    <form method="POST" action="barcode_scanner.php">
                                                                        <input type="hidden" name="product_id" value="' . $product['id'] . '">
                                                                        <input type="hidden" name="quantity" value="1">
                                                                        <button type="submit" name="add_to_cart" class="btn btn-sm btn-primary">
                                                                            <i class="fas fa-plus"></i> Add
                                                                        </button>
                                                                    </form>
                                                                  </td>';
                                                            echo '</tr>';
                                                        }
                                                    } else {
                                                        echo '<tr><td colspan="5" class="text-center">No recently scanned products</td></tr>';
                                                    }
                                                    
                                                    $stmt->close();
                                                } else {
                                                    echo '<tr><td colspan="5" class="text-center text-danger">Error loading recent products: ' . $conn->error . '</td></tr>';
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on barcode input when page loads
    const barcodeInput = document.getElementById('barcodeInput');
    if (barcodeInput) {
        barcodeInput.focus();
    }
    
    // Auto-submit form when barcode is scanned
    if (barcodeInput) {
        let lastInputTime = 0;
        let inputBuffer = '';
        
        barcodeInput.addEventListener('input', function(e) {
            const currentTime = new Date().getTime();
            
            // If there's a pause longer than 500ms, assume it's a new scan
            if (currentTime - lastInputTime > 500) {
                inputBuffer = '';
            }
            
            inputBuffer += e.data;
            lastInputTime = currentTime;
            
            // Most barcode scanners send an Enter key after scanning
            // This is just a fallback for scanners that don't
            if (inputBuffer.length >= 5 && currentTime - lastInputTime < 50) {
                // Submit the form after a short delay to allow the full barcode to be entered
                setTimeout(() => {
                    document.getElementById('barcodeForm').submit();
                }, 100);
            }
        });
        
        // Handle Enter key press
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('barcodeForm').submit();
            }
        });
    }
    
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-info)');
    
    if (alerts) {
        alerts.forEach(alert => {
            setTimeout(() => {
                if (typeof bootstrap !== 'undefined') {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                } else {
                    alert.style.display = 'none';
                }
            }, 5000);
        });
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Alt+C to clear the barcode input
        if (e.altKey && e.key === 'c') {
            e.preventDefault();
            if (barcodeInput) {
                barcodeInput.value = '';
                barcodeInput.focus();
            }
        }
        
        // Alt+S to focus on the barcode input
        if (e.altKey && e.key === 's') {
            e.preventDefault();
            if (barcodeInput) {
                barcodeInput.focus();
            }
        }
        
        // Alt+P to go to checkout
        if (e.altKey && e.key === 'p') {
            e.preventDefault();
            window.location.href = 'sales.php';
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>



