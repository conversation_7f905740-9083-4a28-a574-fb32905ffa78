<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'Online Store'; ?></title>
    <link rel="icon" href="../franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/shop-style.css">
    <style>
        /* Global dark theme styles */
        html, body {
            background-color: #121212 !important;
            color: #f8f9fa !important;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        /* Override all background colors */
        .container, .row, .col, .col-md-4, .col-lg-4, .col-md-8, .col-lg-8, 
        .navbar, .navbar-dark, .bg-dark, .navbar-shop, .sticky-top, 
        .card, .card-body, .card-header, .card-footer,
        .hero-section, .featured-categories, .featured-products, 
        .category-card, .category-img-container, .category-overlay, 
        .section-title, .hero-title, .hero-subtitle, .hero-btn, 
        .pagination, .page-link, .page-item, .page-item.active, 
        .alert, .alert-success, .alert-danger, .alert-warning, .alert-info,
        .product-card, .product-img-container, .product-badge,
        .cart-item, .cart-summary, .cart-summary-title, .cart-summary-total,
        .checkout-form, .order-summary, .order-summary-title, .order-item,
        .product-detail, .product-meta, .related-product-card {
            background-color: #1a1a1a !important;
            color: #f8f9fa !important;
            transition: all 0.3s ease;
        }
        
        /* Ensure text is visible on black background */
        .text-dark, .text-body, .text-primary, .text-secondary, 
        .text-success, .text-danger, .text-warning, .text-info {
            color: #fff !important;
        }
        
        /* Ensure borders are visible */
        .border, .border-top, .border-right, .border-bottom, .border-left {
            border-color: #333 !important;
        }
        
        /* Form elements */
        .form-control, .form-select {
            background-color: #2a2a2a !important;
            color: #f8f9fa !important;
            border-color: #444 !important;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background-color: #333 !important;
            border-color: #4dabf7 !important;
            box-shadow: 0 0 0 0.2rem rgba(77, 171, 247, 0.25) !important;
        }
        
        /* Buttons */
        .btn-outline-primary {
            color: #4dabf7 !important;
            border-color: #4dabf7 !important;
        }
        
        .btn-outline-primary:hover {
            background-color: #4dabf7 !important;
            color: #000 !important;
        }
        
        /* Theme toggle button */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
            background-color: #4dabf7 !important;
            color: #000 !important;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body style="background-color: #000; color: #fff;">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark navbar-shop sticky-top" style="background-color: #111 !important;">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../logo.png" alt="Store Logo" height="40">
                <span class="ms-2">Online Store</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">
                            <i class="fas fa-home me-1"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'cart.php' ? 'active' : ''; ?>" href="cart.php">
                            <i class="fas fa-shopping-cart me-1"></i> Cart
                            <?php if (isset($_SESSION['shop_cart']) && count($_SESSION['shop_cart']) > 0): ?>
                                <span class="badge bg-danger"><?php echo count($_SESSION['shop_cart']); ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>
                <form class="d-flex me-2" action="index.php" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="search" placeholder="Search products...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </nav>
    
    <!-- Main Content Container -->
    <div class="container py-3" style="background-color: #000; color: #fff;">