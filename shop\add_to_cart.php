<?php
session_start();
require_once '../config/database.php';

// Initialize shop cart if it doesn't exist
if (!isset($_SESSION['shop_cart'])) {
    $_SESSION['shop_cart'] = [];
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['product_id']) && isset($_POST['quantity'])) {
    // Get form data
    $product_id = (int)$_POST['product_id'];
    $quantity = (int)$_POST['quantity'];
    $redirect = isset($_POST['redirect']) ? $_POST['redirect'] : 'product.php?id=' . $product_id;
    
    // Validate quantity
    if ($quantity <= 0) {
        $quantity = 1;
    }
    
    // Get database connection
    $conn = connectDB();
    
    // Get product details
    $query = "SELECT * FROM products WHERE id = ? AND is_online = 1 AND quantity > 0";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $product_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $product = $result->fetch_assoc();
        
        // Check if requested quantity is available
        if ($quantity > $product['quantity']) {
            $quantity = $product['quantity'];
        }
        
        // Check if product is already in cart
        $found = false;
        foreach ($_SESSION['shop_cart'] as $key => $item) {
            if ($item['id'] == $product_id) {
                // Update quantity
                $new_quantity = $item['quantity'] + $quantity;
                
                // Make sure we don't exceed available stock
                if ($new_quantity > $product['quantity']) {
                    $new_quantity = $product['quantity'];
                }
                
                $_SESSION['shop_cart'][$key]['quantity'] = $new_quantity;
                $_SESSION['shop_cart'][$key]['total'] = $new_quantity * $product['unit_price'];
                $found = true;
                break;
            }
        }
        
        // If not found, add to cart
        if (!$found) {
            $_SESSION['shop_cart'][] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'price' => $product['unit_price'],
                'quantity' => $quantity,
                'total' => $quantity * $product['unit_price'],
                'image_url' => $product['image_url']
            ];
        }
        
        // Set success message
        $_SESSION['cart_message'] = [
            'type' => 'success',
            'text' => 'Product added to cart successfully!'
        ];
    } else {
        // Product not found or not available
        $_SESSION['cart_message'] = [
            'type' => 'danger',
            'text' => 'Product is not available.'
        ];
    }
    
    // Close connection
    closeDB($conn);
    
    // Redirect back
    header("Location: $redirect");
    exit();
} else {
    // Invalid request
    header("Location: index.php");
    exit();
}