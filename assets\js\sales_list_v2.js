document.addEventListener('DOMContentLoaded', function() {
    console.log('Sales List V2 JS loaded');
    
    // Initialize all tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // ===== VIEW ORDER DETAILS =====
    const viewButtons = document.querySelectorAll('.view-order');
    const orderDetailsModal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
    const orderDetailsLoader = document.getElementById('orderDetailsLoader');
    const orderDetailsContent = document.getElementById('orderDetailsContent');
    const orderDetailsError = document.getElementById('orderDetailsError');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-order-id');
            console.log('Viewing order details for ID:', orderId);
            
            // Show loader, hide content and error
            orderDetailsLoader.style.display = 'block';
            orderDetailsContent.style.display = 'none';
            orderDetailsError.style.display = 'none';
            
            // Show modal
            orderDetailsModal.show();
            
            // Fetch order details
            fetch(`get_order_details.php?id=${orderId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loader
                    orderDetailsLoader.style.display = 'none';
                    
                    if (data.success) {
                        // Populate order details
                        document.getElementById('orderNumber').textContent = data.order.order_number || 'N/A';
                        document.getElementById('invoiceNumber').textContent = data.order.invoice_number || 'N/A';
                        document.getElementById('customerName').textContent = data.order.customer_name || 'N/A';
                        document.getElementById('orderDate').textContent = new Date(data.order.created_at).toLocaleString();
                        
                        document.getElementById('orderStatus').innerHTML = `
                            <span class="badge ${getStatusBadgeClass(data.order.status)}">
                                ${capitalizeFirstLetter(data.order.status)}
                            </span>
                        `;
                        document.getElementById('paymentType').textContent = data.order.payment_type || 'N/A';
                        document.getElementById('totalAmount').textContent = `₱${parseFloat(data.order.total_amount).toFixed(2)}`;
                        document.getElementById('createdBy').textContent = data.order.username || 'N/A';
                        
                        // Populate order items
                        const orderItemsTable = document.getElementById('orderItemsTable');
                        orderItemsTable.innerHTML = '';
                        
                        let subtotal = 0;
                        
                        data.items.forEach(item => {
                            const row = document.createElement('tr');
                            const itemTotal = parseFloat(item.price) * parseInt(item.quantity);
                            subtotal += itemTotal;
                            
                            row.innerHTML = `
                                <td>${item.product_name || 'N/A'}</td>
                                <td>${item.sku || 'N/A'}</td>
                                <td>${item.category_name || 'N/A'}</td>
                                <td class="amount-cell">₱${parseFloat(item.price).toFixed(2)}</td>
                                <td class="text-center">${item.quantity}</td>
                                <td class="amount-cell">₱${itemTotal.toFixed(2)}</td>
                            `;
                            
                            orderItemsTable.appendChild(row);
                        });
                        
                        // Calculate and display totals
                        const tax = parseFloat(data.order.tax_amount) || 0;
                        const grandTotal = parseFloat(data.order.total_amount) || 0;
                        
                        document.getElementById('subtotal').textContent = `₱${subtotal.toFixed(2)}`;
                        document.getElementById('tax').textContent = `₱${tax.toFixed(2)}`;
                        document.getElementById('grandTotal').textContent = `₱${grandTotal.toFixed(2)}`;
                        
                        // Show content
                        orderDetailsContent.style.display = 'block';
                    } else {
                        // Show error
                        document.getElementById('errorMessage').textContent = data.message || 'Failed to load order details';
                        orderDetailsError.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    orderDetailsLoader.style.display = 'none';
                    document.getElementById('errorMessage').textContent = 'An error occurred while loading order details';
                    orderDetailsError.style.display = 'block';
                });
        });
    });
    
    // Helper function to get status badge class
    function getStatusBadgeClass(status) {
        switch (status) {
            case 'pending': return 'bg-warning text-dark';
            case 'processing': return 'bg-info text-white';
            case 'completed': return 'bg-success text-white';
            case 'cancelled': return 'bg-danger text-white';
            case 'in_transit': return 'bg-primary text-white';
            case 'received': return 'bg-success text-white';
            case 'returned': return 'bg-secondary text-white';
            default: return 'bg-secondary text-white';
        }
    }
    
    // Helper function to capitalize first letter
    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
    
    // ===== UPDATE ORDER STATUS =====
    const updateButtons = document.querySelectorAll('.update-order');
    const updateOrderModal = new bootstrap.Modal(document.getElementById('updateOrderModal'));
    const updateOrderIdInput = document.getElementById('updateOrderId');
    const orderStatusSelect = document.getElementById('orderStatus');
    const updateOrderBtn = document.getElementById('updateOrderBtn');
    
    updateButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-order-id');
            const currentStatus = this.getAttribute('data-order-status');
            console.log('Updating order status for ID:', orderId, 'Current status:', currentStatus);
            
            // Set values in the form
            updateOrderIdInput.value = orderId;
            orderStatusSelect.value = currentStatus;
            
            // Show modal
            updateOrderModal.show();
        });
    });
    
    // Handle update button click
    if (updateOrderBtn) {
        updateOrderBtn.addEventListener('click', function() {
            const orderId = updateOrderIdInput.value;
            const newStatus = orderStatusSelect.value;
            
            // Show loading state
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Updating...';
            this.disabled = true;
            
            // Create form data
            const formData = new FormData();
            formData.append('order_id', orderId);
            formData.append('status', newStatus);
            formData.append('action', 'update_status');
            
            // Send AJAX request
            fetch('ajax/update_order.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                updateOrderBtn.innerHTML = 'Update';
                updateOrderBtn.disabled = false;
                
                if (data.success) {
                    // Close modal
                    updateOrderModal.hide();
                    
                    // Show success message
                    showToast('Success', data.message || 'Order status updated successfully', 'success');
                    
                    // Update status in the table
                    const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
                    if (orderRow) {
                        const statusCell = orderRow.querySelector('td:nth-child(6)');
                        if (statusCell) {
                            let statusBadge = '';
                            switch(newStatus) {
                                case 'pending':
                                    statusBadge = '<span class="badge bg-warning text-dark">Pending</span>';
                                    break;
                                case 'processing':
                                    statusBadge = '<span class="badge bg-info text-white">Processing</span>';
                                    break;
                                case 'completed':
                                    statusBadge = '<span class="badge bg-success text-white">Completed</span>';
                                    break;
                                case 'cancelled':
                                    statusBadge = '<span class="badge bg-danger text-white">Cancelled</span>';
                                    break;
                                case 'in_transit':
                                    statusBadge = '<span class="badge bg-primary text-white">In Transit</span>';
                                    break;
                                case 'received':
                                    statusBadge = '<span class="badge bg-success text-white">Received</span>';
                                    break;
                                case 'returned':
                                    statusBadge = '<span class="badge bg-secondary text-white">Returned</span>';
                                    break;
                                default:
                                    statusBadge = '<span class="badge bg-secondary text-white">' + newStatus.charAt(0).toUpperCase() + newStatus.slice(1) + '</span>';
                            }
                            statusCell.innerHTML = statusBadge;
                            
                            // Update data attribute for the update button
                            const updateButton = orderRow.querySelector('.update-order');
                            if (updateButton) {
                                updateButton.setAttribute('data-order-status', newStatus);
                            }
                        }
                    }
                } else {
                    // Show error message
                    showToast('Error', data.message || 'Failed to update order status', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                updateOrderBtn.innerHTML = 'Update';
                updateOrderBtn.disabled = false;
                showToast('Error', 'An error occurred while updating order status', 'error');
            });
        });
    }
    
    // ===== DELETE ORDER =====
    const deleteButtons = document.querySelectorAll('.delete-order');
    const deleteOrderModal = new bootstrap.Modal(document.getElementById('deleteOrderModal'));
    const confirmDeleteBtn = document.getElementById('confirmDeleteOrder');
    let orderToDelete = null;
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            orderToDelete = this.getAttribute('data-order-id');
            console.log('Deleting order ID:', orderToDelete);
            
            // Show confirmation modal
            deleteOrderModal.show();
        });
    });
    
    // Handle confirm delete button click
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            if (!orderToDelete) return;
            
            // Show loading state
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            this.disabled = true;
            
            // Create form data
            const formData = new FormData();
            formData.append('order_id', orderToDelete);
            formData.append('action', 'delete_order');
            
            // Send AJAX request
            fetch('ajax/update_order.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                confirmDeleteBtn.innerHTML = 'Delete';
                confirmDeleteBtn.disabled = false;
                
                if (data.success) {
                    // Close modal
                    deleteOrderModal.hide();
                    
                    // Show success message
                    showToast('Success', data.message || 'Order deleted successfully', 'success');
                    
                    // Remove row from table
                    const orderRow = document.querySelector(`tr[data-order-id="${orderToDelete}"]`);
                    if (orderRow) {
                        orderRow.remove();
                    }
                    
                    // Reset orderToDelete
                    orderToDelete = null;
                } else {
                    // Show error message
                    showToast('Error', data.message || 'Failed to delete order', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                confirmDeleteBtn.innerHTML = 'Delete';
                confirmDeleteBtn.disabled = false;
                showToast('Error', 'An error occurred while deleting order', 'error');
            });
        });
    }
    
    // ===== PRINT ORDER =====
    const printOrderBtn = document.getElementById('printOrderBtn');

    if (printOrderBtn) {
        printOrderBtn.addEventListener('click', function() {
            const printContent = document.getElementById('orderDetailsContent').cloneNode(true);
            
            // Create a new window for printing
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <html>
                <head>
                    <title>Order Details</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { padding: 20px; }
                        .amount-cell { text-align: right; }
                        @media print {
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>Order Details</h2>
                            <button class="btn btn-primary no-print" onclick="window.print()">Print</button>
                        </div>
                        ${printContent.outerHTML}
                    </div>
                </body>
                </html>
            `);
            
            printWindow.document.close();
            printWindow.focus();
        });
    }

    // ===== EXPORT CSV =====
    const exportCSVBtn = document.getElementById('exportCSVBtn');

    if (exportCSVBtn) {
        exportCSVBtn.addEventListener('click', function() {
            // Get current filter values
            const search = document.getElementById('search')?.value || '';
            const dateFrom = document.getElementById('date_from')?.value || '';
            const dateTo = document.getElementById('date_to')?.value || '';
            const payment = document.getElementById('payment')?.value || '';
            const status = document.getElementById('status')?.value || '';
            const customer = document.getElementById('customer')?.value || '';
            
            // Build query string
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (dateFrom) params.append('date_from', dateFrom);
            if (dateTo) params.append('date_to', dateTo);
            if (payment) params.append('payment', payment);
            if (status) params.append('status', status);
            if (customer) params.append('customer', customer);
            
            // Redirect to export_sales.php with the current filters
            window.location.href = 'export_sales.php?' + params.toString();
        });
    }
    
    // ===== HELPER FUNCTIONS =====
    // Toast notification function
    function showToast(title, message, type = 'info') {
        // Check if toast container exists, if not create it
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
        toastEl.id = toastId;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // Toast content
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong>: ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        // Add toast to container
        toastContainer.appendChild(toastEl);
        
        // Initialize and show toast
        const toast = new bootstrap.Toast(toastEl, { autohide: true, delay: 5000 });
        toast.show();
        
        // Remove toast element after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
});


