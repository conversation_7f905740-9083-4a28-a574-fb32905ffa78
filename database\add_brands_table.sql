-- Add brands table
CREATE TABLE IF NOT EXISTS brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add brand_id column to products table
ALTER TABLE products ADD COLUMN brand_id INT AFTER category_id;
ALTER TABLE products ADD CONSTRAINT fk_product_brand FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL;

-- Insert some sample brands
INSERT INTO brands (name, description) VALUES
('Samsung', 'South Korean multinational electronics company'),
('Apple', 'American multinational technology company'),
('Dell', 'American multinational computer technology company'),
('HP', 'American multinational information technology company'),
('Lenovo', 'Chinese multinational technology company');