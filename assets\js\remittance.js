document.addEventListener('DOMContentLoaded', function() {
    const remittanceBtn = document.getElementById('remittanceBtn');
    const remittanceModal = document.getElementById('remittanceModal');
    const saveRemittanceBtn = document.getElementById('saveRemittance');
    const remittanceAmountInput = document.getElementById('remittanceAmount');
    const remittanceDisplay = document.getElementById('remittanceDisplay');
    const cashOnHandDisplay = document.getElementById('cashOnHand');
    
    // Initialize remittance amount from localStorage
    let remittanceAmount = parseFloat(localStorage.getItem('remittanceAmount')) || 0;
    updateDisplays();

    // Add click event to remittance button
    if (remittanceBtn) {
        remittanceBtn.addEventListener('click', function() {
            remittanceAmountInput.value = remittanceAmount;
            const modal = new bootstrap.Modal(remittanceModal);
            modal.show();
        });
    }

    // Add click event to save remittance button
    if (saveRemittanceBtn) {
        saveRemittanceBtn.addEventListener('click', function() {
            const newAmount = parseFloat(remittanceAmountInput.value) || 0;
            remittanceAmount = newAmount;
            localStorage.setItem('remittanceAmount', remittanceAmount);
            updateDisplays();
            const modal = bootstrap.Modal.getInstance(remittanceModal);
            modal.hide();
        });
    }

    function updateDisplays() {
        // Update remittance display
        if (remittanceDisplay) {
            remittanceDisplay.textContent = remittanceAmount.toFixed(2);
        }

        // Get total sales amount
        const totalSalesElement = document.querySelector('tr.table-primary.fw-bold td:nth-child(3)');
        if (totalSalesElement) {
            const totalSales = parseFloat(totalSalesElement.textContent.replace('₱', '').replace(/,/g, '')) || 0;

            // Calculate cash on hand (total sales - remittance)
            const cashOnHand = totalSales - remittanceAmount;
            if (cashOnHandDisplay) {
                cashOnHandDisplay.textContent = cashOnHand.toFixed(2);
            }
        }
    }
});