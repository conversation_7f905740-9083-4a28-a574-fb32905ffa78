document.addEventListener('DOMContentLoaded', function() {
    const printPreviewBtn = document.getElementById('printPreviewBtn');

    if (printPreviewBtn) {
        printPreviewBtn.addEventListener('click', function() {
            // Add print-preview class to body for print-specific styling
            document.body.classList.add('print-preview');

            // Hide elements that shouldn't be printed
            const elementsToHide = document.querySelectorAll('.btn, nav, form, .pagination');
            elementsToHide.forEach(element => {
                element.style.display = 'none';
            });

            // Open the print dialog
            window.print();

            // After printing, restore the elements
            window.onafterprint = function() {
                document.body.classList.remove('print-preview');
                elementsToHide.forEach(element => {
                    element.style.display = '';
                });
            };
        });
    }
});