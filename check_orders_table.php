<?php
// Database configuration
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Check if branch_name column exists in orders table
$result = $conn->query("SHOW COLUMNS FROM orders LIKE 'branch_name'");

if ($result->num_rows == 0) {
    // Column doesn't exist, create it
    echo "branch_name column doesn't exist in orders table. Adding it now...\n";
    $alter_query = "ALTER TABLE orders ADD COLUMN branch_name VARCHAR(100) DEFAULT 'Main Branch' AFTER payment_type";
    
    if ($conn->query($alter_query) === TRUE) {
        echo "branch_name column added successfully.\n";
    } else {
        echo "Error adding branch_name column: " . $conn->error . "\n";
    }
} else {
    echo "branch_name column already exists in orders table.\n";
}

// Check if invoice_number column exists in orders table
$result = $conn->query("SHOW COLUMNS FROM orders LIKE 'invoice_number'");

if ($result->num_rows == 0) {
    // Column doesn't exist, create it
    echo "invoice_number column doesn't exist in orders table. Adding it now...\n";
    $alter_query = "ALTER TABLE orders ADD COLUMN invoice_number VARCHAR(50) AFTER order_number";
    
    if ($conn->query($alter_query) === TRUE) {
        echo "invoice_number column added successfully.\n";
    } else {
        echo "Error adding invoice_number column: " . $conn->error . "\n";
    }
} else {
    echo "invoice_number column already exists in orders table.\n";
}

// Close connection
closeDB($conn);

echo "Table check complete.\n";
?>