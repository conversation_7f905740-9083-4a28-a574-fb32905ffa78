<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Add print-specific stylesheet and scripts
echo '<link rel="stylesheet" href="assets/css/print-styles.css">';
echo '<script src="assets/js/remittance.js" defer></script>';
echo '<script src="assets/js/print-preview.js" defer></script>';

// Add CSS for right-aligned amounts
echo '<style>
    .text-right {
        text-align: right !important;
    }
    .amount-cell {
        text-align: right !important;
    }
</style>';

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$date_from = date('Y-m-d'); // Set default to today
$date_to = date('Y-m-d');   // Set default to today
$payment_filter = '';
$sort_by = 'o.created_at';
$sort_order = 'DESC';
$page = 1;
$items_per_page = 10;

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['date_from'])) {
    $date_from = $_GET['date_from'];
}

if (isset($_GET['date_to'])) {
    $date_to = $_GET['date_to'];
}

if (isset($_GET['payment'])) {
    $payment_filter = $_GET['payment'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Build base query for counting total records
$count_query = "SELECT COUNT(DISTINCT o.id) as total FROM orders o 
              WHERE o.branch_name = ?";

// Build base query for fetching orders
$query = "SELECT o.*, u.username 
          FROM orders o 
          LEFT JOIN users u ON o.created_by = u.id 
          WHERE o.branch_name = ?";

// Add search condition if provided
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (o.order_number LIKE ? OR o.invoice_number LIKE ?)";
    $count_query .= " AND (o.order_number LIKE ? OR o.invoice_number LIKE ?)";
}

// Add date range condition if provided
if (!empty($date_from)) {
    $query .= " AND DATE(o.created_at) >= ?";
    $count_query .= " AND DATE(o.created_at) >= ?";
}

if (!empty($date_to)) {
    $query .= " AND DATE(o.created_at) <= ?";
    $count_query .= " AND DATE(o.created_at) <= ?";
}

// Add payment type filter if provided
if (!empty($payment_filter)) {
    $query .= " AND o.payment_type = ?";
    $count_query .= " AND o.payment_type = ?";
}

// Prepare count statement
$count_stmt = $conn->prepare($count_query);

// Bind parameters for count query
$bind_types = "s"; // branch_name
$bind_params = array(&$bind_types, &$branch_name);

if (!empty($search)) {
    $bind_types .= "sss"; // search term x3
    $bind_params[] = &$search_term;
    $bind_params[] = &$search_term;
    $bind_params[] = &$search_term;
}

if (!empty($date_from)) {
    $bind_types .= "s"; // date_from
    $bind_params[] = &$date_from;
}

if (!empty($date_to)) {
    $bind_types .= "s"; // date_to
    $bind_params[] = &$date_to;
}

if (!empty($payment_filter)) {
    $bind_types .= "s"; // payment_type
    $bind_params[] = &$payment_filter;
}

// Call bind_param with dynamic parameters
call_user_func_array(array($count_stmt, 'bind_param'), $bind_params);

// Execute count query
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$count_stmt->close();

// Calculate total pages
$total_pages = ceil($total_records / $items_per_page);
if ($page > $total_pages && $total_pages > 0) {
    $page = $total_pages;
}

// Add sorting and pagination
$query .= " ORDER BY {$sort_by} {$sort_order} LIMIT " . (($page - 1) * $items_per_page) . ", {$items_per_page}";

// Prepare statement for orders
$stmt = $conn->prepare($query);

// Bind parameters for main query (same as count query)
call_user_func_array(array($stmt, 'bind_param'), $bind_params);

// Execute main query
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();

// Get all payment types for filter dropdown
$payment_types_query = "SELECT DISTINCT payment_type FROM orders WHERE branch_name = ? ORDER BY payment_type ASC";
$payment_stmt = $conn->prepare($payment_types_query);
$payment_stmt->bind_param("s", $branch_name);
$payment_stmt->execute();
$payment_types_result = $payment_stmt->get_result();
$payment_stmt->close();

// Get sales data grouped by category
$category_query = "SELECT c.name as category_name, SUM(oi.total_price) as category_total, COUNT(oi.id) as item_count 
                 FROM orders o 
                 JOIN order_items oi ON o.id = oi.order_id 
                 JOIN products p ON oi.product_id = p.id 
                 JOIN categories c ON p.category_id = c.id 
                 WHERE o.branch_name = ? AND o.status != 'cancelled'";

// Add date range condition if provided
if (!empty($date_from)) {
    $category_query .= " AND DATE(o.created_at) >= ?";
}

if (!empty($date_to)) {
    $category_query .= " AND DATE(o.created_at) <= ?";
}

// Add payment type filter if provided
if (!empty($payment_filter)) {
    $category_query .= " AND o.payment_type = ?";
}

$category_query .= " GROUP BY c.name ORDER BY category_total DESC";

// Prepare statement for category totals
$cat_stmt = $conn->prepare($category_query);

// Bind parameters for category query
$cat_bind_types = "s"; // branch_name
$cat_bind_params = array(&$cat_bind_types, &$branch_name);

if (!empty($date_from)) {
    $cat_bind_types .= "s"; // date_from
    $cat_bind_params[] = &$date_from;
}

if (!empty($date_to)) {
    $cat_bind_types .= "s"; // date_to
    $cat_bind_params[] = &$date_to;
}

if (!empty($payment_filter)) {
    $cat_bind_types .= "s"; // payment_type
    $cat_bind_params[] = &$payment_filter;
}

// Call bind_param with dynamic parameters
call_user_func_array(array($cat_stmt, 'bind_param'), $cat_bind_params);

// Execute category query
$cat_stmt->execute();
$category_result = $cat_stmt->get_result();
$cat_stmt->close();

// Calculate grand total
$grand_total_query = "SELECT SUM(total_amount) as grand_total 
                     FROM orders 
                     WHERE branch_name = ? AND status != 'cancelled'";

// Add date range condition if provided
if (!empty($date_from)) {
    $grand_total_query .= " AND DATE(created_at) >= ?";
}

if (!empty($date_to)) {
    $grand_total_query .= " AND DATE(created_at) <= ?";
}

// Add payment type filter if provided
if (!empty($payment_filter)) {
    $grand_total_query .= " AND payment_type = ?";
}

// Prepare statement for grand total
$total_stmt = $conn->prepare($grand_total_query);

// Bind parameters for grand total query
$total_bind_types = "s"; // branch_name
$total_bind_params = array(&$total_bind_types, &$branch_name);

if (!empty($date_from)) {
    $total_bind_types .= "s"; // date_from
    $total_bind_params[] = &$date_from;
}

if (!empty($date_to)) {
    $total_bind_types .= "s"; // date_to
    $total_bind_params[] = &$date_to;
}

if (!empty($payment_filter)) {
    $total_bind_types .= "s"; // payment_type
    $total_bind_params[] = &$payment_filter;
}

// Call bind_param with dynamic parameters
call_user_func_array(array($total_stmt, 'bind_param'), $total_bind_params);

// Execute grand total query
$total_stmt->execute();
$grand_total_result = $total_stmt->get_result();
$grand_total = $grand_total_result->fetch_assoc()['grand_total'] ?: 0;
$total_stmt->close();

// Close connection
$conn->close();
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Sales Transactions</h1>
        <div>
            <button id="printPreviewBtn" class="btn btn-success me-2">
                <i class="fas fa-print me-2"></i> Print Preview
            </button>
            <a href="sales.php" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2"></i> New Sale
            </a>
            <button id="remittanceBtn" class="btn btn-info">
                <i class="fas fa-money-bill-wave me-2"></i> Remittance
            </button>
        </div>
    </div>

    <!-- Remittance Modal -->
    <div class="modal fade" id="remittanceModal" tabindex="-1" aria-labelledby="remittanceModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="remittanceModalLabel">Enter Remittance Amount</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="remittanceForm">
                        <div class="mb-3">
                            <label for="remittanceAmount" class="form-label">Remittance Amount</label>
                            <input type="number" class="form-control" id="remittanceAmount" name="remittanceAmount" step="0.01" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveRemittance">Save Remittance</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="sales_list.php" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Order #, Customer, Invoice #">
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-2">
                    <label for="payment" class="form-label">Payment Type</label>
                    <select class="form-select" id="payment" name="payment">
                        <option value="">All Payment Types</option>
                        <?php while ($payment_type = $payment_types_result->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($payment_type['payment_type']); ?>" <?php echo ($payment_filter == $payment_type['payment_type']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($payment_type['payment_type']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i> Filter
                    </button>
                    <a href="sales_list.php" class="btn btn-secondary">
                        <i class="fas fa-redo me-2"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Category Summary -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Sales by Category</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Items Sold</th>
                            <th>Total Sales</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($category_result->num_rows > 0): ?>
                            <?php while ($category = $category_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($category['category_name']); ?></td>
                                    <td><?php echo $category['item_count']; ?></td>
                                    <td class="amount-cell">₱<?php echo number_format($category['category_total'], 2); ?></td>
                                    <td>
                                        <?php 
                                        $percentage = ($grand_total > 0) ? ($category['category_total'] / $grand_total) * 100 : 0;
                                        echo number_format($percentage, 2) . '%';
                                        ?>
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                            <tr class="table-primary fw-bold">
                                <td>Grand Total</td>
                                <td></td>
                                <td class="amount-cell">₱<?php echo number_format($grand_total, 2); ?></td>
                                <td>100%</td>
                            </tr>
                            <tr class="table-info fw-bold">
                                <td>Remittance</td>
                                <td></td>
                                <td class="amount-cell">₱<span id="remittanceDisplay">0.00</span></td>
                                <td></td>
                            </tr>
                            <tr class="table-success fw-bold">
                                <td>Cash on Hand</td>
                                <td></td>
                                <td class="amount-cell">₱<span id="cashOnHand"><?php echo number_format($grand_total, 2); ?></span></td>
                                <td></td>
                            </tr>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center">No sales data found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Sales Transactions -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Sales Transactions</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="sales_list.php?search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=order_number&sort_order=<?php echo ($sort_by == 'order_number' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>">
                                    Order #
                                    <?php if ($sort_by == 'order_number'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="sales_list.php?search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=invoice_number&sort_order=<?php echo ($sort_by == 'invoice_number' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>">
                                    Invoice #
                                    <?php if ($sort_by == 'invoice_number'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>

                            <th>
                                <a href="sales_list.php?search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=total_amount&sort_order=<?php echo ($sort_by == 'total_amount' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>">
                                    Amount
                                    <?php if ($sort_by == 'total_amount'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="sales_list.php?search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=payment_type&sort_order=<?php echo ($sort_by == 'payment_type' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>">
                                    Payment
                                    <?php if ($sort_by == 'payment_type'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="sales_list.php?search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=status&sort_order=<?php echo ($sort_by == 'status' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>">
                                    Status
                                    <?php if ($sort_by == 'status'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="sales_list.php?search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=o.created_at&sort_order=<?php echo ($sort_by == 'o.created_at' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>">
                                    Date
                                    <?php if ($sort_by == 'o.created_at'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($order = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                    <td><?php echo htmlspecialchars($order['invoice_number'] ?: 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                    <td class="amount-cell">₱<?php echo number_format($order['total_amount'], 2); ?></td>
                                    <td><?php echo htmlspecialchars($order['payment_type']); ?></td>
                                    <td>
                                        <?php if ($order['status'] == 'pending'): ?>
                                            <span class="badge badge-pending">Pending</span>
                                        <?php elseif ($order['status'] == 'processing'): ?>
                                            <span class="badge badge-processing">Processing</span>
                                        <?php elseif ($order['status'] == 'completed'): ?>
                                            <span class="badge badge-completed">Completed</span>
                                        <?php elseif ($order['status'] == 'cancelled'): ?>
                                            <span class="badge badge-cancelled">Cancelled</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y h:i A', strtotime($order['created_at'])); ?></td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info view-order" data-order-id="<?php echo $order['id']; ?>">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-warning update-order" data-order-id="<?php echo $order['id']; ?>" data-order-status="<?php echo $order['status']; ?>">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-danger delete-order" data-order-id="<?php echo $order['id']; ?>">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">No orders found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mt-4">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="sales_list.php?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                Previous
                            </a>
                        </li>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="sales_list.php?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="sales_list.php?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&payment=<?php echo urlencode($payment_filter); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="orderDetailsModalLabel">Order Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center" id="orderDetailsLoader">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading order details...</p>
                </div>
                <div id="orderDetailsContent" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Order Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Order #</th>
                                    <td id="orderNumber"></td>
                                </tr>
                                <tr>
                                    <th>Invoice #</th>
                                    <td id="invoiceNumber"></td>
                                </tr>
                                <tr>
                                    <th>Customer</th>
                                    <td id="customerName"></td>
                                </tr>
                                <tr>
                                    <th>Date</th>
                                    <td id="orderDate"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Payment Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Status</th>
                                    <td id="orderStatus"></td>
                                </tr>
                                <tr>
                                    <th>Payment Type</th>
                                    <td id="paymentType"></td>
                                </tr>
                                <tr>
                                    <th>Total Amount</th>
                                    <td id="totalAmount"></td>
                                </tr>
                                <tr>
                                    <th>Created By</th>
                                    <td id="createdBy"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <h5>Order Items</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Category</th>
                                    <th>Unit Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody id="orderItemsTable">
                                <!-- Order items will be inserted here -->
                            </tbody>
                            <tfoot>
                                <tr class="table-primary">
                                    <th colspan="5" class="text-end">Subtotal:</th>
                                    <td id="subtotal"></td>
                                </tr>
                                <tr>
                                    <th colspan="5" class="text-end">Tax:</th>
                                    <td id="tax"></td>
                                </tr>
                                <tr class="fw-bold">
                                    <th colspan="5" class="text-end">Grand Total:</th>
                                    <td id="grandTotal"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div id="orderDetailsError" class="alert alert-danger" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i> <span id="errorMessage"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Order Confirmation Modal -->
<div class="modal fade" id="deleteOrderModal" tabindex="-1" aria-labelledby="deleteOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteOrderModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this sales entry? This action cannot be undone.</p>
                <p><strong>Warning:</strong> Deleting this entry will permanently remove it from the database.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteOrder">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Print Preview Modal -->
<div class="modal fade" id="printPreviewModal" tabindex="-1" aria-labelledby="printPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="printPreviewModalLabel">Sales Report Print Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="printArea" class="p-3">
                    <div class="text-center mb-4">
                        <h1 class="company-name mb-2">MUSAR MUSIC CORPORATION</h1>
                        
                        <h2 class="report-title mb-2">Daily Sales Report</h2>
                        <p class="report-date mb-0" id="printReportDate"></p>
                        <p class="branch-name" id="printBranchName"></p>
                    </div>
                    <div id="printContent" class="sales-report-content"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="printReportBtn">
                    <i class="fas fa-print me-2"></i> Print
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Update Order Status Modal -->
<div class="modal fade" id="updateOrderModal" tabindex="-1" aria-labelledby="updateOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="updateOrderModalLabel">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="updateOrderForm">
                    <input type="hidden" id="updateOrderId" name="order_id">
                    <div class="mb-3">
                        <label for="orderStatus" class="form-label">Status of the Order</label>
                        <select class="form-select" id="orderStatus" name="status" required>
                            <option value="pending">Pending</option>
                            <option value="in_transit">In Transit</option>
                            <option value="received">Received</option>
                            <option value="completed">Completed</option>
                            <option value="returned">Returned</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-warning" id="updateOrderBtn">Update</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Print Preview Functionality
        const printPreviewBtn = document.getElementById('printPreviewBtn');
        const printPreviewModal = new bootstrap.Modal(document.getElementById('printPreviewModal'));
        const printReportBtn = document.getElementById('printReportBtn');
        
        // Set branch name in print preview
        document.getElementById('printBranchName').textContent = '<?php echo htmlspecialchars($branch_name); ?>';
        
        // Function to generate sales report
        function generateSalesReport() {
            // Set report date range
            let reportDateText = 'All Transactions';
            if ('<?php echo $date_from; ?>' && '<?php echo $date_to; ?>') {
                reportDateText = 'From <?php echo date("M d, Y", strtotime($date_from)); ?> to <?php echo date("M d, Y", strtotime($date_to)); ?>';
            } else if ('<?php echo $date_from; ?>') {
                reportDateText = 'From <?php echo date("M d, Y", strtotime($date_from)); ?>';
            } else if ('<?php echo $date_to; ?>') {
                reportDateText = 'Until <?php echo date("M d, Y", strtotime($date_to)); ?>';
            }
            document.getElementById('printReportDate').textContent = 'Report Date: ' + reportDateText;
            
            // Prepare parameters for the fetch request
            let params = new URLSearchParams();
            if ('<?php echo $date_from; ?>') params.append('date_from', '<?php echo $date_from; ?>');
            if ('<?php echo $date_to; ?>') params.append('date_to', '<?php echo $date_to; ?>');
            if ('<?php echo $payment_filter; ?>') params.append('payment', '<?php echo $payment_filter; ?>');
            params.append('branch_name', '<?php echo htmlspecialchars($branch_name); ?>');
            
            // Fetch sales data for the report
            fetch('generate_sales_report.php?' + params.toString())
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const printContent = document.getElementById('printContent');
                        printContent.innerHTML = '';
                        
                        if (data.payment_groups.length === 0) {
                            printContent.innerHTML = '<div class="alert alert-info">No sales data found for the selected criteria.</div>';
                            return;
                        }
                        
                        // Retail Sales Section
                        const retailHeader = document.createElement('div');
                        retailHeader.className = 'mt-3 mb-2';
                        
                        printContent.appendChild(retailHeader);
                        
                        // Group by payment type
                        data.payment_groups.forEach(paymentGroup => {
                            // Payment type header
                            const paymentHeader = document.createElement('div');
                            paymentHeader.className = 'payment-group';
                            paymentHeader.innerHTML = `<p>${paymentGroup.payment_type}</p>`;
                            printContent.appendChild(paymentHeader);
                            
                            let paymentTotal = 0;
                            
                            // Group by invoice
                            paymentGroup.invoices.forEach(invoice => {
                                // Invoice header
                                const invoiceHeader = document.createElement('div');
                                invoiceHeader.className = 'invoice-group';
                                invoiceHeader.innerHTML = `
                                    <h5>Invoice #: ${invoice.invoice_number || 'N/A'}</h5>
                                   
                                `;
                                printContent.appendChild(invoiceHeader);
                                
                                // Invoice items table
                                const invoiceTable = document.createElement('table');
                                invoiceTable.className = 'table table-bordered table-sm';
                                invoiceTable.innerHTML = `
                                    <thead>
                                        <tr>
                                            <th>Description</th>
                                            <th width="10%">Qty Sold</th>
                                            <th width="15%">Selling Price</th>
                                            <th width="10%">Discount</th>
                                            <th width="15%">Total</th>
                                            <th width="15%">Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoice-${invoice.id}-items"></tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="4" class="text-end">Invoice-total</th>
                                            <th class="amount-cell">${parseFloat(invoice.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                `;
                                printContent.appendChild(invoiceTable);
                                
                                // Add invoice items
                                const invoiceItemsBody = document.getElementById(`invoice-${invoice.id}-items`);
                                invoice.items.forEach(item => {
                                    const row = document.createElement('tr');
                                    row.innerHTML = `
                                        <td>${item.product_name}</td>
                                        <td>${item.quantity}</td>
                                        <td class="amount-cell">${parseFloat(item.actual_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                        <td class="amount-cell">0.00</td>
                                        <td class="amount-cell">${parseFloat(item.total_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                        <td></td>
                                    `;
                                    invoiceItemsBody.appendChild(row);
                                });
                                
                                paymentTotal += parseFloat(invoice.total_amount);
                            });
                            
                            // Payment type total
                            const paymentTotalDiv = document.createElement('div');
                            paymentTotalDiv.className = 'mt-2 mb-2';
                            paymentTotalDiv.innerHTML = `
                                <div class="alert alert-primary py-1 px-2">
                                    <strong>Payment Type Total (${paymentGroup.payment_type}):</strong> 
                                    <span class="amount-cell">₱${paymentTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                                </div>
                            `;
                            printContent.appendChild(paymentTotalDiv);
                        });
                        
                        // Grand total
                        const grandTotalDiv = document.createElement('div');
                        grandTotalDiv.className = 'mt-3 mb-2';
                        grandTotalDiv.innerHTML = `
                            <div class="alert alert-success py-1 px-2">
                                <h4 class="mb-0">Total Retail Sales: <span class="amount-cell">${parseFloat(data.grand_total).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span></h4>
                            </div>
                        `;
                        printContent.appendChild(grandTotalDiv);
                        
                        // School Fees Section
                        if (data.tuition_payment_groups.length > 0) {
                            const tuitionHeader = document.createElement('div');
                            tuitionHeader.className = 'mt-4 mb-2';
                            tuitionHeader.innerHTML = `<h3>SCHOOL TRANSACTIONS</h3>`;
                            printContent.appendChild(tuitionHeader);
                            
                            // Create a container for tuition and summary tables to be side by side
                            const flexContainer = document.createElement('div');
                            flexContainer.className = 'flex-container';
                            printContent.appendChild(flexContainer);
                            
                            // Tuition table container
                            const tuitionTableContainer = document.createElement('div');
                            tuitionTableContainer.className = 'flex-grow-1 me-2';
                            flexContainer.appendChild(tuitionTableContainer);
                            
                            let tuitionGrandTotal = 0;
                            
                            // Group by payment type
                            data.tuition_payment_groups.forEach(paymentGroup => {
                                // Payment type header
                                const paymentHeader = document.createElement('div');
                                paymentHeader.className = 'mt-3 mb-1';
                                paymentHeader.innerHTML = `<h4>Payment Type: ${paymentGroup.payment_type}</h4>`;
                                tuitionTableContainer.appendChild(paymentHeader);
                                
                                let paymentTotal = 0;
                                
                                // Group by invoice
                                paymentGroup.invoices.forEach(invoice => {
                                    // Invoice header
                                    const invoiceHeader = document.createElement('div');
                                    invoiceHeader.className = 'mt-2 mb-1';
                                    invoiceHeader.innerHTML = `
                                        <h5>Invoice #: ${invoice.invoice_number || 'N/A'}</h5>

                                        <p>
                                            Customer: ${invoice.customer_name} | 
                                            Email: ${invoice.customer_email || 'N/A'} | 
                                            Phone: ${invoice.customer_phone || 'N/A'} | 
                                            Date: ${new Date(invoice.created_at).toLocaleString()}
                                        </p>
                                    `;
                                    tuitionTableContainer.appendChild(invoiceHeader);
                                    
                                    // Invoice items table
                                    const invoiceTable = document.createElement('table');
                                    invoiceTable.className = 'table table-bordered table-sm';
                                    invoiceTable.innerHTML = `
                                        <thead class="table-light">
                                            <tr>
                                                <th>Description</th>
                                                <th width="10%">Qty</th>
                                                <th width="15%">Price</th>
                                                <th width="15%">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody id="tuition-invoice-${invoice.id}-items"></tbody>
                                    `;
                                    tuitionTableContainer.appendChild(invoiceTable);
                                    
                                    // Add invoice items
                                    const invoiceItemsBody = document.getElementById(`tuition-invoice-${invoice.id}-items`);
                                    let invoiceTotal = 0;
                                    
                                    invoice.items.forEach(item => {
                                        const itemTotal = parseFloat(item.total_price);
                                        invoiceTotal += itemTotal;
                                        
                                        const row = document.createElement('tr');
                                        row.innerHTML = `
                                            <td>${item.product_name}</td>
                                            <td>${item.quantity}</td>
                                            <td class="amount-cell">${parseFloat(item.actual_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                            <td class="amount-cell">${itemTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                        `;
                                        invoiceItemsBody.appendChild(row);
                                    });
                                    
                                    // Add invoice total row
                                    const totalRow = document.createElement('tr');
                                    totalRow.className = 'table-light';
                                    totalRow.innerHTML = `
                                        <td colspan="3" class="text-end"><strong>Invoice Total:</strong></td>
                                        <td><strong>₱${invoiceTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong></td>
                                    `;
                                    invoiceItemsBody.appendChild(totalRow);
                                    
                                    paymentTotal += invoiceTotal;
                                });
                                
                                // Payment type total
                                const paymentTotalDiv = document.createElement('div');
                                paymentTotalDiv.className = 'mt-2 mb-2';
                                paymentTotalDiv.innerHTML = `
                                    <div class="alert alert-primary py-1 px-2">
                                        <strong>Cash Sales Total</strong> 
                                        <span class="amount-cell">${paymentTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                                    </div>
                                `;
                                tuitionTableContainer.appendChild(paymentTotalDiv);
                                
                                tuitionGrandTotal += paymentTotal;
                            });
                            
                            // Tuition grand total
                            const tuitionTotalDiv = document.createElement('div');
                            tuitionTotalDiv.className = 'mt-3 mb-2';
                            tuitionTotalDiv.innerHTML = `
                                <div class="alert alert-success py-1 px-2">
                                    <h4 class="mb-0">Cash Payments Total: <span class="amount-cell">${parseFloat(data.tuition_total).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span></h4>
                                </div>
                            `;
                            tuitionTableContainer.appendChild(tuitionTotalDiv);
                            
                            // Summary and Payment Distribution Table (side by side with tuition)
                            const summaryContainer = document.createElement('div');
                            summaryContainer.className = 'flex-shrink-0 ms-2';
                            summaryContainer.style.minWidth = '300px';
                            flexContainer.appendChild(summaryContainer);
                            
                            // Payment Distribution Table
                            const summaryHeader = document.createElement('div');
                            summaryHeader.className = 'mt-3 mb-1';
                            summaryHeader.innerHTML = `<h4>FINANCIAL SUMMARY</h4>`;
                            summaryContainer.appendChild(summaryHeader);
                            
                            const summaryTable = document.createElement('table');
                            summaryTable.className = 'table table-bordered table-sm';
                            summaryTable.innerHTML = `
                                <thead class="table-light">
                                    <tr>
                                        <th>Payment Type</th>
                                        <th>Sales</th>
                                        <th>Tuition</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody id="summary-table-body"></tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <th>Total Sales</th>
                                        <th class="amount-cell">₱${parseFloat(data.grand_total).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</th>
                                        <th class="amount-cell">₱${parseFloat(data.tuition_total).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</th>
                                        <th class="amount-cell">₱${(parseFloat(data.grand_total) + parseFloat(data.tuition_total)).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</th>
                                    </tr>
                                </tfoot>
                            `;
                            summaryContainer.appendChild(summaryTable);
                            
                            const summaryTableBody = document.getElementById('summary-table-body');
                            data.summary.forEach(item => {
                                const row = document.createElement('tr');
                                const salesTotal = parseFloat(item.sales_total);
                                const tuitionTotal = parseFloat(item.tuition_total);
                                const total = salesTotal + tuitionTotal;
                                
                                row.innerHTML = `
                                    <td>${item.payment_type}</td>
                                    <td class="amount-cell">₱${salesTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                    <td class="amount-cell">₱${tuitionTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                    <td class="amount-cell">₱${total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                `;
                                summaryTableBody.appendChild(row);
                            });
                            
                            // Cash Fund Summary
                        
                        }
                        
                        // Expenses Section
                        if (data.expenses.length > 0) {
                            const expensesHeader = document.createElement('div');
                            expensesHeader.className = 'mt-4 mb-2';
                            expensesHeader.innerHTML = `<h3>EXPENSES</h3>`;
                            printContent.appendChild(expensesHeader);
                            
                            const expensesTable = document.createElement('table');
                            expensesTable.className = 'table table-bordered table-sm';
                            expensesTable.innerHTML = `
                                <thead>
                                    <tr>
                                        <th>Payee</th>
                                        <th>Explanation</th>
                                        <th>Reference</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody id="expenses-table-body"></tbody>
                                <tfoot>
                                    <tr class="table-danger">
                                        <th colspan="3" class="text-end">Total Expenses</th>
                                        <th class="amount-cell">${parseFloat(data.expenses_total).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</th>
                                    </tr>
                                </tfoot>
                            `;
                            printContent.appendChild(expensesTable);
                            
                            const expensesTableBody = document.getElementById('expenses-table-body');
                            data.expenses.forEach(expense => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${expense.category}</td>
                                    <td>${expense.description}</td>
                                    <td>-</td>
                                    <td class="amount-cell">${parseFloat(expense.amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                `;
                                expensesTableBody.appendChild(row);
                            });
                        }
                        
                        // Show the modal
                        printPreviewModal.show();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while generating the report.');
                });
        }
        
        // Print preview button click event
        printPreviewBtn.addEventListener('click', function() {
            // Generate the sales report and show the modal
            generateSalesReport();
        });
        
        // Print report button click event
        printReportBtn.addEventListener('click', function() {
            const printContents = document.getElementById('printArea').innerHTML;
            const originalContents = document.body.innerHTML;
            
            // Use the external print-styles.css instead of inline styles
            // Create a hidden iframe for printing
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            // Write the content to the iframe with the print-styles.css
            iframe.contentDocument.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Sales Report</title>
                    <link rel="stylesheet" href="assets/css/print-styles.css">
                </head>
                <body>
                    <div class="print-container">${printContents}</div>
                </body>
                </html>
            `);
            
            iframe.contentDocument.close();
            
            // Wait a moment for styles to load, then print directly
            setTimeout(function() {
                iframe.contentWindow.print();
                
                // Remove the iframe after printing
                setTimeout(function() {
                    document.body.removeChild(iframe);
                }, 100);
            }, 500);
        });
        // Variables for delete functionality
        const deleteButtons = document.querySelectorAll('.delete-order');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteOrderModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteOrder');
        let orderIdToDelete = null;
        
        // Variables for view order functionality
        const viewButtons = document.querySelectorAll('.view-order');
        const orderDetailsModal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
        const orderDetailsLoader = document.getElementById('orderDetailsLoader');
        const orderDetailsContent = document.getElementById('orderDetailsContent');
        const orderDetailsError = document.getElementById('orderDetailsError');
        
        // Add click event to all view buttons
        viewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const orderId = this.getAttribute('data-order-id');
                
                // Show modal with loader
                orderDetailsLoader.style.display = 'block';
                orderDetailsContent.style.display = 'none';
                orderDetailsError.style.display = 'none';
                orderDetailsModal.show();
                
                // Fetch order details
                fetch('get_order_details.php?id=' + orderId)
                    .then(response => response.json())
                    .then(data => {
                        // Hide loader
                        orderDetailsLoader.style.display = 'none';
                        
                        if (data.success) {
                            // Populate order details
                            const order = data.order;
                            const items = data.items;
                            
                            // Order information
                            document.getElementById('orderNumber').textContent = order.order_number;
                            document.getElementById('invoiceNumber').textContent = order.invoice_number || 'N/A';
                            document.getElementById('customerName').textContent = order.customer_name;
                            document.getElementById('orderDate').textContent = new Date(order.created_at).toLocaleString();
                            
                            // Payment information
                            let statusBadge = '';
                            if (order.status === 'pending') {
                                statusBadge = '<span class="badge badge-pending">Pending</span>';
                            } else if (order.status === 'processing') {
                                statusBadge = '<span class="badge badge-processing">Processing</span>';
                            } else if (order.status === 'completed') {
                                statusBadge = '<span class="badge badge-completed">Completed</span>';
                            } else if (order.status === 'cancelled') {
                                statusBadge = '<span class="badge badge-cancelled">Cancelled</span>';
                            }
                            
                            document.getElementById('orderStatus').innerHTML = statusBadge;
                            document.getElementById('paymentType').textContent = order.payment_type;
                            document.getElementById('totalAmount').textContent = '₱' + parseFloat(order.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            document.getElementById('createdBy').textContent = order.username || 'N/A';
                            
                            // Order items
                            const itemsTable = document.getElementById('orderItemsTable');
                            itemsTable.innerHTML = '';
                            
                            let subtotal = 0;
                            
                            items.forEach(item => {
                                const row = document.createElement('tr');
                                
                                // Calculate item total based on unit_price * quantity
                                const itemTotal = parseFloat(item.unit_price) * parseFloat(item.quantity);
                                subtotal += itemTotal;
                                
                                row.innerHTML = `
                                    <td>${item.product_name}</td>
                                    <td>${item.sku || 'N/A'}</td>
                                    <td>${item.category_name || 'N/A'}</td>
                                    <td class="amount-cell">${parseFloat(item.unit_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                    <td>${item.quantity}</td>
                                    <td class="amount-cell">${itemTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                `;
                                
                                itemsTable.appendChild(row);
                            });
                            
                            // Calculate totals
                            const tax = parseFloat(order.tax_amount) || 0;
                            const grandTotal = parseFloat(order.total_amount);
                            
                            document.getElementById('subtotal').textContent = '₱' + subtotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            document.getElementById('tax').textContent = '₱' + tax.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            document.getElementById('grandTotal').textContent = '₱' + grandTotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            
                            // Show content
                            orderDetailsContent.style.display = 'block';
                        } else {
                            // Show error
                            document.getElementById('errorMessage').textContent = data.message || 'Failed to load order details';
                            orderDetailsError.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        orderDetailsLoader.style.display = 'none';
                        document.getElementById('errorMessage').textContent = 'An error occurred while loading order details';
                        orderDetailsError.style.display = 'block';
                    });
            });
        });
        
        // Add click event to all delete buttons
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                orderIdToDelete = this.getAttribute('data-order-id');
                deleteModal.show();
            });
        });
        
        // Handle confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (orderIdToDelete) {
                // Create form data
                const formData = new FormData();
                formData.append('order_id', orderIdToDelete);
                
                // Send delete request
                fetch('delete_order.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Hide modal
                    deleteModal.hide();
                    
                    // Show alert based on response
                    if (data.success) {
                        alert(data.message);
                        // Reload page to reflect changes
                        window.location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the order.');
                    deleteModal.hide();
                });
            }
        });

        // Variables for update order functionality
        const updateButtons = document.querySelectorAll('.update-order');
        const updateOrderModal = new bootstrap.Modal(document.getElementById('updateOrderModal'));
        const updateOrderForm = document.getElementById('updateOrderForm');
        const updateOrderIdInput = document.getElementById('updateOrderId');
        const orderStatusSelect = document.getElementById('orderStatus');
        const updateOrderBtn = document.getElementById('updateOrderBtn');
        
        // Add click event to all update buttons
        updateButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const orderId = this.getAttribute('data-order-id');
                const currentStatus = this.getAttribute('data-order-status');
                
                // Set the order ID in the form
                updateOrderIdInput.value = orderId;
                
                // Set the current status in the dropdown
                orderStatusSelect.value = currentStatus;
                
                // Show the modal
                updateOrderModal.show();
            });
        });
        
        // Handle update button click
        updateOrderBtn.addEventListener('click', function() {
            const orderId = updateOrderIdInput.value;
            const newStatus = orderStatusSelect.value;
            
            // Send AJAX request to update the order status
            fetch('update_order_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'order_id=' + orderId + '&status=' + newStatus
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Order status updated successfully!');
                    
                    // Reload the page to show the updated status
                    window.location.reload();
                } else {
                    // Show error message
                    alert('Error updating order status: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the order status.');
            });
        });
    });
</script>

<?php require_once 'includes/footer.php'; ?>