<?php
session_start();
require_once '../config/database.php';

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$category_filter = '';
$sort_by = 'name';
$sort_order = 'ASC';
$page = 1;
$items_per_page = 12; // Show 12 products per page (4x3 grid)

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['category'])) {
    $category_filter = $_GET['category'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Get categories for filter dropdown and featured categories
$categories_query = "SELECT c.*, p.image_url FROM categories c 
                    LEFT JOIN products p ON c.id = p.category_id 
                    WHERE p.image_url IS NOT NULL 
                    GROUP BY c.id 
                    ORDER BY c.name ASC";
$categories_result = $conn->query($categories_query);
$featured_categories_result = $conn->query($categories_query); // Clone for featured categories

// Build query for products
$query = "SELECT p.*, c.name as category_name, b.name as brand_name, 
          (SELECT COUNT(*) FROM order_items oi WHERE oi.product_id = p.id) as total_sales 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          LEFT JOIN brands b ON p.brand_id = b.id 
          WHERE p.is_online = 1 AND p.branch_name = 'Online Store'";

// Add search condition
if (!empty($search)) {
    $query .= " AND (p.name LIKE ? OR p.sku LIKE ? OR p.description LIKE ?)";
}

// Add category filter
if (!empty($category_filter)) {
    $query .= " AND p.category_id = ?";
}

// Add sorting
$query .= " ORDER BY p." . $sort_by . " " . $sort_order;

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameters
$bind_types = '';
$bind_params = [];

if (!empty($search)) {
    $search_param = "%$search%";
    $bind_types .= 'sss';
    $bind_params[] = $search_param;
    $bind_params[] = $search_param;
    $bind_params[] = $search_param;
}

if (!empty($category_filter)) {
    $bind_types .= 'i';
    $bind_params[] = $category_filter;
}

if (!empty($bind_params)) {
    $stmt->bind_param($bind_types, ...$bind_params);
}

// Execute query
$stmt->execute();
$result = $stmt->get_result();

// Get total count for pagination
$total_items = $result->num_rows;
$total_pages = ceil($total_items / $items_per_page);

// Apply pagination to results
$offset = ($page - 1) * $items_per_page;
$products = [];
$count = 0;

while ($row = $result->fetch_assoc()) {
    if ($count >= $offset && count($products) < $items_per_page) {
        $products[] = $row;
    }
    $count++;
}

// Get most ordered products
$most_ordered_query = "SELECT p.*, c.name as category_name, b.name as brand_name, 
                      COUNT(oi.product_id) as order_count 
                      FROM products p 
                      LEFT JOIN categories c ON p.category_id = c.id 
                      LEFT JOIN brands b ON p.brand_id = b.id 
                      LEFT JOIN order_items oi ON p.id = oi.product_id 
                      WHERE p.is_online = 1 
                      GROUP BY p.id 
                      ORDER BY order_count DESC 
                      LIMIT 5";
$most_ordered_result = $conn->query($most_ordered_query);
$most_ordered_products = [];

while ($row = $most_ordered_result->fetch_assoc()) {
    $most_ordered_products[] = $row;
}

// Get featured products (newest products)
$featured_query = "SELECT p.*, c.name as category_name, b.name as brand_name FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  LEFT JOIN brands b ON p.brand_id = b.id 
                  WHERE p.is_online = 1 
                  ORDER BY p.created_at DESC LIMIT 4";
$featured_result = $conn->query($featured_query);
$featured_products = [];

while ($row = $featured_result->fetch_assoc()) {
    $featured_products[] = $row;
}

// Get brands with logos - Move this BEFORE closing the connection
$brands_query = "SELECT name, logo_path FROM brands WHERE logo_path IS NOT NULL ORDER BY name ASC";
$brands_result = $conn->query($brands_query);

// NOW close the connection after all database operations are complete
closeDB($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Store - Quality Products at Great Prices</title>
    <meta name="description" content="Shop our wide selection of quality products at competitive prices. Fast shipping and excellent customer service.">
    <link rel="icon" href="../franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="../assets/css/shop-style.css?v=<?php echo time(); ?>">
    <style>
        /* Add these styles for the card headers */
        .card-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-bottom: none;
            border-radius: 8px 8px 0 0 !important;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 500;
        }

        .card-header i {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Reduced font sizes for shop pages */
        body {
            font-size: 14px;
            animation: fadeIn 0.5s ease-in;
        }

        /* Page refresh animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                transform: translateY(20px);
                opacity: 0;
            }
            to { 
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes scaleIn {
            from { 
                transform: scale(0.95);
                opacity: 0;
            }
            to { 
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Apply animations to sections */
        .hero-section {
            animation: slideUp 0.6s ease-out;
        }

        .featured-categories {
            animation: slideUp 0.7s ease-out;
        }

        .featured-products {
            animation: slideUp 0.8s ease-out;
        }

        /* Stagger animation for product cards */
        .row > .col {
            opacity: 0;
            animation: slideUp 0.5s ease-out forwards;
        }

        .row > .col:nth-child(1) { animation-delay: 0.1s; }
        .row > .col:nth-child(2) { animation-delay: 0.2s; }
        .row > .col:nth-child(3) { animation-delay: 0.3s; }
        .row > .col:nth-child(4) { animation-delay: 0.4s; }
        .row > .col:nth-child(5) { animation-delay: 0.5s; }
        .row > .col:nth-child(6) { animation-delay: 0.6s; }
        .row > .col:nth-child(7) { animation-delay: 0.7s; }
        .row > .col:nth-child(8) { animation-delay: 0.8s; }
        
        h1, .h1 { font-size: 1.8rem; }
        h2, .h2 { font-size: 1.5rem; }
        h3, .h3 { font-size: 1.3rem; }
        h4, .h4 { font-size: 1.1rem; }
        h5, .h5 { font-size: 1rem; }
        h6, .h6 { font-size: 0.9rem; }
        
        .card-title {
            font-size: 1.1rem;
        }
        
        .card-text {
            font-size: 0.9rem;
        }
        
        .product-title {
            font-size: 1rem;
        }
        
        .product-price {
            font-size: 1.1rem;
        }
        
        .product-description {
            font-size: 0.9rem;
        }
        
        .btn {
            font-size: 0.9rem;
        }
        
        .nav-link {
            font-size: 0.9rem;
        }
        
        .table {
            font-size: 0.9rem;
        }
        
        .form-control {
            font-size: 0.9rem;
        }
        
        .form-label {
            font-size: 0.9rem;
        }
        
        .badge {
            font-size: 0.8rem;
            z-index: 2;
        }

        .ribbon {
            position: absolute;
            padding: 5px 12px;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            z-index: 2;
        }

        .ribbon-new {
            background: linear-gradient(45deg, #28a745, #20c997);
            top: 10px;
            right: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .ribbon-sale {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            top: 10px;
            left: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .ribbon-popular {
            background: linear-gradient(45deg, #6f42c1, #007bff);
            top: 50px;
            left: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .small {
            font-size: 0.8rem;
        }
        
        .text-muted {
            font-size: 0.85rem;
        }

        /* Product card styles */
        .product-card {
            transition: all 0.3s ease;
            padding: 1.5rem;
            margin: 0.5rem;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .product-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .product-img-container {
            margin-bottom: 1.5rem;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .product-title {
            margin: 1rem 0;
            padding: 0 0.5rem;
        }

        .product-price {
            margin: 0.75rem 0;
            padding: 0 0.5rem;
        }

        .product-meta {
            margin: 0.75rem 0;
            padding: 0 0.5rem;
        }

        /* Category card styles */
        .category-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            padding: 1.5rem;
            margin: 0.5rem;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .category-img-container {
            margin-bottom: 1.5rem;
            border-radius: 8px;
            overflow: hidden;
        }

        .category-title {
            margin: 1rem 0;
            padding: 0.75rem;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 5px;
        }

        .card-body {
            padding: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark navbar-shop sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../logo.png" alt="Store Logo" height="40">
                <span class="ms-2">Online Store</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php"><i class="fas fa-home me-1"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-tags me-1"></i> Categories
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                            <?php 
                            // Reset the categories result pointer
                            $categories_result->data_seek(0);
                            while ($category = $categories_result->fetch_assoc()): 
                            ?>
                                <li><a class="dropdown-item" href="index.php?category=<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></a></li>
                            <?php endwhile; ?>
                        </ul>
                    </li>
                </ul>
                <form class="d-flex me-2" action="index.php" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search products...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                <a href="cart.php" class="btn btn-outline-light position-relative">
                    <i class="fas fa-shopping-cart"></i>
                    <?php if (isset($_SESSION['shop_cart']) && count($_SESSION['shop_cart']) > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <?php echo count($_SESSION['shop_cart']); ?>
                        </span>
                    <?php endif; ?>
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-5">
        <div class="row">
            <div class="col-lg-9">
        <?php if (empty($search) && empty($category_filter)): ?>
        <!-- Hero Section -->
        <div class="hero-section mb-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6 hero-content">
                        <h1 class="hero-title">Welcome to Musar Online Store</h1>
                        <p class="hero-subtitle">Discover quality products at competitive prices with fast shipping and excellent customer service.</p>
                        <a href="#featured-products" class="btn btn-light hero-btn">Shop Now <i class="fas fa-arrow-right ms-2"></i></a>
                    </div>

                    <div class="col-lg-6 d-none d-lg-block text-center">
                        <img src="logo.png" alt="Featured Products" class="img-fluid" style="max-height: 300px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Categories -->
        <section class="featured-categories mb-5">
            <h2 class="section-title mb-4">Shop by Category</h2>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <?php 
                // Reset the featured categories result pointer
                $featured_categories_result->data_seek(0);
                $category_count = 0;
                while ($category = $featured_categories_result->fetch_assoc()): 
                    if ($category_count >= 4) break; // Limit to 4 categories
                    $category_count++;
                ?>
                    <div class="col animate-fade-in" style="animation-delay: <?php echo $category_count * 0.1; ?>s;">
                        <a href="index.php?category=<?php echo $category['id']; ?>" class="text-decoration-none">
                            <div class="category-card">
                                <div class="category-img-container">
                                    <img src="<?php echo !empty($category['image_url']) ? '../uploads/products/' . $category['image_url'] : 'no-image.png'; ?>" alt="<?php echo htmlspecialchars($category['name']); ?>" class="category-img">
                                    <div class="category-overlay">
                                        <h3 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h3>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>
            </div>
        </section>

        <!-- Featured Products -->
        <section id="featured-products" class="mb-5">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="section-title">Featured Products</h2>
                <a href="#all-products" class="btn btn-outline-primary">View All <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <?php $delay = 0.1; foreach ($featured_products as $product): $delay += 0.1; ?>
                    <div class="col animate-fade-in" style="animation-delay: <?php echo $delay; ?>s;">
                        <div class="product-card">
                            <div class="product-img-container">
                                <?php if ($product['quantity'] <= 0): ?>
                                    <span class="badge bg-danger product-badge position-absolute top-0 end-0 m-2 px-3 py-2">Out of Stock</span>
                                <?php elseif ($product['created_at'] >= date('Y-m-d', strtotime('-7 days'))): ?>
                                    <div class="ribbon ribbon-new">New</div>
                                <?php endif; ?>
                                <?php if (isset($product['is_on_sale']) && $product['is_on_sale']): ?>
                                    <div class="ribbon ribbon-sale">Sale</div>
                                <?php endif; ?>
                                <?php if (isset($product['order_count']) && $product['order_count'] > 0): ?>
                                    <div class="ribbon ribbon-popular"><?php echo $product['order_count']; ?> Purchases</div>
                                <?php endif; ?>
                                
                                <?php if (!empty($product['image_url'])): ?>
                                    <img src="../uploads/products/<?php echo $product['image_url']; ?>" class="product-img" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <img src="no-image.png" class="product-img" alt="No Image Available">
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <h5 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="product-price">₱<?php echo number_format($product['unit_price'], 2); ?></p>
                                <p class="product-meta">
                                    <?php if (!empty($product['category_name'])): ?>
                                        <span><i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($product['category_name']); ?></span>
                                    <?php endif; ?>
                                    <?php if (isset($product['total_sales']) && $product['total_sales'] > 0): ?>
                                        <span class="ms-2"><i class="fas fa-shopping-cart me-1"></i><?php echo number_format($product['total_sales']); ?> sold</span>
                                    <?php endif; ?>
                                </p>
                                <div class="d-grid gap-2">
                                    <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-outline-primary product-btn">
                                        View Details <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                    <?php if ($product['quantity'] > 0): ?>
                                        <form action="add_to_cart.php" method="POST">
                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                            <input type="hidden" name="quantity" value="1">
                                            <input type="hidden" name="redirect" value="index.php">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-cart-plus me-2"></i> Add to Cart
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-secondary w-100" disabled>
                                            <i class="fas fa-times me-2"></i> Out of Stock
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>
        
        <!-- All Products -->
        <section id="all-products">
            <?php if (!empty($search) || !empty($category_filter)): ?>
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="section-title">
                        <?php if (!empty($search)): ?>
                            Search Results for "<?php echo htmlspecialchars($search); ?>"
                        <?php elseif (!empty($category_filter)): ?>
                            <?php 
                            // Get category name
                            $categories_result->data_seek(0);
                            while ($category = $categories_result->fetch_assoc()) {
                                if ($category['id'] == $category_filter) {
                                    echo htmlspecialchars($category['name']);
                                    break;
                                }
                            }
                            ?>
                        <?php else: ?>
                            All Products
                        <?php endif; ?>
                    </h2>
                    <a href="index.php" class="btn btn-outline-secondary"><i class="fas fa-arrow-left me-1"></i> Back to All</a>
                </div>
            <?php else: ?>
                <h2 class="section-title mb-4">All Products</h2>
            <?php endif; ?>
            
            <!-- Filters and Sorting -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <form action="index.php" method="GET" class="d-flex">
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <?php endif; ?>
                        <select name="category" class="form-select me-2" onchange="this.form.submit()">
                            <option value="">All Categories</option>
                            <?php 
                            // Reset the categories result pointer
                            $categories_result->data_seek(0);
                            while ($category = $categories_result->fetch_assoc()): 
                            ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo ($category_filter == $category['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </form>
                </div>
                <div class="col-md-8">
                    <form action="index.php" method="GET" class="d-flex justify-content-end">
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <?php endif; ?>
                        <?php if (!empty($category_filter)): ?>
                            <input type="hidden" name="category" value="<?php echo htmlspecialchars($category_filter); ?>">
                        <?php endif; ?>
                        <select name="sort_by" class="form-select me-2" style="width: auto;">
                            <option value="name" <?php echo ($sort_by == 'name') ? 'selected' : ''; ?>>Name</option>
                            <option value="unit_price" <?php echo ($sort_by == 'unit_price') ? 'selected' : ''; ?>>Price</option>
                            <option value="created_at" <?php echo ($sort_by == 'created_at') ? 'selected' : ''; ?>>Newest</option>
                        </select>
                        <select name="sort_order" class="form-select me-2" style="width: auto;">
                            <option value="ASC" <?php echo ($sort_order == 'ASC') ? 'selected' : ''; ?>>Ascending</option>
                            <option value="DESC" <?php echo ($sort_order == 'DESC') ? 'selected' : ''; ?>>Descending</option>
                        </select>
                        <button type="submit" class="btn btn-primary">Sort</button>
                    </form>
                </div>
            </div>
        
        <!-- Products Grid -->
        <?php if (count($products) > 0): ?>
            <div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 g-4">
                <?php $delay = 0.1; foreach ($products as $product): $delay += 0.05; ?>
                    <div class="col animate-fade-in" style="animation-delay: <?php echo $delay; ?>s;">
                        <div class="product-card">
                            <div class="product-img-container">
                                <?php if ($product['quantity'] <= 0): ?>
                                    <span class="badge bg-danger product-badge position-absolute top-0 end-0 m-2 px-3 py-2">Out of Stock</span>
                                <?php elseif ($product['created_at'] >= date('Y-m-d', strtotime('-7 days'))): ?>
                                    <div class="ribbon ribbon-new">New</div>
                                <?php endif; ?>
                                <?php if (isset($product['is_on_sale']) && $product['is_on_sale']): ?>
                                    <div class="ribbon ribbon-sale">Sale</div>
                                <?php endif; ?>
                                <?php if (isset($product['order_count']) && $product['order_count'] > 0): ?>
                                    <div class="ribbon ribbon-popular"><?php echo $product['order_count']; ?> Purchases</div>
                                <?php endif; ?>
                                
                                <?php if (!empty($product['image_url'])): ?>
                                    <img src="../uploads/products/<?php echo $product['image_url']; ?>" class="product-img" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <img src="no-image.png" class="product-img" alt="No Image Available">
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <h5 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="product-price">₱<?php echo number_format($product['unit_price'], 2); ?></p>
                                <p class="product-meta">
                                    <?php if (!empty($product['category_name'])): ?>
                                        <span><i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($product['category_name']); ?></span>
                                    <?php endif; ?>
                                    <?php if (isset($product['total_sales']) && $product['total_sales'] > 0): ?>
                                        <span class="ms-2"><i class="fas fa-shopping-cart me-1"></i><?php echo number_format($product['total_sales']); ?> sold</span>
                                    <?php endif; ?>
                                </p>
                                <div class="d-grid gap-2">
                                    <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-outline-primary product-btn">
                                        View Details <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                    <?php if ($product['quantity'] > 0): ?>
                                        <form action="add_to_cart.php" method="POST">
                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                            <input type="hidden" name="quantity" value="1">
                                            <input type="hidden" name="redirect" value="index.php<?php echo (!empty($search) || !empty($category_filter) || !empty($sort_by)) ? '?' . http_build_query($_GET) : ''; ?>">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-cart-plus me-2"></i> Add to Cart
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-secondary w-100" disabled>
                                            <i class="fas fa-times me-2"></i> Out of Stock
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page - 1); ?><?php echo (!empty($search)) ? '&search=' . urlencode($search) : ''; ?><?php echo (!empty($category_filter)) ? '&category=' . urlencode($category_filter) : ''; ?><?php echo (!empty($sort_by) && !empty($sort_order)) ? '&sort_by=' . urlencode($sort_by) . '&sort_order=' . urlencode($sort_order) : ''; ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo (!empty($search)) ? '&search=' . urlencode($search) : ''; ?><?php echo (!empty($category_filter)) ? '&category=' . urlencode($category_filter) : ''; ?><?php echo (!empty($sort_by) && !empty($sort_order)) ? '&sort_by=' . urlencode($sort_by) . '&sort_order=' . urlencode($sort_order) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page + 1); ?><?php echo (!empty($search)) ? '&search=' . urlencode($search) : ''; ?><?php echo (!empty($category_filter)) ? '&category=' . urlencode($category_filter) : ''; ?><?php echo (!empty($sort_by) && !empty($sort_order)) ? '&sort_by=' . urlencode($sort_by) . '&sort_order=' . urlencode($sort_order) : ''; ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h3>No Products Found</h3>
                <p>We couldn't find any products matching your criteria.</p>
                <a href="index.php" class="btn btn-primary mt-3">View All Products</a>
            </div>
        <?php endif; ?>
            </div>
            <div class="col-lg-3">
                <!-- Most Popular Products -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-fire me-2"></i>Most Popular</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <?php foreach ($most_ordered_products as $product): ?>
                                <a href="product.php?id=<?php echo $product['id']; ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <?php if (!empty($product['image_url'])): ?>
                                                <img src="../uploads/products/<?php echo $product['image_url']; ?>" class="rounded" alt="<?php echo htmlspecialchars($product['name']); ?>" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <img src="no-image.png" class="rounded" alt="No Image Available" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-0"><?php echo htmlspecialchars($product['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($product['category_name']); ?></small>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Brand Logo Gallery -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-building me-2"></i>Our Brands</h5>
                    </div>
                    <div class="card-body">
                        <div class="brand-gallery">
                            <?php
                            // Use the brands result from earlier query
                            $brands_result->data_seek(0);
                            
                            while ($brand = $brands_result->fetch_assoc()):
                            ?>
                                <div class="brand-logo-wrapper">
                                    <img src="../<?php echo htmlspecialchars($brand['logo_path']); ?>" 
                                         alt="<?php echo htmlspecialchars($brand['name']); ?>" 
                                         title="<?php echo htmlspecialchars($brand['name']); ?>"
                                         class="brand-logo" width="100" height="auto">
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php require_once 'footer.php'; ?>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

    <!-- Bootstrap Bundle with Popper -->
   

