<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Process form submission for adding/editing category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_category'])) {
    $category_id = isset($_POST['category_id']) ? $_POST['category_id'] : '';
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    $image_url = '';
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Category name is required";
    }
    
    // Check if category name already exists (for new categories or if name changed)
    $name_check_query = "SELECT id FROM categories WHERE name = ? AND id != ?";
    $stmt = $conn->prepare($name_check_query);
    $stmt->bind_param("si", $name, $category_id);
    $stmt->execute();
    $name_result = $stmt->get_result();
    
    if ($name_result->num_rows > 0) {
        $errors[] = "Category name already exists. Please use a different name.";
    }
    
    $stmt->close();
    
    // Handle image upload
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['image']['type'], $allowed_types)) {
            $errors[] = "Invalid file type. Only JPG, PNG, and GIF files are allowed.";
        } elseif ($_FILES['image']['size'] > $max_size) {
            $errors[] = "File size exceeds the maximum limit of 2MB.";
        } else {
            // Create uploads/categories directory if it doesn't exist
            $upload_dir = 'uploads/categories/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            // Generate unique filename
            $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $new_filename = 'category_' . uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $image_url = $new_filename;
            } else {
                $errors[] = "Failed to upload image. Please try again.";
            }
        }
    }
    
    // If no errors, save category
    if (empty($errors)) {
        if (!empty($category_id)) {
            // Update existing category
            if (!empty($image_url)) {
                // If new image uploaded, update with new image
                $query = "UPDATE categories SET name = ?, description = ?, image_url = ? WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("sssi", $name, $description, $image_url, $category_id);
            } else {
                // If no new image, keep existing image
                $query = "UPDATE categories SET name = ?, description = ? WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ssi", $name, $description, $category_id);
            }
            
            if ($stmt->execute()) {
                $success_message = "Category updated successfully";
            } else {
                $errors[] = "Error updating category: " . $conn->error;
            }
        } else {
            // Insert new category
            if (!empty($image_url)) {
                // If image uploaded
                $query = "INSERT INTO categories (name, description, image_url) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("sss", $name, $description, $image_url);
            } else {
                // If no image
                $query = "INSERT INTO categories (name, description) VALUES (?, ?)";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ss", $name, $description);
            }
            
            if ($stmt->execute()) {
                $success_message = "Category added successfully";
            } else {
                $errors[] = "Error adding category: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_category'])) {
    $category_id = $_POST['category_id'];
    
    // Check if category is used in products
    $check_query = "SELECT COUNT(*) as count FROM products WHERE category_id = ?";
    $stmt = $conn->prepare($check_query);
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    if ($count > 0) {
        $errors[] = "Cannot delete category because it is used by $count product(s). Please reassign these products to another category first.";
    } else {
        // Delete category
        $delete_query = "DELETE FROM categories WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $category_id);
        
        if ($stmt->execute()) {
            $success_message = "Category deleted successfully";
        } else {
            $errors[] = "Error deleting category: " . $conn->error;
        }
        
        $stmt->close();
    }
}

// Pagination variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM categories";
$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get categories with pagination
$query = "SELECT c.*, COUNT(p.id) as product_count 
          FROM categories c 
          LEFT JOIN products p ON c.id = p.category_id 
          GROUP BY c.id 
          ORDER BY c.name ASC
          LIMIT $items_per_page OFFSET $offset";
$result = $conn->query($query);

// Close connection
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Categories</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="fas fa-plus me-2"></i> Add New Category
        </button>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Products</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($category = $result->fetch_assoc()): ?>
                                <tr>
                                    <td class="text-center">
                                        <?php if (!empty($category['image_url'])): ?>
                                            <img src="uploads/categories/<?php echo $category['image_url']; ?>" alt="<?php echo htmlspecialchars($category['name']); ?>" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <img src="assets/img/no-image.png" alt="No image" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($category['name']); ?></td>
                                    <td>
                                        <?php 
                                        if (!empty($category['description'])) {
                                            echo (strlen($category['description']) > 100) 
                                                ? htmlspecialchars(substr($category['description'], 0, 100)) . '...' 
                                                : htmlspecialchars($category['description']);
                                        } else {
                                            echo '<span class="text-muted">No description</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <a href="products.php?category=<?php echo $category['id']; ?>">
                                            <?php echo $category['product_count']; ?> product(s)
                                        </a>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($category['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#editCategoryModal" 
                                                    data-id="<?php echo $category['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($category['name']); ?>"
                                                    data-description="<?php echo htmlspecialchars($category['description']); ?>"
                                                    data-image="<?php echo htmlspecialchars($category['image_url']); ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteCategoryModal"
                                                    data-id="<?php echo $category['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($category['name']); ?>"
                                                    data-count="<?php echo $category['product_count']; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <h5>No categories found</h5>
                                        <p class="text-muted">Start by adding a new category</p>
                                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                            <i class="fas fa-plus me-2"></i> Add New Category
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="categories.php" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="image" class="form-label">Category Image</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/jpeg, image/png, image/gif">
                        <small class="form-text text-muted">Max file size: 2MB. Allowed formats: JPG, PNG, GIF</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_category" class="btn btn-primary">Save Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="categories.php" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_image" class="form-label">Category Image</label>
                        <input type="file" class="form-control" id="edit_image" name="image" accept="image/jpeg, image/png, image/gif">
                        <small class="form-text text-muted">Max file size: 2MB. Allowed formats: JPG, PNG, GIF</small>
                        <div id="current_image_container" class="mt-2 d-none">
                            <p>Current image:</p>
                            <img id="current_image" src="" alt="Current category image" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_category" class="btn btn-primary">Update Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCategoryModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category <strong id="delete_category_name"></strong>?</p>
                <div id="delete_warning" class="alert alert-warning d-none">
                    This category contains <strong id="delete_product_count"></strong> product(s). Deleting it will remove the category from these products.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="categories.php">
                    <input type="hidden" id="delete_category_id" name="category_id">
                    <button type="submit" name="delete_category" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Script to populate edit modal with category data
    document.addEventListener('DOMContentLoaded', function() {
        const editCategoryModal = document.getElementById('editCategoryModal');
        if (editCategoryModal) {
            editCategoryModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const categoryId = button.getAttribute('data-id');
                const categoryName = button.getAttribute('data-name');
                const categoryDescription = button.getAttribute('data-description');
                const categoryImage = button.getAttribute('data-image');
                
                const modal = this;
                modal.querySelector('#edit_category_id').value = categoryId;
                modal.querySelector('#edit_name').value = categoryName;
                modal.querySelector('#edit_description').value = categoryDescription;
                
                // Show current image if available
                const currentImageContainer = modal.querySelector('#current_image_container');
                const currentImage = modal.querySelector('#current_image');
                
                if (categoryImage) {
                    currentImage.src = 'uploads/categories/' + categoryImage;
                    currentImageContainer.classList.remove('d-none');
                } else {
                    currentImageContainer.classList.add('d-none');
                }
            });
        }
        
        // Script to populate delete modal with category data
        const deleteCategoryModal = document.getElementById('deleteCategoryModal');
        if (deleteCategoryModal) {
            deleteCategoryModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const categoryId = button.getAttribute('data-id');
                const categoryName = button.getAttribute('data-name');
                const productCount = button.getAttribute('data-count');
                
                const modal = this;
                modal.querySelector('#delete_category_id').value = categoryId;
                modal.querySelector('#delete_category_name').textContent = categoryName;
                
                const warningElement = modal.querySelector('#delete_warning');
                const countElement = modal.querySelector('#delete_product_count');
                
                if (productCount > 0) {
                    countElement.textContent = productCount;
                    warningElement.classList.remove('d-none');
                } else {
                    warningElement.classList.add('d-none');
                }
            });
        }
    });
</script>

<?php require_once 'includes/footer.php'; ?>