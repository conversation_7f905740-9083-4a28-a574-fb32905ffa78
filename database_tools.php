<?php
// Start session and perform authentication before any output
session_start();

// Check if user is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: index.php");
    exit();
}

require_once 'config/database.php';

// Handle form submission before any output
if ($_POST) {
    $conn = connectDB();
    
    if (isset($_POST['reset_qty'])) {
        $sql = "UPDATE products SET quantity = 0";
        $conn->query($sql);
    }
    
    if (isset($_POST['delete_products'])) {
        $sql = "DELETE FROM products";
        $conn->query($sql);
    }
    
    if (isset($_POST['delete_sales'])) {
        $sql = "DELETE FROM sales";
        $conn->query($sql);
        $sql = "DELETE FROM sale_items";
        $conn->query($sql);
    }
    
    if (isset($_POST['delete_categories'])) {
        $sql = "DELETE FROM categories";
        $conn->query($sql);
    }
    
    if (isset($_POST['delete_brands'])) {
        $sql = "DELETE FROM brands";
        $conn->query($sql);
    }
    
    if (isset($_POST['delete_expenses'])) {
        $sql = "DELETE FROM expenses";
        $conn->query($sql);
    }
    
    closeDB($conn);
    
    $_SESSION['success'] = "Database operation completed successfully.";
    header("Location: database_tools.php");
    exit();
}

// Include header after all potential redirects
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>Database Tools</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php 
                            echo $_SESSION['success'];
                            unset($_SESSION['success']);
                            ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" onsubmit="return confirm('Are you sure you want to perform this operation? This action cannot be undone!');">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Database</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Products</td>
                                        <td>
                                            <button type="submit" name="reset_qty" class="btn btn-warning btn-sm">Reset Qty</button>
                                            <button type="submit" name="delete_products" class="btn btn-danger btn-sm">Delete all Products</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Sales</td>
                                        <td>
                                            <button type="submit" name="delete_sales" class="btn btn-danger btn-sm">Delete all Sales</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Categories</td>
                                        <td>
                                            <button type="submit" name="delete_categories" class="btn btn-danger btn-sm">Delete all Categories</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Brands</td>
                                        <td>
                                            <button type="submit" name="delete_brands" class="btn btn-danger btn-sm">Delete all Brands</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Expenses</td>
                                        <td>
                                            <button type="submit" name="delete_expenses" class="btn btn-danger btn-sm">Delete all Expenses</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>