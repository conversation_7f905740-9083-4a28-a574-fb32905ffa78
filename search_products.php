<?php
require_once 'config/database.php';

// Start session if needed (e.g., for branch filtering)
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch'; // Get current branch

header('Content-Type: application/json'); // Set response type to JSON

$conn = connectDB();
$term = isset($_GET['term']) ? $conn->real_escape_string($_GET['term']) : '';
$supplier_id = isset($_GET['supplier_id']) ? (int)$_GET['supplier_id'] : 0; // Optional: Filter by supplier

$products = [];

if (strlen($term) >= 2) { // Only search if term is long enough
    // Base query - Select necessary product fields
    // Filter by branch_name to only show products available for the current user's branch
    $query = "SELECT id, name, sku, unit_price, quantity
              FROM products
              WHERE branch_name = ? AND (name LIKE ? OR sku LIKE ?)";

    $params = [$branch_name];
    $types = "s";

    $like_term = '%' . $term . '%';
    $params[] = $like_term;
    $params[] = $like_term;
    $types .= "ss";

    // Optional: Add supplier filter if a supplier is selected in the main form
    // This assumes your 'products' table has a 'supplier_id' column.
    // If not, you might need a different way to associate products with suppliers.
    /*
    if ($supplier_id > 0) {
        $query .= " AND supplier_id = ?";
        $params[] = $supplier_id;
        $types .= "i";
    }
    */

    $query .= " LIMIT 10"; // Limit results for performance

    $stmt = $conn->prepare($query);
    if ($stmt) {
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            // Ensure unit_price is treated as a number
            $row['unit_price'] = !is_null($row['unit_price']) ? (float)$row['unit_price'] : 0.00;
            $products[] = $row;
        }
        $stmt->close();
    } else {
        // Log error or handle appropriately
        error_log("Error preparing product search query: " . $conn->error);
    }
}

closeDB($conn);

echo json_encode($products); // Output the results as JSON
exit();
?>