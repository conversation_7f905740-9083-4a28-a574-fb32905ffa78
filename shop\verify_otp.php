<?php
session_start();
require_once '../config/database.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify OTP - Online Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .otp-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .otp-input {
            width: 50px;
            height: 50px;
            text-align: center;
            font-size: 24px;
            margin: 0 5px;
            border: 2px solid #ddd;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .otp-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .timer {
            font-size: 1.2rem;
            color: #dc3545;
            margin: 10px 0;
        }

        .resend-btn {
            cursor: pointer;
            color: #007bff;
            text-decoration: underline;
        }

        .resend-btn:disabled {
            color: #6c757d;
            text-decoration: none;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="otp-container">
            <h2 class="text-center mb-4">Verify OTP</h2>
            <div id="message" class="alert" style="display: none;"></div>
            
            <form id="otpForm">
                <div class="mb-3">
                    <label for="phone" class="form-label">Phone Number</label>
                    <input type="tel" class="form-control" id="phone" name="phone" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Enter OTP</label>
                    <div class="d-flex justify-content-center">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]">
                    </div>
                </div>

                <div class="text-center mb-3">
                    <span class="timer">Time remaining: <span id="countdown">05:00</span></span>
                </div>

                <div class="text-center mb-3">
                    <span class="resend-btn" id="resendBtn">Resend OTP</span>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Verify OTP</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            let timer;
            let timeLeft = 300; // 5 minutes in seconds

            // Handle OTP input
            $('.otp-input').on('input', function() {
                if (this.value.length === 1) {
                    $(this).next('.otp-input').focus();
                }
            });

            $('.otp-input').on('keydown', function(e) {
                if (e.key === 'Backspace' && !this.value) {
                    $(this).prev('.otp-input').focus();
                }
            });

            // Start timer
            function startTimer() {
                clearInterval(timer);
                timeLeft = 300;
                updateTimer();
                
                timer = setInterval(function() {
                    timeLeft--;
                    updateTimer();
                    
                    if (timeLeft <= 0) {
                        clearInterval(timer);
                        $('#resendBtn').prop('disabled', false);
                    }
                }, 1000);
            }

            function updateTimer() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                $('#countdown').text(
                    String(minutes).padStart(2, '0') + ':' + 
                    String(seconds).padStart(2, '0')
                );
            }

            // Send OTP
            function sendOTP(phone) {
                $.ajax({
                    url: 'otp.php',
                    method: 'POST',
                    data: {
                        action: 'send',
                        phone_number: phone
                    },
                    success: function(response) {
                        if (response.success) {
                            showMessage('OTP sent successfully!', 'success');
                            startTimer();
                            $('#resendBtn').prop('disabled', true);
                        } else {
                            showMessage(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showMessage('Failed to send OTP. Please try again.', 'danger');
                    }
                });
            }

            // Verify OTP
            $('#otpForm').on('submit', function(e) {
                e.preventDefault();
                
                const phone = $('#phone').val();
                const otp = $('.otp-input').map(function() {
                    return $(this).val();
                }).get().join('');

                $.ajax({
                    url: 'otp.php',
                    method: 'POST',
                    data: {
                        action: 'verify',
                        phone_number: phone,
                        otp: otp
                    },
                    success: function(response) {
                        if (response.success) {
                            showMessage('OTP verified successfully!', 'success');
                            // Redirect or perform further actions
                            setTimeout(() => {
                                window.location.href = 'index.php';
                            }, 2000);
                        } else {
                            showMessage(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showMessage('Failed to verify OTP. Please try again.', 'danger');
                    }
                });
            });

            // Resend OTP
            $('#resendBtn').on('click', function() {
                const phone = $('#phone').val();
                if (phone) {
                    sendOTP(phone);
                } else {
                    showMessage('Please enter your phone number first.', 'warning');
                }
            });

            function showMessage(message, type) {
                const alert = $('#message');
                alert.removeClass().addClass(`alert alert-${type}`).text(message).show();
                setTimeout(() => alert.fadeOut(), 5000);
            }

            // Start timer on page load
            startTimer();
        });
    </script>
</body>
</html> 