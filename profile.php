<?php
require_once 'config/database.php';
require_once 'includes/header.php';

// Initialize variables
$success_message = '';
$error_message = '';
$user_id = $_SESSION['user_id'];

// Get database connection
$conn = connectDB();

// Fetch user data
$stmt = $conn->prepare("SELECT username, full_name, email, profile_image FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 1) {
    $user = $result->fetch_assoc();
} else {
    $error_message = "User not found";
}
$stmt->close();

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Update profile information
    if (isset($_POST['update_profile'])) {
        $full_name = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        
        // Validate input
        if (empty($full_name)) {
            $error_message = "Full name is required";
        } elseif (empty($email)) {
            $error_message = "Email is required";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = "Invalid email format";
        } else {
            // Check if email already exists for another user
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->bind_param("si", $email, $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $error_message = "Email already in use by another account";
            } else {
                // Process profile image upload
                $profile_image = $user['profile_image']; // Default to current image
                
                if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    $file_type = $_FILES['profile_image']['type'];
                    
                    if (!in_array($file_type, $allowed_types)) {
                        $error_message = "Only JPG, PNG, and GIF images are allowed";
                    } else {
                        $file_name = 'user_' . $user_id . '_' . uniqid() . '_' . basename($_FILES['profile_image']['name']);
                        $upload_dir = 'uploads/users/';
                        $upload_path = $upload_dir . $file_name;
                        
                        // Create directory if it doesn't exist
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0777, true);
                        }
                        
                        if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                            // Delete old image if it's not the default
                            if ($profile_image !== 'default.png' && file_exists($upload_dir . $profile_image)) {
                                unlink($upload_dir . $profile_image);
                            }
                            $profile_image = $file_name;
                        } else {
                            $error_message = "Failed to upload image";
                        }
                    }
                }
                
                // If no errors, update user profile
                if (empty($error_message)) {
                    $stmt = $conn->prepare("UPDATE users SET full_name = ?, email = ?, profile_image = ? WHERE id = ?");
                    $stmt->bind_param("sssi", $full_name, $email, $profile_image, $user_id);
                    
                    if ($stmt->execute()) {
                        // Update session variables
                        $_SESSION['full_name'] = $full_name;
                        $_SESSION['profile_image'] = $profile_image;
                        
                        $success_message = "Profile updated successfully";
                        // Refresh user data
                        $user['full_name'] = $full_name;
                        $user['email'] = $email;
                        $user['profile_image'] = $profile_image;
                    } else {
                        $error_message = "Failed to update profile: " . $conn->error;
                    }
                }
            }
        }
    }
}

closeDB($conn);
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">My Profile</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success"><?php echo $success_message; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger"><?php echo $error_message; ?></div>
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="mb-3">
                                    <?php 
                                    $profile_image = isset($user['profile_image']) ? $user['profile_image'] : 'default.png';
                                    $image_path = 'uploads/users/' . $profile_image;
                                    ?>
                                    <img src="<?php echo $image_path; ?>" alt="Profile Image" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                                </div>
                                <div class="mb-3">
                                    <label for="profile_image" class="form-label">Change Profile Picture</label>
                                    <input type="file" class="form-control" id="profile_image" name="profile_image">
                                    <small class="text-muted">Max file size: 2MB. JPG, PNG, GIF only.</small>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                    <small class="text-muted">Username cannot be changed</small>
                                </div>
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>