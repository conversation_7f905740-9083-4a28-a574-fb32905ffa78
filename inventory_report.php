<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$category_filter = '';
$stock_status = '';
$sort_by = 'name';
$sort_order = 'ASC';

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['category'])) {
    $category_filter = $_GET['category'];
}

if (isset($_GET['stock_status'])) {
    $stock_status = $_GET['stock_status'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

// Get categories for filter dropdown
$categories_query = "SELECT * FROM categories ORDER BY name ASC";
$categories_result = $conn->query($categories_query);

// Build query for products
$query = "SELECT p.*, c.name as category_name, b.name as brand_name FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          LEFT JOIN brands b ON p.brand_id = b.id 
          WHERE p.branch_name = ?";

// Add search condition
if (!empty($search)) {
    $query .= " AND (p.name LIKE ? OR p.sku LIKE ? OR p.description LIKE ?)";
}

// Add category filter
if (!empty($category_filter)) {
    $query .= " AND p.category_id = ?";
}

// Add stock status filter
if ($stock_status === 'low') {
    $query .= " AND p.quantity <= p.reorder_level";
} elseif ($stock_status === 'out') {
    $query .= " AND p.quantity = 0";
} elseif ($stock_status === 'in') {
    $query .= " AND p.quantity > 0";
}

// Add sorting
$query .= " ORDER BY " . $sort_by . " " . $sort_order;

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameters
if (!empty($search) && !empty($category_filter)) {
    $search_param = "%$search%";
    $stmt->bind_param("ssssi", $branch_name, $search_param, $search_param, $search_param, $category_filter);
} elseif (!empty($search)) {
    $search_param = "%$search%";
    $stmt->bind_param("ssss", $branch_name, $search_param, $search_param, $search_param);
} elseif (!empty($category_filter)) {
    $stmt->bind_param("si", $branch_name, $category_filter);
} else {
    $stmt->bind_param("s", $branch_name);
}

// Execute query
$stmt->execute();
$result = $stmt->get_result();

// Calculate inventory value
$total_inventory_value = 0;
$total_items = 0;
$low_stock_count = 0;
$out_of_stock_count = 0;

$products = [];
while ($row = $result->fetch_assoc()) {
    $row['inventory_value'] = $row['quantity'] * $row['unit_price'];
    $total_inventory_value += $row['inventory_value'];
    $total_items += $row['quantity'];
    
    if ($row['quantity'] <= $row['reorder_level'] && $row['quantity'] > 0) {
        $low_stock_count++;
    }
    
    if ($row['quantity'] == 0) {
        $out_of_stock_count++;
    }
    
    $products[] = $row;
}
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4 no-print-header">
        <h1 class="h2">Inventory Report</h1>
        <button onclick="printReport()" class="btn btn-primary">
            <i class="fas fa-print me-2"></i> Print Report
        </button>
    </div>
    
    <script>
    function printReport() {
        window.print();
    }
    </script>
    
    <!-- Filters -->
    <div class="card mb-4 no-print">
        <div class="card-body">
            <form method="GET" action="inventory_report.php" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by name, SKU, or description">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_filter == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="stock_status" class="form-label">Stock Status</label>
                    <select class="form-select" id="stock_status" name="stock_status">
                        <option value="" <?php echo ($stock_status == '') ? 'selected' : ''; ?>>All Stock</option>
                        <option value="low" <?php echo ($stock_status == 'low') ? 'selected' : ''; ?>>Low Stock</option>
                        <option value="out" <?php echo ($stock_status == 'out') ? 'selected' : ''; ?>>Out of Stock</option>
                        <option value="in" <?php echo ($stock_status == 'in') ? 'selected' : ''; ?>>In Stock</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_by" class="form-label">Sort By</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="name" <?php echo ($sort_by == 'name') ? 'selected' : ''; ?>>Name</option>
                        <option value="quantity" <?php echo ($sort_by == 'quantity') ? 'selected' : ''; ?>>Quantity</option>
                        <option value="unit_price" <?php echo ($sort_by == 'unit_price') ? 'selected' : ''; ?>>Price</option>
                        <option value="category_name" <?php echo ($sort_by == 'category_name') ? 'selected' : ''; ?>>Category</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_order" class="form-label">Order</label>
                    <select class="form-select" id="sort_order" name="sort_order">
                        <option value="ASC" <?php echo ($sort_order == 'ASC') ? 'selected' : ''; ?>>Ascending</option>
                        <option value="DESC" <?php echo ($sort_order == 'DESC') ? 'selected' : ''; ?>>Descending</option>
                    </select>
                </div>
                <div class="col-md-12 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i> Generate Report
                    </button>
                    <a href="inventory_report.php" class="btn btn-secondary">
                        <i class="fas fa-redo me-2"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Report Header -->
    <div class="card mb-4">
        <div class="card-body text-center">
            <h2 class="mb-0">Musar Music Corporation</h2>
            <h4><?php echo htmlspecialchars($branch_name); ?></h4>
            <h3 class="mt-3 mb-2">Inventory Report</h3>
            <p class="mb-0">Generated on: <?php echo date('F d, Y h:i A'); ?></p>
        </div>
    </div>
    
    <!-- Inventory Summary -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card h-100 bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Products</h5>
                    <h2 class="display-4"><?php echo count($products); ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Items</h5>
                    <h2 class="display-4"><?php echo $total_items; ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">Low Stock Items</h5>
                    <h2 class="display-4"><?php echo $low_stock_count; ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">Out of Stock</h5>
                    <h2 class="display-4"><?php echo $out_of_stock_count; ?></h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Inventory Value -->
    <div class="card mb-4">
        <div class="card-body">
            <h4 class="mb-3">Inventory Valuation</h4>
            <h2 class="text-primary">₱<?php echo number_format($total_inventory_value, 2); ?></h2>
            <p class="text-muted">Total value of all products in inventory</p>
        </div>
    </div>
    
    <!-- Inventory Table -->
    <div class="card mb-4">
        <div class="card-body">
            <h4 class="mb-3">Inventory Details</h4>
            
            <?php if (count($products) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>SKU</th>
                                <th>Product Name</th>
                                <th>Category</th>
                                <th>Brand</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Inventory Value</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($product['sku']); ?></td>
                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?: 'Uncategorized'); ?></td>
                                    <td><?php echo htmlspecialchars($product['brand_name'] ?: 'No Brand'); ?></td>
                                    <td><?php echo $product['quantity']; ?></td>
                                    <td>₱<?php echo number_format($product['unit_price'], 2); ?></td>
                                    <td>₱<?php echo number_format($product['inventory_value'], 2); ?></td>
                                    <td>
                                        <?php if ($product['quantity'] == 0): ?>
                                            <span class="badge bg-danger">Out of Stock</span>
                                        <?php elseif ($product['quantity'] <= $product['reorder_level']): ?>
                                            <span class="badge bg-warning text-dark">Low Stock</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">In Stock</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No products found matching your criteria.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    @media print {
        .no-print, .no-print * {
            display: none !important;
        }
        .container-fluid {
            width: 100%;
            padding: 0;
        }
        .card {
            border: none;
        }
        .card-body {
            padding: 0.5rem;
        }
        body {
            font-size: 12pt;
        }
        .table {
            font-size: 10pt;
        }
        .display-4 {
            font-size: 1.5rem;
        }
    }
</style>

<?php
// Close database connection
$conn->close();

// Include footer
require_once 'includes/footer.php';
?>