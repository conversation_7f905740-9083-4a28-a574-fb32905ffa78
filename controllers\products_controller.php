<?php
/**
 * Products Controller - Handles product-related operations
 */

function getProductsForBranch($conn, $branch_name, $page = 1, $items_per_page = 10, $search = '') {
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM products p
                  WHERE p.branch_name = '" . $conn->real_escape_string($branch_name) . "'";

    // Add search condition if provided
    if (!empty($search)) {
        $count_query .= " AND (p.name LIKE '%" . $conn->real_escape_string($search) . "%'
                       OR p.sku LIKE '%" . $conn->real_escape_string($search) . "%')";
    }

    $count_result = $conn->query($count_query);
    $total_items = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_items / $items_per_page);

    // Get products with pagination
    $offset = ($page - 1) * $items_per_page;
    $products_query = "SELECT p.*, c.name as category_name, b.name as brand_name
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN brands b ON p.brand_id = b.id
                    WHERE p.branch_name = '" . $conn->real_escape_string($branch_name) . "'";

    // Add search condition if provided
    if (!empty($search)) {
        $products_query .= " AND (p.name LIKE '%" . $conn->real_escape_string($search) . "%'
                          OR p.sku LIKE '%" . $conn->real_escape_string($search) . "%')";
    }

    $products_query .= " ORDER BY p.name ASC LIMIT $items_per_page OFFSET $offset";
    $products_result = $conn->query($products_query);
    
    $products = [];
    if ($products_result) {
        while ($row = $products_result->fetch_assoc()) {
            $products[] = $row;
        }
    }

    return [
        'products' => $products,
        'total_pages' => $total_pages
    ];
}

function getProductDetails($conn, $product_id) {
    $query = "SELECT p.*, c.name as category_name, b.name as brand_name, br.name as branch_name
              FROM products p
              LEFT JOIN categories c ON p.category_id = c.id
              LEFT JOIN brands b ON p.brand_id = b.id
              LEFT JOIN branches br ON p.branch_id = br.id
              WHERE p.id = ?";
              
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $product_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    return null;
}

/*
function getPopularProducts($conn, $branch_name, $limit = 5) {
    // This function can remain in the file but won't be used in sales.php
}

function getLowStockProducts($conn, $branch_name, $limit = 5) {
    // This function can remain in the file but won't be used in sales.php
}
*/

