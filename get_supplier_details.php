<?php
// Only start session if one doesn't already exist
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Get database connection
$conn = connectDB();

// Check if supplier ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Supplier ID is required']);
    exit();
}

$supplier_id = (int)$_GET['id'];

// Get supplier details
$query = "SELECT * FROM suppliers WHERE id = ? LIMIT 1";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Supplier not found']);
    exit();
}

$supplier = $result->fetch_assoc();
$stmt->close();

// Close connection
closeDB($conn);

// Return supplier details as JSON
header('Content-Type: application/json');
echo json_encode($supplier);