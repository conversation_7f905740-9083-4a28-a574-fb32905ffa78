<?php
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Get branch name from session
session_start();
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Fetch recent activities (last 24 hours)
$query = "SELECT 
    CASE
        WHEN o.id IS NOT NULL THEN 'sale'
        WHEN t.id IS NOT NULL THEN 'transfer'
        WHEN e.id IS NOT NULL THEN 'expense'
    END as type,
    CASE
        WHEN o.id IS NOT NULL THEN CONCAT('Sale #', o.order_number, ' - ', o.customer_name)
        WHEN t.id IS NOT NULL THEN CONCAT('Transfer #', t.transfer_number)
        WHEN e.id IS NOT NULL THEN CONCAT('Expense: ', e.description)
    END as details,
    CASE
        WHEN o.id IS NOT NULL THEN o.created_at
        WHEN t.id IS NOT NULL THEN t.created_at
        WHEN e.id IS NOT NULL THEN e.created_at
    END as timestamp
FROM (
    SELECT id, order_number, customer_name, created_at
    FROM orders
    WHERE branch_name = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    UNION ALL
    SELECT id, transfer_number, NULL, created_at
    FROM transfers
    WHERE (from_branch = ? OR to_branch = ?) AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    UNION ALL
    SELECT id, NULL, description, created_at
    FROM expenses
    WHERE branch_name = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
) activities
LEFT JOIN orders o ON activities.id = o.id AND activities.order_number IS NOT NULL
LEFT JOIN transfers t ON activities.id = t.id AND activities.transfer_number IS NOT NULL
LEFT JOIN expenses e ON activities.id = e.id AND activities.description IS NOT NULL
ORDER BY timestamp DESC
LIMIT 10";

$stmt = $conn->prepare($query);
$stmt->bind_param('ssss', $branch_name, $branch_name, $branch_name, $branch_name);
$stmt->execute();
$result = $stmt->get_result();

$activities = [];
while ($row = $result->fetch_assoc()) {
    $activities[] = [
        'type' => $row['type'],
        'details' => $row['details'],
        'timestamp' => $row['timestamp']
    ];
}

// Set JSON response headers
header('Content-Type: application/json');
echo json_encode($activities);