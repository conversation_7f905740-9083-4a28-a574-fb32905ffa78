<?php
// Start the session at the beginning of the file
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in before any output
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Check if user is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: dashboard.php?error=unauthorized");
    exit();
}

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6>Database Import Tool</h6>
                    <p class="text-sm">Import data from u476900858_stocks to inventory_management database</p>
                </div>
                <div class="card-body">
                    <form action="import_database.php" method="post" id="importForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Source Database (u476900858_stocks)</h5>
                                <div class="form-group">
                                    <label for="src_host">Host</label>
                                    <input type="text" class="form-control" id="src_host" name="src_host" value="srv1518.hstgr.io" required>
                                </div>
                                <div class="form-group">
                                    <label for="src_user">Username</label>
                                    <input type="text" class="form-control" id="src_user" name="src_user" value="u476900858_musar_stocks" required>
                                </div>
                                <div class="form-group">
                                    <label for="src_pass">Password</label>
                                    <input type="password" class="form-control" id="src_pass" name="src_pass" value="uzHWGC0FLHHT8xb2">
                                </div>

                                <div class="form-group">
                                    <label for="src_db">Database Name</label>
                                    <input type="text" class="form-control" id="src_db" name="src_db" value="u476900858_stocks" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>Destination Database (inventory_management)</h5>
                                <div class="form-group">
                                    <label for="dest_host">Host</label>
                                    <input type="text" class="form-control" id="dest_host" name="dest_host" value="localhost" required>
                                </div>
                                <div class="form-group">
                                    <label for="dest_user">Username</label>
                                    <input type="text" class="form-control" id="dest_user" name="dest_user" value="root" required>
                                </div>
                                <div class="form-group">
                                    <label for="dest_pass">Password</label>
                                    <input type="password" class="form-control" id="dest_pass" name="dest_pass" value="uzHWGC0FLHHT8xb2">
                                </div>
                                <div class="form-group">
                                    <label for="dest_db">Database Name</label>
                                    <input type="text" class="form-control" id="dest_db" name="dest_db" value="u476900858_inventor" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Import Options</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="import_brands" name="import_brands" value="1" checked>
                                    <label class="form-check-label" for="import_brands">
                                        Import Brands (merchandize_brands → brands)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="import_categories" name="import_categories" value="1" checked>
                                    <label class="form-check-label" for="import_categories">
                                        Import Categories (categories → categories)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="import_products" name="import_products" value="1" checked>
                                    <label class="form-check-label" for="import_products">
                                        Import Products (merchandize → products)
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Field Mappings</h6>
                                    <p>The following field mappings will be used during import:</p>
                                    <ul>
                                        <li><strong>Brands:</strong> brandid → id, brandnam → name</li>
                                        <li><strong>Categories:</strong> categoryid → id, categoryname → name, description → description</li>
                                        <li><strong>Products:</strong> merchandizeid → id, description → name, itemcode → sku, qty → quantity, unitprice → unit_price, supplierid → supplier_id, categoryid → category_id, brandid → brand_id, location → branch_name</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" id="importBtn">
                                    <i class="fas fa-database"></i> Start Import
                                </button>
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('importForm').addEventListener('submit', function(e) {
        document.getElementById('importBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        document.getElementById('importBtn').disabled = true;
    });
</script>

<?php require_once 'includes/footer.php'; ?>