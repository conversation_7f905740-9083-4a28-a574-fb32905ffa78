<?php
session_start();
require_once '../config/database.php';

// Initialize shop cart if it doesn't exist
if (!isset($_SESSION['shop_cart'])) {
    $_SESSION['shop_cart'] = [];
}

// Process remove from cart
if (isset($_GET['remove']) && is_numeric($_GET['remove'])) {
    $index = (int)$_GET['remove'];
    if (isset($_SESSION['shop_cart'][$index])) {
        unset($_SESSION['shop_cart'][$index]);
        $_SESSION['shop_cart'] = array_values($_SESSION['shop_cart']); // Reindex array
        
        // Set success message
        $_SESSION['cart_message'] = [
            'type' => 'success',
            'text' => 'Product removed from cart.'
        ];
    }
    
    // Redirect to avoid resubmission
    header("Location: cart.php");
    exit();
}

// Process clear cart
if (isset($_GET['clear_cart'])) {
    $_SESSION['shop_cart'] = [];
    
    // Set success message
    $_SESSION['cart_message'] = [
        'type' => 'success',
        'text' => 'Cart has been cleared.'
    ];
    
    // Redirect to avoid resubmission
    header("Location: cart.php");
    exit();
}

// Process update quantities
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_cart'])) {
    // Get database connection
    $conn = connectDB();
    
    foreach ($_POST['quantity'] as $index => $qty) {
        if (isset($_SESSION['shop_cart'][$index])) {
            $product_id = $_SESSION['shop_cart'][$index]['id'];
            $quantity = (int)$qty;
            
            // Validate quantity
            if ($quantity <= 0) {
                $quantity = 1;
            }
            
            // Check product availability
            $query = "SELECT quantity FROM products WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $product_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $product = $result->fetch_assoc();
                
                // Make sure we don't exceed available stock
                if ($quantity > $product['quantity']) {
                    $quantity = $product['quantity'];
                }
                
                // Update cart item
                $_SESSION['shop_cart'][$index]['quantity'] = $quantity;
                $_SESSION['shop_cart'][$index]['total'] = $quantity * $_SESSION['shop_cart'][$index]['price'];
            }
        }
    }
    
    // Close connection
    closeDB($conn);
    
    // Set success message
    $_SESSION['cart_message'] = [
        'type' => 'success',
        'text' => 'Cart updated successfully.'
    ];
    
    // Redirect to avoid resubmission
    header("Location: cart.php");
    exit();
}

// Calculate cart total
$cart_total = 0;
foreach ($_SESSION['shop_cart'] as $item) {
    $cart_total += $item['total'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Online Store</title>
    <meta name="description" content="Review your shopping cart items and proceed to checkout. Fast and secure shopping experience.">
    <link rel="icon" href="../franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/shop-style.css">
    <!-- Using styles from shop-style.css -->
    <style>
        /* Reduced font sizes for shop pages */
        body {
            font-size: 14px;
        }
        
        h1, .h1 { font-size: 1.8rem; }
        h2, .h2 { font-size: 1.5rem; }
        h3, .h3 { font-size: 1.3rem; }
        h4, .h4 { font-size: 1.1rem; }
        h5, .h5 { font-size: 1rem; }
        h6, .h6 { font-size: 0.9rem; }
        
        .card-title {
            font-size: 1.1rem;
        }
        
        .card-text {
            font-size: 0.9rem;
        }
        
        .product-title {
            font-size: 1rem;
        }
        
        .product-price {
            font-size: 1.1rem;
        }
        
        .product-description {
            font-size: 0.9rem;
        }
        
        .btn {
            font-size: 0.9rem;
        }
        
        .nav-link {
            font-size: 0.9rem;
        }
        
        .table {
            font-size: 0.9rem;
        }
        
        .form-control {
            font-size: 0.9rem;
        }
        
        .form-label {
            font-size: 0.9rem;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .small {
            font-size: 0.8rem;
        }
        
        .text-muted {
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark navbar-shop sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../logo.png" alt="Store Logo" height="40">
                <span class="ms-2">Online Store</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="cart.php"><i class="fas fa-shopping-cart me-1"></i> Cart</a>
                    </li>
                </ul>
                <form class="d-flex" action="index.php" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="search" placeholder="Search products...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        
        <!-- Page Title -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="section-title">Shopping Cart</h1>
            <a href="index.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i> Continue Shopping
            </a>
        </div>
        
        <?php if (isset($_SESSION['cart_message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['cart_message']['type']; ?> alert-dismissible fade show animate-fade-in" role="alert">
                <?php echo $_SESSION['cart_message']['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['cart_message']); ?>
        <?php endif; ?>
        
        <?php if (count($_SESSION['shop_cart']) > 0): ?>
            <form action="cart.php" method="POST">
                <div class="row">
                    <!-- Cart Items -->
                    <div class="col-lg-8 mb-4">
                        <?php foreach ($_SESSION['shop_cart'] as $index => $item): ?>
                            <div class="cart-item animate-fade-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                                <div class="row align-items-center">
                                    <div class="col-md-2 col-4 mb-2 mb-md-0">
                                        <div class="text-center">
                                            <?php if (!empty($item['image_url'])): ?>
                                                <img src="../uploads/products/<?php echo $item['image_url']; ?>" class="cart-item-img" alt="<?php echo htmlspecialchars($item['name']); ?>">
                                            <?php else: ?>
                                                <img src="no-image.png" class="cart-item-img" alt="No Image Available">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-8 mb-2 mb-md-0">
                                        <h5 class="cart-item-title"><?php echo htmlspecialchars($item['name']); ?></h5>
                                        <a href="product.php?id=<?php echo $item['id']; ?>" class="text-muted small">View Product</a>
                                    </div>
                                    <div class="col-md-2 col-4 text-md-center mb-2 mb-md-0">
                                        <p class="cart-item-price">₱<?php echo number_format($item['price'], 2); ?></p>
                                    </div>
                                    <div class="col-md-2 col-4 text-md-center mb-2 mb-md-0">
                                        <input type="number" name="quantity[<?php echo $index; ?>]" class="form-control cart-quantity-input" value="<?php echo $item['quantity']; ?>" min="1">
                                    </div>
                                    <div class="col-md-2 col-4 text-md-end">
                                        <div class="d-flex justify-content-end align-items-center">
                                            <p class="cart-item-price mb-0 me-3">₱<?php echo number_format($item['total'], 2); ?></p>
                                            <a href="cart.php?remove=<?php echo $index; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to remove this item?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="cart.php?clear_cart=1" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to clear your cart?')">
                                <i class="fas fa-trash me-2"></i> Clear Cart
                            </a>
                            <button type="submit" name="update_cart" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-2"></i> Update Cart
                            </button>
                        </div>
                    </div>
                    
                    <!-- Cart Summary -->
                    <div class="col-lg-4">
                        <div class="cart-summary animate-fade-in" style="animation-delay: 0.3s;">
                            <h5 class="cart-summary-title">Order Summary</h5>
                            
                            <div class="cart-summary-item">
                                <span>Subtotal</span>
                                <span>₱<?php echo number_format($cart_total, 2); ?></span>
                            </div>
                            
                            <div class="cart-summary-item">
                                <span>Shipping</span>
                                <span>Calculated at checkout</span>
                            </div>
                            
                            <div class="cart-summary-total">
                                <span>Total</span>
                                <span>₱<?php echo number_format($cart_total, 2); ?></span>
                            </div>
                            
                            <div class="d-grid gap-2 mt-4">
                                <a href="checkout.php" class="btn btn-primary hero-btn">
                                    <i class="fas fa-credit-card me-2"></i> Proceed to Checkout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        <?php else: ?>
            <div class="text-center py-5 animate-fade-in">
                <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                <h3>Your cart is empty</h3>
                <p>Looks like you haven't added any products to your cart yet.</p>
                <a href="index.php" class="btn btn-primary hero-btn mt-3">
                    <i class="fas fa-shopping-bag me-2"></i> Start Shopping
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <?php require_once 'footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>