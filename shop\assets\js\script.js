/**
 * Main JavaScript file for the Inventory Management System
 */

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
    
    // Initialize any Bootstrap popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
    })
});

// Function to confirm deletion
function confirmDelete(itemType, itemId) {
    if (confirm(`Are you sure you want to delete this ${itemType}?`)) {
        document.getElementById(`delete-${itemType}-${itemId}`).submit();
    }
}

// Function to preview image before upload
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            document.getElementById(previewId).src = e.target.result;
            document.getElementById(previewId).style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// Function to generate dashboard charts
function generateDashboardCharts() {
    // Check if the charts container exists
    if (!document.getElementById('revenue-chart') || !document.getElementById('orders-chart')) {
        return;
    }
    
    // Revenue Chart
    var revenueCtx = document.getElementById('revenue-chart').getContext('2d');
    var revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Revenue',
                data: [12000, 19000, 15000, 25000, 22000, 30000, 28000, 25000, 30000, 29000, 32000, 35000],
                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                borderColor: '#198754',
                borderWidth: 2,
                tension: 0.3,
                pointBackgroundColor: '#198754'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '$' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
    
    // Orders Chart
    var ordersCtx = document.getElementById('orders-chart').getContext('2d');
    var ordersChart = new Chart(ordersCtx, {
        type: 'bar',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Orders',
                data: [65, 78, 52, 91, 83, 105, 95, 87, 102, 97, 110, 115],
                backgroundColor: '#0d6efd'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Product Categories Chart
    var categoriesCtx = document.getElementById('categories-chart').getContext('2d');
    var categoriesChart = new Chart(categoriesCtx, {
        type: 'doughnut',
        data: {
            labels: ['Electronics', 'Office Supplies', 'Furniture', 'Others'],
            datasets: [{
                data: [40, 30, 20, 10],
                backgroundColor: ['#0d6efd', '#198754', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Call the chart generation function when the page loads
document.addEventListener('DOMContentLoaded', function() {
    generateDashboardCharts();
});