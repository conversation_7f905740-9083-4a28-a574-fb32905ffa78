<?php
// Only start session if one doesn't already exist
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

// Check if user is logged in before any output
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Get database connection
$conn = connectDB();

// Check if purchase order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: purchase_order.php");
    exit();
}

$po_id = (int)$_GET['id'];
$success_message = '';
$error_message = '';

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Get purchase order details
$po_query = "SELECT po.*, s.name as supplier_name, s.contact_person, s.email, s.phone, s.address,
             u.username as created_by_name, a.username as approved_by_name
             FROM purchase_orders po 
             LEFT JOIN suppliers s ON po.supplier_id = s.id 
             LEFT JOIN users u ON po.created_by = u.id 
             LEFT JOIN users a ON po.approved_by = a.id 
             WHERE po.id = ? AND po.branch_name = ?";

$stmt = $conn->prepare($po_query);
$stmt->bind_param("is", $po_id, $branch_name);
$stmt->execute();
$po_result = $stmt->get_result();

if ($po_result->num_rows === 0) {
    // Purchase order not found or doesn't belong to this branch
    header("Location: purchase_order.php");
    exit();
}

$po = $po_result->fetch_assoc();
$stmt->close();

// Process form submission for adding items to purchase order
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_item'])) {
    $product_id = $_POST['product_id'];
    $quantity = (int)$_POST['quantity'];
    $unit_price = (float)$_POST['unit_price'];
    $total_price = $quantity * $unit_price;
    
    // Validate form data
    $errors = [];
    
    if (empty($product_id)) {
        $errors[] = "Product is required";
    }
    
    if ($quantity <= 0) {
        $errors[] = "Quantity must be greater than zero";
    }
    
    if ($unit_price <= 0) {
        $errors[] = "Unit price must be greater than zero";
    }
    
    // Check if product already exists in this PO
    $check_query = "SELECT id FROM purchase_order_items WHERE po_id = ? AND product_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $po_id, $product_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        $errors[] = "This product is already in the purchase order. Please update the existing item instead.";
    }
    $check_stmt->close();
    
    // If no errors, save item
    if (empty($errors)) {
        // Insert item
        $query = "INSERT INTO purchase_order_items (po_id, product_id, quantity, unit_price, total_price) 
                  VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("iiddd", $po_id, $product_id, $quantity, $unit_price, $total_price);
        
        if ($stmt->execute()) {
            // Update purchase order total amount
            $update_total_query = "UPDATE purchase_orders SET total_amount = (SELECT SUM(total_price) FROM purchase_order_items WHERE po_id = ?) WHERE id = ?";
            $update_stmt = $conn->prepare($update_total_query);
            $update_stmt->bind_param("ii", $po_id, $po_id);
            $update_stmt->execute();
            $update_stmt->close();
            
            $success_message = "Item added successfully";
        } else {
            $error_message = "Error adding item: " . $conn->error;
        }
        
        $stmt->close();
    } else {
        $error_message = implode("<br>", $errors);
    }
}

// Process form submission for updating purchase order status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];
    $approved_by = ($new_status == 'approved') ? $_SESSION['user_id'] : NULL;
    
    // Update status
    $update_query = "UPDATE purchase_orders SET status = ?, approved_by = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("sii", $new_status, $approved_by, $po_id);
    
    if ($update_stmt->execute()) {
        $success_message = "Purchase order status updated successfully";
        // Refresh PO data
        $stmt = $conn->prepare($po_query);
        $stmt->bind_param("is", $po_id, $branch_name);
        $stmt->execute();
        $po_result = $stmt->get_result();
        $po = $po_result->fetch_assoc();
        $stmt->close();
    } else {
        $error_message = "Error updating status: " . $conn->error;
    }
    
    $update_stmt->close();
}

// Process form submission for removing items from purchase order
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['remove_item'])) {
    $item_id = $_POST['item_id'];
    
    // Delete item
    $delete_query = "DELETE FROM purchase_order_items WHERE id = ? AND po_id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("ii", $item_id, $po_id);
    
    if ($delete_stmt->execute()) {
        // Update purchase order total amount
        $update_total_query = "UPDATE purchase_orders SET total_amount = (SELECT COALESCE(SUM(total_price), 0) FROM purchase_order_items WHERE po_id = ?) WHERE id = ?";
        $update_stmt = $conn->prepare($update_total_query);
        $update_stmt->bind_param("ii", $po_id, $po_id);
        $update_stmt->execute();
        $update_stmt->close();
        
        $success_message = "Item removed successfully";
    } else {
        $error_message = "Error removing item: " . $conn->error;
    }
    
    $delete_stmt->close();
}

// Process form submission for updating item quantities
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_quantities'])) {
    if (!isset($_POST['item_id']) || !isset($_POST['quantity']) || !isset($_POST['unit_price'])) {
        $error_message = "Missing required form data";
        header("Location: purchase_order_detail.php?id=$po_id");
        exit();
    }
    
    $item_ids = is_array($_POST['item_id']) ? $_POST['item_id'] : [];
    $quantities = is_array($_POST['quantity']) ? $_POST['quantity'] : [];
    $unit_prices = is_array($_POST['unit_price']) ? $_POST['unit_price'] : [];
    
    if (count($item_ids) === 0 || count($quantities) === 0 || count($unit_prices) === 0) {
        $error_message = "No items to update";
        header("Location: purchase_order_detail.php?id=$po_id");
        exit();
    }
    
    $success = true;
    
    // Update each item
    for ($i = 0; $i < count($item_ids); $i++) {
        $item_id = $item_ids[$i];
        $quantity = (int)$quantities[$i];
        $unit_price = (float)$unit_prices[$i];
        $total_price = $quantity * $unit_price;
        
        if ($quantity <= 0) {
            $error_message = "Quantity must be greater than zero";
            $success = false;
            break;
        }
        
        $update_query = "UPDATE purchase_order_items SET quantity = ?, unit_price = ?, total_price = ? WHERE id = ? AND po_id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("iddii", $quantity, $unit_price, $total_price, $item_id, $po_id);
        
        if (!$update_stmt->execute()) {
            $error_message = "Error updating items: " . $conn->error;
            $success = false;
            break;
        }
        
        $update_stmt->close();
    }
    
    if ($success) {
        // Update purchase order total amount
        $update_total_query = "UPDATE purchase_orders SET total_amount = (SELECT SUM(total_price) FROM purchase_order_items WHERE po_id = ?) WHERE id = ?";
        $update_stmt = $conn->prepare($update_total_query);
        $update_stmt->bind_param("ii", $po_id, $po_id);
        $update_stmt->execute();
        $update_stmt->close();
        
        $success_message = "Items updated successfully";
    }
}

// Process form submission for receiving items
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['receive_items'])) {
    $item_ids = isset($_POST['item_id']) && is_array($_POST['item_id']) ? $_POST['item_id'] : [];
    $received_quantities = isset($_POST['received_quantity']) && is_array($_POST['received_quantity']) ? $_POST['received_quantity'] : [];
    if (count($item_ids) === 0 || count($received_quantities) === 0) {
        $error_message = "No items to receive";
        header("Location: purchase_order_detail.php?id=$po_id");
        exit();
    }
    
    $success = true;
    $all_received = true;
    $updated_products = 0;
    
    // Update each item's received quantity
    for ($i = 0; $i < count($item_ids); $i++) {
        $item_id = $item_ids[$i];
        $received_quantity = (int)$received_quantities[$i];
        
        // Get current item details
        $item_query = "SELECT product_id, quantity, received_quantity FROM purchase_order_items WHERE id = ?";
        $item_stmt = $conn->prepare($item_query);
        $item_stmt->bind_param("i", $item_id);
        $item_stmt->execute();
        $item_result = $item_stmt->get_result();
        $item = $item_result->fetch_assoc();
        $item_stmt->close();
        
        $total_received = $item['received_quantity'] + $received_quantity;
        
        if ($total_received > $item['quantity']) {
            $error_message = "Received quantity cannot exceed ordered quantity";
            $success = false;
            break;
        }
        
        // Update received quantity
        $update_query = "UPDATE purchase_order_items SET received_quantity = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ii", $total_received, $item_id);
        
        if (!$update_stmt->execute()) {
            $error_message = "Error updating received quantities: " . $conn->error;
            $success = false;
            break;
        }
        
        $update_stmt->close();
        
        // Update product inventory
        if ($received_quantity > 0) {
            $update_inventory_query = "UPDATE products SET quantity = quantity + ? WHERE id = ?";
            $inventory_stmt = $conn->prepare($update_inventory_query);
            $inventory_stmt->bind_param("ii", $received_quantity, $item['product_id']);
            $inventory_stmt->execute();
            $inventory_stmt->close();
            $updated_products++;
        }
        
        // Check if all items are fully received
        if ($total_received < $item['quantity']) {
            $all_received = false;
        }
    }
    
    if ($success) {
        // If all items are fully received, update PO status to 'received'
        if ($all_received) {
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Update PO status
                $update_status_query = "UPDATE purchase_orders SET status = 'received' WHERE id = ?";
                $status_stmt = $conn->prepare($update_status_query);
                $status_stmt->bind_param("i", $po_id);
                $status_stmt->execute();
                $status_stmt->close();
                
                // Commit transaction
                $conn->commit();
                
                // Refresh PO data
                $stmt = $conn->prepare($po_query);
                $stmt->bind_param("is", $po_id, $branch_name);
                $stmt->execute();
                $po_result = $stmt->get_result();
                $po = $po_result->fetch_assoc();
                $stmt->close();
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error_message = "Error updating status: " . $e->getMessage();
                $success = false;
            }
        }
        
        $success_message = "Items received successfully! " . $updated_products . " product(s) updated in inventory.";
    }
}

// Get purchase order items
$items_query = "SELECT poi.*, p.name as product_name, p.sku, p.unit_price, c.name as category_name 
               FROM purchase_order_items poi 
               LEFT JOIN products p ON poi.product_id = p.id 
               LEFT JOIN categories c ON p.category_id = c.id 
               WHERE poi.po_id = ? 
               ORDER BY poi.id ASC";

$items_stmt = $conn->prepare($items_query);
$items_stmt->bind_param("i", $po_id);
$items_stmt->execute();
$items_result = $items_stmt->get_result();
$items_stmt->close();

// Include mail functions
require_once 'includes/mail_functions.php';

// Process email sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_email'])) {
    $to = $po['email'];
    $subject = "Purchase Order #{$po['po_number']} from {$branch_name}";
    
    // Generate email content
    $message = "<html><body>";
    $message .= "<h2>Purchase Order #{$po['po_number']}</h2>";
    $message .= "<p>Dear {$po['supplier_name']},</p>";
    $message .= "<p>Please find the details of our purchase order below:</p>";
    
    // Add PO details
    $message .= "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    $message .= "<tr><th>Product</th><th>SKU</th><th>Quantity</th><th>Unit Price</th><th>Total</th></tr>";
    
    // Reset pointer for items result
    if ($items_result) {
        mysqli_data_seek($items_result, 0);
        while ($item = $items_result->fetch_assoc()) {
            $message .= "<tr>";
            $message .= "<td>{$item['product_name']}</td>";
            $message .= "<td>{$item['sku']}</td>";
            $message .= "<td>{$item['quantity']}</td>";
            $message .= "<td>₱" . number_format($item['unit_price'], 2) . "</td>";
            $message .= "<td>₱" . number_format($item['total_price'], 2) . "</td>";
            $message .= "</tr>";
        }
    }
    
    $message .= "</table>";
    $message .= "<p><strong>Total Amount: ₱" . number_format($po['total_amount'], 2) . "</strong></p>";
    
    if (!empty($po['notes'])) {
        $message .= "<p><strong>Notes:</strong><br>" . nl2br(htmlspecialchars($po['notes'])) . "</p>";
    }
    
    $message .= "<p>Please process this order at your earliest convenience.</p>";
    $message .= "<p>Best regards,<br>{$branch_name}</p>";
    $message .= "</body></html>";
    
    // Send email
    if (send_email($to, $subject, $message)) {
        $success_message = "Email sent successfully to {$po['email']}";
    } else {
        $error_message = "Failed to send email. Please try again.";
    }
}

// Include header
require_once 'includes/header.php';
?>

<!-- Email Preview Modal -->
<div class="modal fade" id="emailPreviewModal" tabindex="-1" aria-labelledby="emailPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailPreviewModalLabel">Email Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>To:</strong> <?php echo htmlspecialchars($po['email']); ?>
                </div>
                <div class="mb-3">
                    <strong>Subject:</strong> Purchase Order #<?php echo htmlspecialchars($po['po_number']); ?> from <?php echo htmlspecialchars($branch_name); ?>
                </div>
                <hr>
                <div class="email-content">
                    <h2>Purchase Order #<?php echo htmlspecialchars($po['po_number']); ?></h2>
                    <p>Dear <?php echo htmlspecialchars($po['supplier_name']); ?>,</p>
                    <p>Please find the details of our purchase order below:</p>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                if ($items_result) {
                                    mysqli_data_seek($items_result, 0);
                                    while ($item = $items_result->fetch_assoc()): 
                                ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['sku']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td>₱<?php echo number_format($item['unit_price'], 2); ?></td>
                                        <td>₱<?php echo number_format($item['total_price'], 2); ?></td>
                                    </tr>
                                <?php 
                                    endwhile;
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <p><strong>Total Amount: ₱<?php echo number_format($po['total_amount'], 2); ?></strong></p>
                    
                    <?php if (!empty($po['notes'])): ?>
                    <p><strong>Notes:</strong><br>
                    <?php echo nl2br(htmlspecialchars($po['notes'])); ?></p>
                    <?php endif; ?>
                    
                    <p>Please process this order at your earliest convenience.</p>
                    <p>Best regards,<br><?php echo htmlspecialchars($branch_name); ?></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <form method="post" style="display: inline;">
                    <button type="submit" name="send_email" class="btn btn-primary">Send Email</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Purchase Order Details</h1>
        <div>
            <a href="purchase_order_receipt.php?id=<?php echo $po_id; ?>" class="btn btn-info me-2" target="_blank">
                <i class="fas fa-receipt me-2"></i> Print Receipt
            </a>
            <a href="purchase_order.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i> Back to Purchase Orders
            </a>
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print me-2"></i> Print
            </button>
            <?php if (!empty($po['email'])): ?>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#emailPreviewModal">
                    <i class="fas fa-envelope me-2"></i> Send to Supplier
                </button>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Purchase Order Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">PO Number:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['po_number']); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">PO Date:</div>
                        <div class="col-md-8"><?php echo date('M d, Y', strtotime($po['po_date'])); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Expected Delivery:</div>
                        <div class="col-md-8"><?php echo !empty($po['expected_delivery_date']) ? date('M d, Y', strtotime($po['expected_delivery_date'])) : 'N/A'; ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Status:</div>
                        <div class="col-md-8">
                            <?php if ($po['status'] == 'draft'): ?>
                                <span class="badge bg-secondary">Draft</span>
                            <?php elseif ($po['status'] == 'pending'): ?>
                                <span class="badge bg-warning text-dark">Pending</span>
                            <?php elseif ($po['status'] == 'approved'): ?>
                                <span class="badge bg-info">Approved</span>
                            <?php elseif ($po['status'] == 'received'): ?>
                                <span class="badge bg-success">Received</span>
                            <?php elseif ($po['status'] == 'cancelled'): ?>
                                <span class="badge bg-danger">Cancelled</span>
                            <?php endif; ?>
                            
                            <?php if ($po['status'] != 'received' && $po['status'] != 'cancelled' && $_SESSION['role'] == 'admin'): ?>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                                    Update Status
                                </button>
                            <?php endif; ?>
                            <?php if ($po['status'] != 'cancelled'): ?>
                                <button type="button" class="btn btn-sm btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#emailPreviewModal">
                                    <i class="fas fa-envelope me-1"></i> Send to Supplier
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Total Amount:</div>
                        <div class="col-md-8">₱<?php echo number_format($po['total_amount'], 2); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Created By:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['created_by_name'] ?? 'N/A'); ?></div>
                    </div>
                    <?php if (!empty($po['approved_by_name'])): ?>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Approved By:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['approved_by_name']); ?></div>
                    </div>
                    <?php endif; ?>
                    <?php if (!empty($po['notes'])): ?>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Notes:</div>
                        <div class="col-md-8"><?php echo nl2br(htmlspecialchars($po['notes'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Supplier Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Supplier Name:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['supplier_name'] ?? 'N/A'); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Contact Person:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['contact_person'] ?? 'N/A'); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Email:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['email'] ?? 'N/A'); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Phone:</div>
                        <div class="col-md-8"><?php echo htmlspecialchars($po['phone'] ?? 'N/A'); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Address:</div>
                        <div class="col-md-8"><?php echo nl2br(htmlspecialchars($po['address'] ?? 'N/A')); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Purchase Order Items -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Purchase Order Items</h5>
            <div>
                <span class="text-white me-3">Branch/Store: <strong><?php echo htmlspecialchars($po['branch_name']); ?></strong></span>
                <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                    <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addItemModal">
                        <i class="fas fa-plus me-2"></i> Add Item
                    </button>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if ($items_result->num_rows > 0): ?>
                <form method="post" action="purchase_order_detail.php?id=<?php echo $po_id; ?>">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Category</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                    <?php if ($po['status'] == 'approved'): ?>
                                        <th>Received</th>
                                    <?php endif; ?>
                                    <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                                        <th>Actions</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $counter = 1;
                                while ($item = $items_result->fetch_assoc()): 
                                ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['sku']); ?></td>
                                        <td><?php echo htmlspecialchars($item['category_name'] ?? 'N/A'); ?></td>
                                        <td>
                                            <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                                                <input type="hidden" name="item_id[]" value="<?php echo $item['id']; ?>">
                                                <input type="number" class="form-control form-control-sm" name="quantity[]" value="<?php echo $item['quantity']; ?>" min="1" style="width: 80px;">
                                            <?php else: ?>
                                                <?php echo $item['quantity']; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                                                <input type="number" class="form-control form-control-sm" name="unit_price[]" value="<?php echo $item['unit_price']; ?>" min="0.01" step="0.01" style="width: 100px;">
                                            <?php else: ?>
                                                ₱<?php echo number_format($item['unit_price'], 2); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>₱<?php echo number_format($item['total_price'], 2); ?></td>
                                        <?php if ($po['status'] == 'approved'): ?>
                                            <td>
                                                <?php echo $item['received_quantity']; ?> / <?php echo $item['quantity']; ?>
                                                <?php if ($item['received_quantity'] < $item['quantity']): ?>
                                                    <input type="hidden" name="item_id[]" value="<?php echo $item['id']; ?>">
                                                    <input type="number" class="form-control form-control-sm d-none" name="received_quantity[]" value="<?php echo $item['quantity'] - $item['received_quantity']; ?>" min="0" max="<?php echo $item['quantity'] - $item['received_quantity']; ?>" style="width: 80px;">
                                                    <button type="button" class="btn btn-sm btn-outline-success ms-2" 
                                                            onclick="showReceiveModal(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['product_name']); ?>', <?php echo $item['quantity']; ?>, <?php echo $item['received_quantity']; ?>)">
                                                        Receive
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        <?php endif; ?>
                                        <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#removeItemModal" 
                                                        data-id="<?php echo $item['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($item['product_name']); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-primary">
                                    <th colspan="6" class="text-end">Total:</th>
                                    <th>₱<?php echo number_format($po['total_amount'], 2); ?></th>
                                    <?php if ($po['status'] == 'approved' || $po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                                        <th></th>
                                    <?php endif; ?>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                        <div class="text-end mt-3">
                            <button type="submit" name="update_quantities" class="btn btn-primary" onclick="return validateQuantities();">
                                <i class="fas fa-save me-2"></i> Update Quantities
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($po['status'] == 'approved' && $items_result->num_rows > 0): ?>
                        <div class="text-end mt-3">
                            <button type="submit" name="receive_items" class="btn btn-success" onclick="return confirm('Are you sure you want to confirm receipt of all remaining items?');">
                                <i class="fas fa-check me-2"></i> Confirm Receipt
                            </button>
                        </div>
                    <?php endif; ?>
                </form>
            <?php else: ?>
                <div class="text-center py-4">
                    <p class="text-muted mb-0">No items added to this purchase order yet.</p>
                    <?php if ($po['status'] == 'draft' || $po['status'] == 'pending'): ?>
                        <button type="button" class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-2"></i> Add Item
                        </button>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addItemModalLabel">Add Item to Purchase Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="product_search" class="form-label">Search Products</label>
                    <input type="text" class="form-control" id="product_search" placeholder="Enter product name, SKU or category...">
                </div>
                
                <div id="search_results" class="mb-3" style="max-height: 200px; overflow-y: auto;">
                    <p class="text-muted">Type in the search box to find products</p>
                </div>
                
                <form method="post" action="purchase_order_detail.php?id=<?php echo $po_id; ?>" id="add_item_form">
                    <input type="hidden" name="product_id" id="selected_product_id">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="quantity" class="form-label">Quantity *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="unit_price" class="form-label">Unit Price *</label>
                            <input type="number" class="form-control" id="unit_price" name="unit_price" min="0.01" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="selected_product_info alert alert-info" style="display: none;">
                        <h6 class="product_name mb-2"></h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>SKU:</strong> <span class="product_sku"></span></p>
                                <p class="mb-1"><strong>Category:</strong> <span class="product_category"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Current Stock:</strong> <span class="product_stock"></span></p>
                                <p class="mb-1"><strong>Current Price:</strong> ₱<span class="product_price"></span></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_item" class="btn btn-primary" id="add_item_btn" disabled>Add Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Remove Item Modal -->
<div class="modal fade" id="removeItemModal" tabindex="-1" aria-labelledby="removeItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="purchase_order_detail.php?id=<?php echo $po_id; ?>">
                <input type="hidden" name="item_id" id="remove_item_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="removeItemModalLabel">Confirm Removal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to remove <span id="remove_item_name" class="fw-bold"></span> from this purchase order?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="remove_item" class="btn btn-danger">Remove Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="purchase_order_detail.php?id=<?php echo $po_id; ?>">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Purchase Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="draft" <?php echo ($po['status'] == 'draft') ? 'selected' : ''; ?>>Draft</option>
                            <option value="pending" <?php echo ($po['status'] == 'pending') ? 'selected' : ''; ?>>Pending</option>
                            <option value="approved" <?php echo ($po['status'] == 'approved') ? 'selected' : ''; ?>>Approved</option>
                            <option value="cancelled" <?php echo ($po['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Receive Item Modal -->
<div class="modal fade" id="receiveItemModal" tabindex="-1" aria-labelledby="receiveItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiveItemModalLabel">Receive Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Product: <span id="receive_product_name" class="fw-bold"></span></p>
                <p>Ordered Quantity: <span id="receive_ordered_qty"></span></p>
                <p>Already Received: <span id="receive_already_qty"></span></p>
                <p>Remaining: <span id="receive_remaining_qty"></span></p>
                
                <div class="mb-3">
                    <label for="receive_quantity" class="form-label">Quantity to Receive</label>
                    <input type="number" class="form-control" id="receive_quantity" min="1" value="1">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirm_receive_btn">Confirm Receipt</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quantity validation function
        window.validateQuantities = function() {
            const quantityInputs = document.querySelectorAll('input[name="quantity[]"]');
            const unitPriceInputs = document.querySelectorAll('input[name="unit_price[]"]');
            
            for (let i = 0; i < quantityInputs.length; i++) {
                const qty = parseInt(quantityInputs[i].value);
                const price = parseFloat(unitPriceInputs[i].value);
                
                if (qty <= 0) {
                    alert('Quantity must be greater than zero');
                    quantityInputs[i].focus();
                    return false;
                }
                
                if (price <= 0) {
                    alert('Unit price must be greater than zero');
                    unitPriceInputs[i].focus();
                    return false;
                }
            }
            
            return true;
        };

        // Product search functionality
        const productSearchInput = document.getElementById('product_search');
        const searchResults = document.getElementById('search_results');
        const selectedProductId = document.getElementById('selected_product_id');
        const addItemBtn = document.getElementById('add_item_btn');
        const selectedProductInfo = document.querySelector('.selected_product_info');
        const unitPriceInput = document.getElementById('unit_price');
        
        productSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            
            if (searchTerm.length < 2) {
                searchResults.innerHTML = '<p class="text-muted">Type at least 2 characters to search</p>';
                return;
            }
            
            // Show loading indicator
            searchResults.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm text-primary" role="status"></div> Searching...</div>';
            
            // Fetch products via AJAX
            fetch(`search_products.php?term=${encodeURIComponent(searchTerm)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) {
                        searchResults.innerHTML = '<p class="text-muted">No products found</p>';
                        return;
                    }
                    
                    let html = '<div class="list-group">';
                    data.forEach(product => {
                        html += `<button type="button" class="list-group-item list-group-item-action product-item" 
                                data-id="${product.id}" 
                                data-name="${product.name}" 
                                data-sku="${product.sku}" 
                                data-category="${product.category_name || 'N/A'}" 
                                data-price="${product.unit_price}" 
                                data-stock="${product.quantity}">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>${product.name}</strong><br>
                                            <small>SKU: ${product.sku}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-primary">₱${parseFloat(product.unit_price).toFixed(2)}</span><br>
                                            <small>Stock: ${product.quantity}</small>
                                        </div>
                                    </div>
                                </button>`;
                    });
                    html += '</div>';
                    searchResults.innerHTML = html;
                    
                    // Add click event to product items
                    document.querySelectorAll('.product-item').forEach(item => {
                        item.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            const name = this.getAttribute('data-name');
                            const sku = this.getAttribute('data-sku');
                            const category = this.getAttribute('data-category');
                            const price = this.getAttribute('data-price');
                            const stock = this.getAttribute('data-stock');
                            
                            // Set selected product
                            selectedProductId.value = id;
                            unitPriceInput.value = price;
                            
                            // Update product info display
                            document.querySelector('.product_name').textContent = name;
                            document.querySelector('.product_sku').textContent = sku;
                            document.querySelector('.product_category').textContent = category;
                            document.querySelector('.product_price').textContent = parseFloat(price).toFixed(2);
                            document.querySelector('.product_stock').textContent = stock;
                            
                            // Show product info and enable add button
                            selectedProductInfo.style.display = 'block';
                            addItemBtn.disabled = false;
                            
                            // Clear search results
                            searchResults.innerHTML = '<p class="text-success">Product selected</p>';
                            productSearchInput.value = name;
                        });
                    });
                })
                .catch(error => {
                    searchResults.innerHTML = '<p class="text-danger">Error searching products</p>';
                    console.error('Error:', error);
                });
        });
        
        // Remove item modal functionality
        const removeItemModal = document.getElementById('removeItemModal');
        if (removeItemModal) {
            removeItemModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const itemId = button.getAttribute('data-id');
                const itemName = button.getAttribute('data-name');
                
                document.getElementById('remove_item_id').value = itemId;
                document.getElementById('remove_item_name').textContent = itemName;
            });
        }
        
        // Receive item functionality
        window.showReceiveModal = function(itemId, productName, orderedQty, receivedQty) {
            const modal = new bootstrap.Modal(document.getElementById('receiveItemModal'));
            const remainingQty = orderedQty - receivedQty;
            
            document.getElementById('receive_product_name').textContent = productName;
            document.getElementById('receive_ordered_qty').textContent = orderedQty;
            document.getElementById('receive_already_qty').textContent = receivedQty;
            document.getElementById('receive_remaining_qty').textContent = remainingQty;
            
            const receiveQtyInput = document.getElementById('receive_quantity');
            receiveQtyInput.max = remainingQty;
            receiveQtyInput.value = remainingQty;
            
            document.getElementById('confirm_receive_btn').onclick = function() {
                const qtyToReceive = parseInt(receiveQtyInput.value);
                
                if (qtyToReceive <= 0 || qtyToReceive > remainingQty) {
                    alert('Please enter a valid quantity');
                    return;
                }
                
                // Find the hidden input for this item and update its value
                const receivedQtyInputs = document.querySelectorAll('input[name="received_quantity[]"]');
                for (let i = 0; i < receivedQtyInputs.length; i++) {
                    const itemIdInput = receivedQtyInputs[i].previousElementSibling;
                    if (itemIdInput && itemIdInput.value == itemId) {
                        receivedQtyInputs[i].value = qtyToReceive;
                        break;
                    }
                }
                
                // Submit the form
                document.querySelector('form').submit();
                modal.hide();
            };
            
            modal.show();
        };
    });
</script>

<?php
// Close connection after all database operations are complete
closeDB($conn);

require_once 'includes/footer.php';
?>