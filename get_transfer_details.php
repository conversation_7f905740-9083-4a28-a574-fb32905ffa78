<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die(json_encode(['error' => 'Unauthorized access']));
}

// Get database connection
$conn = connectDB();

// Get transfer ID from URL
$transfer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$transfer_id) {
    die(json_encode(['error' => 'Transfer ID not provided']));
}

try {
    // Fetch transfer details
    $query = "SELECT t.*, 
              u.username as created_by_name
              FROM transfers t
              LEFT JOIN users u ON t.created_by = u.id
              WHERE t.id = ?";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing transfer query: " . $conn->error);
    }

    $stmt->bind_param("i", $transfer_id);
    if (!$stmt->execute()) {
        throw new Exception("Error executing transfer query: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $transfer = $result->fetch_assoc();

    if (!$transfer) {
        throw new Exception("Transfer not found");
    }

    // Fetch transfer items
    $items_query = "SELECT ti.*, p.name as product_name, p.sku, p.unit_price
                   FROM transfer_items ti
                   LEFT JOIN products p ON ti.product_id = p.id
                   WHERE ti.transfer_id = ?";

    $stmt = $conn->prepare($items_query);
    if (!$stmt) {
        throw new Exception("Error preparing items query: " . $conn->error);
    }

    $stmt->bind_param("i", $transfer_id);
    if (!$stmt->execute()) {
        throw new Exception("Error executing items query: " . $stmt->error);
    }

    $items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

    // Calculate total value
    $total_value = 0;
    foreach ($items as $item) {
        $total_value += $item['quantity'] * $item['unit_price'];
    }

    // Prepare response data
    $response = [
        'transfer' => $transfer,
        'items' => $items,
        'total_value' => $total_value
    ];

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    // Close database connection
    closeDB($conn);
} 