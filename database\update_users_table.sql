-- Add email, otp, and otp_expiry columns to users table
ALTER TABLE users ADD COLUMN email VARCHAR(255) AFTER username;
ALTER TABLE users ADD COLUMN otp VARCHAR(6) DEFAULT NULL;
ALTER TABLE users ADD COLUMN otp_expiry DATETIME DEFAULT NULL;
ALTER TABLE users ADD COLUMN admin_approved ENUM('Yes', 'No') DEFAULT 'No';

-- Update existing users with default email addresses
UPDATE users SET email = CONCAT(username, '@example.com') WHERE email IS NULL;

-- Add unique index on email column
ALTER TABLE users ADD UNIQUE INDEX idx_email (email);