// Dashboard Analytics and Charts
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard components
    initializeSalesChart();
    initializeInventoryStatus();
    initializeRecentActivities();
    setupRefreshInterval();
});

// Sales Chart Initialization
function initializeSalesChart() {
    const ctx = document.getElementById('salesChart');
    if (!ctx) return;

    fetch('daily_sales_report.php?format=json')
        .then(response => response.json())
        .then(data => {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.dates,
                    datasets: [{
                        label: 'Daily Sales',
                        data: data.sales,
                        borderColor: '#4CAF50',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sales Trend'
                        }
                    }
                }
            });
        })
        .catch(error => console.error('Error loading sales data:', error));
}

// Inventory Status Display
function initializeInventoryStatus() {
    const container = document.getElementById('inventoryStatus');
    if (!container) return;

    fetch('inventory_report.php?format=json')
        .then(response => response.json())
        .then(data => {
            updateInventoryMetrics(data);
            highlightLowStock(data.lowStockItems);
        })
        .catch(error => console.error('Error loading inventory data:', error));
}

function updateInventoryMetrics(data) {
    document.getElementById('totalProducts').textContent = data.totalProducts;
    document.getElementById('lowStockItems').textContent = data.lowStockCount;
    document.getElementById('outOfStockItems').textContent = data.outOfStockCount;
    document.getElementById('totalValue').textContent = formatCurrency(data.totalValue);
}

function highlightLowStock(items) {
    const container = document.getElementById('lowStockList');
    if (!container) return;

    container.innerHTML = items.map(item => `
        <div class="alert alert-warning">
            <strong>${item.name}</strong>
            <span>Quantity: ${item.quantity}</span>
            <span>Reorder Level: ${item.reorderLevel}</span>
        </div>
    `).join('');
}

// Recent Activities Tracking
function initializeRecentActivities() {
    const container = document.getElementById('recentActivities');
    if (!container) return;

    fetchRecentActivities()
        .then(activities => {
            container.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <span class="activity-time">${formatTime(activity.timestamp)}</span>
                    <span class="activity-type">${activity.type}</span>
                    <span class="activity-details">${activity.details}</span>
                </div>
            `).join('');
        })
        .catch(error => console.error('Error loading activities:', error));
}

// Utility Functions
function formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(value);
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
}

async function fetchRecentActivities() {
    const response = await fetch('recent_activities.php');
    return await response.json();
}

// Auto-refresh Setup
function setupRefreshInterval() {
    setInterval(() => {
        initializeInventoryStatus();
        initializeRecentActivities();
    }, 300000); // Refresh every 5 minutes
}