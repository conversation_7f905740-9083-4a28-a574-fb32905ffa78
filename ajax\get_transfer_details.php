<?php
require_once '../config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Check if transfer ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid transfer ID']);
    exit;
}

$transfer_id = (int)$_GET['id'];
$conn = connectDB();

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Get transfer details
$transfer_query = "SELECT t.*, u.username 
                  FROM transfers t 
                  LEFT JOIN users u ON t.created_by = u.id 
                  WHERE t.id = ?";
$stmt = $conn->prepare($transfer_query);
$stmt->bind_param("i", $transfer_id);
$stmt->execute();
$transfer_result = $stmt->get_result();

if ($transfer_result->num_rows === 0) {
    http_response_code(404);
    echo json_encode(['error' => 'Transfer not found']);
    closeDB($conn);
    exit;
}

$transfer = $transfer_result->fetch_assoc();

// Check if user has permission to view this transfer
// User should be from either source or destination branch
if ($transfer['source_branch'] !== $branch_name && $transfer['destination_branch'] !== $branch_name) {
    // Check if user is admin (you can add more roles if needed)
    if ($_SESSION['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'You do not have permission to view this transfer']);
        closeDB($conn);
        exit;
    }
}

// Get transfer items
$items_query = "SELECT ti.*, p.name as product_name, p.sku 
               FROM transfer_items ti 
               LEFT JOIN products p ON ti.product_id = p.id 
               WHERE ti.transfer_id = ?";
$stmt = $conn->prepare($items_query);
$stmt->bind_param("i", $transfer_id);
$stmt->execute();
$items_result = $stmt->get_result();

$items = [];
while ($item = $items_result->fetch_assoc()) {
    $items[] = $item;
}

// Close database connection
closeDB($conn);

// Return transfer details and items as JSON
echo json_encode([
    'transfer' => $transfer,
    'items' => $items
]);
exit;