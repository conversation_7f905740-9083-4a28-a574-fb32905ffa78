<?php
/**
 * Mail Functions for Inventory Management System
 * This file contains functions for sending emails, including OTP verification emails
 */

/**
 * Send an email with the provided details
 * 
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email body
 * @param array $headers Additional headers (optional)
 * @return bool True if email was sent successfully, false otherwise
 */
function send_email($to, $subject, $message, $headers = []) {
    // Set default headers if not provided
    if (empty($headers)) {
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: text/html; charset=UTF-8';
        $headers[] = 'From: Musar Music Corporation <<EMAIL>>';
    }
    
    // Convert headers array to string
    $headers_str = implode("\r\n", $headers);
    
    // Send email using PHP's mail function
    return mail($to, $subject, $message, $headers_str);
}

/**
 * Send OTP verification email to user
 * 
 * @param string $to Recipient email address
 * @param string $otp One-time password
 * @param string $full_name User's full name
 * @return bool True if email was sent successfully, false otherwise
 */
function send_otp_email($to, $otp, $full_name) {
    $subject = "Your OTP Verification Code - Musar Music Corporation";
    
    // Create HTML message
    $message = "
    <html>
    <head>
        <title>OTP Verification</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
            .header { background-color: #4e73df; color: white; padding: 10px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; }
            .otp-code { font-size: 24px; font-weight: bold; text-align: center; padding: 15px; background-color: #f8f9fc; border-radius: 5px; letter-spacing: 5px; }
            .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
        </style>
    </head>
    <body>
        <div class=\"container\">
            <div class=\"header\">
                <h2>Musar Admin - OTP Verification</h2>
            </div>
            <div class=\"content\">
                <p>Hello $full_name,</p>
                <p>Your one-time password (OTP) for logging into the Musar Music Corporation Inventory Management System is:</p>
                <div class=\"otp-code\">$otp</div>
                <p>This OTP will expire in 10 minutes. Please do not share this code with anyone.</p>
                <p>If you did not request this OTP, please ignore this email or contact your administrator.</p>
            </div>
            <div class=\"footer\">
                <p>This is an automated message. Please do not reply to this email.</p>
                <p>&copy; " . date('Y') . " Musar Music Corporation</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Send the email
    return send_email($to, $subject, $message);
}

/**
 * Send purchase order email to supplier
 * 
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email body
 * @param array $attachments File attachments (optional)
 * @return bool True if email was sent successfully, false otherwise
 */
function send_purchase_order_email($to, $subject, $message, $attachments = []) {
    // Generate a boundary string
    $boundary = md5(time());
    
    // Set headers for multipart message and attachments
    $headers = [
        'MIME-Version: 1.0',
        "Content-Type: multipart/mixed; boundary=\"$boundary\"",
        'From: Musar Music Corporation <<EMAIL>>',
        'Reply-To: <EMAIL>'
    ];
    
    // Build the email body with attachments
    $body = "--$boundary\r\n";
    $body .= "Content-Type: text/html; charset=UTF-8\r\n";
    $body .= "Content-Transfer-Encoding: base64\r\n\r\n";
    $body .= chunk_split(base64_encode($message)) . "\r\n";
    
    // Add attachments if any
    foreach ($attachments as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $filename = basename($file);
            
            $body .= "--$boundary\r\n";
            $body .= "Content-Type: application/octet-stream; name=\"$filename\"\r\n";
            $body .= "Content-Transfer-Encoding: base64\r\n";
            $body .= "Content-Disposition: attachment; filename=\"$filename\"\r\n\r\n";
            $body .= chunk_split(base64_encode($content)) . "\r\n";
        }
    }
    
    $body .= "--$boundary--";
    
    // Send the email
    return mail($to, $subject, $body, implode("\r\n", $headers));
}
?>