<?php
// Start the session at the beginning of the file
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in before any output
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit();
}

// Check if user is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: dashboard.php?error=unauthorized");
    exit();
}

// Set maximum execution time to 5 minutes to handle large datasets
ini_set('max_execution_time', 300);

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: import_form.php");
    exit();
}

// Get database connection parameters from form
$src_host = $_POST['src_host'] ?? 'srv1518.hstgr.io';
$src_user = $_POST['src_user'] ?? 'root';
$src_pass = $_POST['src_pass'] ?? 'uzHWGC0FLHHT8xb2';
$src_db = $_POST['src_db'] ?? 'u476900858_stocks';

$dest_host = $_POST['dest_host'] ?? 'localhost';
$dest_user = $_POST['dest_user'] ?? 'root';
$dest_pass = $_POST['dest_pass'] ?? 'uzHWGC0FLHHT8xb2';
$dest_db = $_POST['dest_db'] ?? 'u476900858_inventor';

// Define constants for database connections
define('SRC_DB_HOST', $src_host);
define('SRC_DB_USER', $src_user);
define('SRC_DB_PASS', $src_pass);
define('SRC_DB_NAME', $src_db);

define('DEST_DB_HOST', $dest_host);
define('DEST_DB_USER', $dest_user);
define('DEST_DB_PASS', $dest_pass);
define('DEST_DB_NAME', $dest_db);

// Get import options
$import_brands = isset($_POST['import_brands']);
$import_categories = isset($_POST['import_categories']);
$import_products = isset($_POST['import_products']);

// Error and success counters
$errors = [];
$success_count = [
    'brands' => 0,
    'categories' => 0,
    'products' => 0
];

// Connect to source database
function connectSourceDB() {
    $conn = new mysqli(SRC_DB_HOST, SRC_DB_USER, SRC_DB_PASS, SRC_DB_NAME);
    
    if ($conn->connect_error) {
        die("Connection to source database failed: " . $conn->connect_error);
    }
    
    return $conn;
}

// Connect to destination database
function connectDestDB() {
    $conn = new mysqli(DEST_DB_HOST, DEST_DB_USER, DEST_DB_PASS, DEST_DB_NAME);
    
    if ($conn->connect_error) {
        die("Connection to destination database failed: " . $conn->connect_error);
    }
    
    return $conn;
}

// Function to import brands
function importBrands($src_conn, $dest_conn) {
    global $success_count, $errors;
    
    echo "<h3>Importing Brands...</h3>";
    
    // Get all brands from source database
    $query = "SELECT brandid, brandname FROM merchandize_brands";
    $result = $src_conn->query($query);
    
    if (!$result) {
        $errors[] = "Error fetching brands: " . $src_conn->error;
        return;
    }
    
    // Prepare insert statement for destination database
    $insert_stmt = $dest_conn->prepare("INSERT INTO brands (id, name) VALUES (?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name)");
    
    if (!$insert_stmt) {
        $errors[] = "Error preparing brand insert statement: " . $dest_conn->error;
        return;
    }
    
    // Start transaction
    $dest_conn->begin_transaction();
    
    try {
        while ($row = $result->fetch_assoc()) {
            $brand_id = $row['brandid'];
            $brand_name = $row['brandname'];
            
            $insert_stmt->bind_param("is", $brand_id, $brand_name);
            
            if ($insert_stmt->execute()) {
                $success_count['brands']++;
                echo "Imported brand: {$brand_name}<br>";
            } else {
                $errors[] = "Error importing brand {$brand_name}: " . $insert_stmt->error;
            }
        }
        
        // Commit transaction
        $dest_conn->commit();
        echo "<p>Successfully imported {$success_count['brands']} brands</p>";
    } catch (Exception $e) {
        // Rollback transaction on error
        $dest_conn->rollback();
        $errors[] = "Transaction failed: " . $e->getMessage();
    }
    
    $insert_stmt->close();
}

// Function to import categories
function importCategories($src_conn, $dest_conn) {
    global $success_count, $errors;
    
    echo "<h3>Importing Categories...</h3>";
    
    // Get all categories from source database
    $query = "SELECT categoryid, categoryname, description FROM categories";
    $result = $src_conn->query($query);
    
    if (!$result) {
        $errors[] = "Error fetching categories: " . $src_conn->error;
        return;
    }
    
    // Prepare insert statement for destination database
    $insert_stmt = $dest_conn->prepare("INSERT INTO categories (id, name, description) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name), description = VALUES(description)");
    
    if (!$insert_stmt) {
        $errors[] = "Error preparing category insert statement: " . $dest_conn->error;
        return;
    }
    
    // Start transaction
    $dest_conn->begin_transaction();
    
    try {
        while ($row = $result->fetch_assoc()) {
            $category_id = $row['categoryid'];
            $category_name = $row['categoryname'];
            $description = $row['description'];
            
            $insert_stmt->bind_param("iss", $category_id, $category_name, $description);
            
            if ($insert_stmt->execute()) {
                $success_count['categories']++;
                echo "Imported category: {$category_name}<br>";
            } else {
                $errors[] = "Error importing category {$category_name}: " . $insert_stmt->error;
            }
        }
        
        // Commit transaction
        $dest_conn->commit();
        echo "<p>Successfully imported {$success_count['categories']} categories</p>";
    } catch (Exception $e) {
        // Rollback transaction on error
        $dest_conn->rollback();
        $errors[] = "Transaction failed: " . $e->getMessage();
    }
    
    $insert_stmt->close();
}

// Function to import products
function importProducts($src_conn, $dest_conn) {
    global $success_count, $errors;
    
    echo "<h3>Importing Products...</h3>";
    
    // Get all products from source database
    $query = "SELECT m.id, m.description, m.itemcode, m.qty, m.unitprice, m.supplierid, m.categoryid, m.brandid, m.location FROM merchandize m";
    $result = $src_conn->query($query);
    
    if (!$result) {
        $errors[] = "Error fetching products: " . $src_conn->error;
        return;
    }
    
    // Prepare insert statement for destination database
    $insert_stmt = $dest_conn->prepare("INSERT INTO products (id, name, sku, quantity, unit_price, supplier_id, category_id, brand_id, branch_name) 
                                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) 
                                      ON DUPLICATE KEY UPDATE 
                                      name = VALUES(name), 
                                      sku = VALUES(sku), 
                                      quantity = VALUES(quantity), 
                                      unit_price = VALUES(unit_price), 
                                      supplier_id = VALUES(supplier_id), 
                                      category_id = VALUES(category_id), 
                                      brand_id = VALUES(brand_id), 
                                      branch_name = VALUES(branch_name)");
    
    if (!$insert_stmt) {
        $errors[] = "Error preparing product insert statement: " . $dest_conn->error;
        return;
    }
    
    // Start transaction
    $dest_conn->begin_transaction();
    
    try {
        while ($row = $result->fetch_assoc()) {
            $product_id = $row['id'];
            $product_name = $row['description'];
            $sku = $row['itemcode'];
            
            // Handle empty or null SKU values by generating a unique SKU based on product ID
            if (empty($sku) || $sku === '0') {
                $sku = 'PROD-' . $product_id . '-' . substr(md5($product_name), 0, 6);
            }
            
            $quantity = $row['qty'];
            $unit_price = $row['unitprice'];
            $supplier_id = $row['supplierid'];
            $category_id = $row['categoryid'];
            $brand_id = $row['brandid'];
            $branch_name = $row['location'] ?: 'Main Branch';
            
            $insert_stmt->bind_param("issddiiis", $product_id, $product_name, $sku, $quantity, $unit_price, $supplier_id, $category_id, $brand_id, $branch_name);
            
            try {
                if ($insert_stmt->execute()) {
                    $success_count['products']++;
                    echo "Imported product: {$product_name}<br>";
                } else {
                    // If error is duplicate entry for SKU, try with a different SKU
                    if (strpos($insert_stmt->error, 'Duplicate entry') !== false && strpos($insert_stmt->error, 'key \'sku\'') !== false) {
                        // Generate a new unique SKU with timestamp
                        $sku = 'PROD-' . $product_id . '-' . time() . '-' . substr(md5($product_name . rand()), 0, 6);
                        $insert_stmt->bind_param("issddiiis", $product_id, $product_name, $sku, $quantity, $unit_price, $supplier_id, $category_id, $brand_id, $branch_name);
                        
                        if ($insert_stmt->execute()) {
                            $success_count['products']++;
                            echo "Imported product with regenerated SKU: {$product_name}<br>";
                        } else {
                            $errors[] = "Error importing product {$product_name} after SKU regeneration: " . $insert_stmt->error;
                        }
                    } else {
                        $errors[] = "Error importing product {$product_name}: " . $insert_stmt->error;
                    }
                }
            } catch (Exception $e) {
                $errors[] = "Exception importing product {$product_name}: " . $e->getMessage();
                // Continue with next product
                continue;
            }
        }
        
        // Commit transaction
        $dest_conn->commit();
        echo "<p>Successfully imported {$success_count['products']} products</p>";
    } catch (Exception $e) {
        // Rollback transaction on error
        $dest_conn->rollback();
        $errors[] = "Transaction failed: " . $e->getMessage();
    }
    
    $insert_stmt->close();
}

// Main execution

// Include header
require_once 'includes/header.php';

// Start output buffering for better performance
ob_start();

// HTML content
echo "<div class='container-fluid py-4'>
    <div class='row'>
        <div class='col-12'>
            <div class='card mb-4'>
                <div class='card-header pb-0 d-flex justify-content-between align-items-center'>
                    <div>
                        <h5>Database Import Tool</h5>
                        <p class='text-sm mb-0'>Importing data from <strong>{$src_db}</strong> to <strong>{$dest_db}</strong> database</p>
                    </div>
                    <a href='import_form.php' class='btn btn-sm btn-secondary'>
                        <i class='fas fa-arrow-left'></i> Back to Import Form
                    </a>
                </div>
                <div class='card-body'>
                    <div class='progress mb-4' style='height: 25px;'>
                        <div class='progress-bar progress-bar-striped progress-bar-animated' role='progressbar' style='width: 0%' id='importProgress'></div>
                    </div>
                    <div id='importResults'>
                        <p>Starting import process...</p>
";

// Flush initial output
ob_flush();
flush();

// Connect to databases
try {
    $src_conn = connectSourceDB();
    echo "<p class='text-success'><i class='fas fa-check-circle'></i> Successfully connected to source database ({$src_db})</p>";
    ob_flush();
    flush();
    
    $dest_conn = connectDestDB();
    echo "<p class='text-success'><i class='fas fa-check-circle'></i> Successfully connected to destination database ({$dest_db})</p>";
    ob_flush();
    flush();
    
    // Update progress bar - 10%
    echo "<script>document.getElementById('importProgress').style.width = '10%';</script>";
    ob_flush();
    flush();
    
    // Import data based on user selections
    $total_steps = ($import_brands ? 1 : 0) + ($import_categories ? 1 : 0) + ($import_products ? 1 : 0);
    $current_step = 0;
    $progress_per_step = $total_steps > 0 ? 80 / $total_steps : 0; // 80% for import steps (10% for connections, 10% for summary)
    
    if ($import_brands) {
        $current_step++;
        importBrands($src_conn, $dest_conn);
        $progress = 10 + ($current_step * $progress_per_step);
        echo "<script>document.getElementById('importProgress').style.width = '{$progress}%';</script>";
        ob_flush();
        flush();
    }
    
    if ($import_categories) {
        $current_step++;
        importCategories($src_conn, $dest_conn);
        $progress = 10 + ($current_step * $progress_per_step);
        echo "<script>document.getElementById('importProgress').style.width = '{$progress}%';</script>";
        ob_flush();
        flush();
    }
    
    if ($import_products) {
        $current_step++;
        importProducts($src_conn, $dest_conn);
        $progress = 10 + ($current_step * $progress_per_step);
        echo "<script>document.getElementById('importProgress').style.width = '{$progress}%';</script>";
        ob_flush();
        flush();
    }
    
    // Update progress bar - 90%
    echo "<script>document.getElementById('importProgress').style.width = '90%';</script>";
    ob_flush();
    flush();
    
    // Display summary
    echo "<div class='alert alert-success mt-4'>
        <h5><i class='fas fa-check-circle'></i> Import Summary</h5>
        <p>Successfully imported:</p>
        <ul>";
        
    if ($import_brands) {
        echo "<li>Brands: {$success_count['brands']}</li>";
    }
    
    if ($import_categories) {
        echo "<li>Categories: {$success_count['categories']}</li>";
    }
    
    if ($import_products) {
        echo "<li>Products: {$success_count['products']}</li>";
    }
    
    echo "</ul>";
    echo "</div>";
    
    if (!empty($errors)) {
        echo "<div class='alert alert-danger mt-4'>
            <h5><i class='fas fa-exclamation-triangle'></i> Errors Encountered</h5>
            <ul>";
        foreach ($errors as $error) {
            echo "<li>{$error}</li>";
        }
        echo "</ul>
        </div>";
    }
    
    // Update progress bar - 100%
    echo "<script>document.getElementById('importProgress').style.width = '100%';</script>";
    echo "<script>document.getElementById('importProgress').classList.remove('progress-bar-animated');</script>";
    ob_flush();
    flush();
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
        <h5><i class='fas fa-exclamation-circle'></i> Critical Error</h5>
        <p>{$e->getMessage()}</p>
    </div>";
} finally {
    // Close connections if they exist
    if (isset($src_conn)) $src_conn->close();
    if (isset($dest_conn)) $dest_conn->close();
    
    echo "<div class='mt-4'>
        <a href='import_form.php' class='btn btn-primary'>
            <i class='fas fa-sync'></i> Run Another Import
        </a>
        <a href='dashboard.php' class='btn btn-secondary'>
            <i class='fas fa-home'></i> Back to Dashboard
        </a>
    </div>";
    
    echo "</div></div></div></div></div>";
}

// Include footer
require_once 'includes/footer.php';

// Flush output buffer
ob_end_flush();
?>