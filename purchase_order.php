<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$conn = connectDB();
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';
// Add error checking for the query
/*$query = "SELECT po.*, s.name as supplier_name, u.username as created_by_name 
          FROM purchase_orders po 
          LEFT JOIN suppliers s ON po.supplier_id = s.id 
          LEFT JOIN users u ON po.created_by = u.id 
          ORDER BY po.created_at DESC";*/

$date_created = date('Y-m-d');

$query = "SELECT 
            po.po_number,
            s.name AS supplier_name,
            po.created_at,
            po.expected_delivery_date,
            (po.unit_price * po.quantity) AS total_amount,
            po.ordering_branch,
            po.status
        FROM 
            u476900858_inventor.purchase_orders AS po
        INNER JOIN 
            u476900858_inventor.suppliers AS s
            ON po.supplier_id = s.id
        WHERE 
            po.ordering_branch = '$branch_name'
        GROUP BY 
            po.po_number";
$result = $conn->query($query);

// Add error handling for query execution
if ($result === false) {
    echo "Error executing query: " . $conn->error;
    exit();
}

?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Purchase Orders</h5>
                    <button type="button" class="btn btn-primary" onclick="window.location.href='create_purchase_order.php?id=reset'">
                        <i class="fas fa-plus me-2"></i>Create Purchase Order
                    </button>
                    
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>PO Number</th>
                                    <th>Supplier</th>
                                    <th>Date Created</th>
                                    <th>Expected Delivery</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                if ($result->num_rows > 0) {
                                    while ($po = $result->fetch_assoc()): 
                                ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($po['po_number']); ?></td>
                                    <td><?php echo htmlspecialchars($po['supplier_name']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($po['created_at'])); ?></td>
                                    <td><?php echo $po['expected_delivery_date'] ? date('M d, Y', strtotime($po['expected_delivery_date'])) : 'Not set'; ?></td>
                                    <td><?php echo number_format($po['total_amount'], 2); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo getStatusBadgeClass($po['status']); ?>">
                                            <?php echo ucfirst($po['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                    <a href="purchase_order_detail.php?po_number=<?php echo $po['po_number']; ?>" class="btn btn-info btn-sm">
                                                 <i class="fas fa-eye"></i>
                                    </a>

                                    </td>
                                </tr>
                                <?php 
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="7" class="text-center">No purchase orders found</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'draft': return 'secondary';
        case 'pending': return 'warning';
        case 'approved': return 'success';
        case 'sent': return 'info';
        case 'received': return 'primary';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

closeDB($conn);
require_once 'includes/footer.php';
?>

<style>
.search-results {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 4px;
}

.search-item:hover {
    background-color: #f8f9fa;
}
</style>

<script>
$(document).ready(function() {
    // Add new row button
    $('#add_row').click(function() {
        addNewRow();
    });

    // Function to add new row
    function addNewRow() {
        var newRow = `
            <tr class="item-row">
                <td>
                    <select class="form-select product-select" name="product_id[]" required>
                        <option value="">Select Product</option>
                    </select>
                </td>
                <td><input type="number" class="form-control quantity" name="quantity[]" min="1" required></td>
                <td><input type="number" class="form-control unit-price" name="unit_price[]" step="0.01" required readonly></td>
                <td><span class="total-price">0.00</span></td>
                <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
            </tr>
        `;
        $('#items_table tbody').append(newRow);
        initializeSelect2();
    }

    // Initialize Select2 for product selection
    function initializeSelect2() {
        $('.product-select').select2({
            placeholder: 'Search for a product...',
            minimumInputLength: 2,
            ajax: {
                url: 'search_products.php',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        term: params.term,
                        supplier_id: $('#supplier_id').val()
                    };
                },
                processResults: function(data) {
                    return {
                        results: data.map(function(item) {
                            return {
                                id: item.id,
                                text: item.name + ' (SKU: ' + item.sku + ')',
                                unit_price: item.unit_price
                            };
                        })
                    };
                },
                cache: true
            }
        }).on('select2:select', function(e) {
            var data = e.params.data;
            var row = $(this).closest('tr');
            row.find('.unit-price').val(data.unit_price);
            updateRowTotal(row);
        });
    }

    // Remove row
    $(document).on('click', '.remove-item', function() {
        $(this).closest('tr').remove();
        calculateGrandTotal();
    });

    // Update row total when quantity changes
    $(document).on('input', '.quantity', function() {
        updateRowTotal($(this).closest('tr'));
    });

    // Calculate row total
    function updateRowTotal(row) {
        var quantity = parseFloat(row.find('.quantity').val()) || 0;
        var unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
        var total = quantity * unitPrice;
        row.find('.total-price').text(total.toFixed(2));
        calculateGrandTotal();
    }

    // Calculate grand total
    function calculateGrandTotal() {
        var grandTotal = 0;
        $('.total-price').each(function() {
            grandTotal += parseFloat($(this).text()) || 0;
        });
        $('#total_amount').text(grandTotal.toFixed(2));
    }

    // Initialize first row
    initializeSelect2();

    // Add row button
    $('#items_table tfoot').prepend(`
        <tr>
            <td colspan="5">
                <button type="button" class="btn btn-success btn-sm" id="add_row">
                    <i class="fas fa-plus"></i> Add Item
                </button>
            </td>
        </tr>
    `);
});
</script>

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>