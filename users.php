<?php
// Remove session_start() as it's already in header.php
require_once 'config/database.php';

// Check if user is admin


// Include header after authorization check
require_once 'includes/header.php';

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$role_filter = '';
$sort_by = 'username';
$sort_order = 'ASC';
$page = 1;
$items_per_page = 10;

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['role'])) {
    $role_filter = $_GET['role'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Build query for users
$query = "SELECT *, IFNULL(admin_approved, 'No') as admin_approved FROM users WHERE 1=1";
$count_query = "SELECT COUNT(*) as total FROM users WHERE 1=1";

// Add search condition
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (username LIKE '%" . $conn->real_escape_string($search) . "%' 
               OR full_name LIKE '%" . $conn->real_escape_string($search) . "%'
               OR email LIKE '%" . $conn->real_escape_string($search) . "%')";
    $count_query .= " AND (username LIKE '%" . $conn->real_escape_string($search) . "%' 
                    OR full_name LIKE '%" . $conn->real_escape_string($search) . "%'
                    OR email LIKE '%" . $conn->real_escape_string($search) . "%')";
}

// Add role filter
if (!empty($role_filter)) {
    $query .= " AND role = '" . $conn->real_escape_string($role_filter) . "'";
    $count_query .= " AND role = '" . $conn->real_escape_string($role_filter) . "'";
}

// Get total count for pagination
$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Add sorting
$query .= " ORDER BY " . $conn->real_escape_string($sort_by) . " " . $conn->real_escape_string($sort_order);

// Add pagination
$offset = ($page - 1) * $items_per_page;
$query .= " LIMIT $items_per_page OFFSET $offset";

// Execute query
$result = $conn->query($query);

// Process form submission for adding/editing user
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_user'])) {
    $user_id = isset($_POST['user_id']) ? $_POST['user_id'] : '';
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $role = $_POST['role'];
    
    // Validate form data
    $errors = [];
    
    if (empty($full_name)) {
        $errors[] = "Full name is required";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    // Check if email already exists
    if (empty($user_id)) {
        // For new user
        $check_query = "SELECT * FROM users WHERE email = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $errors[] = "Email already exists";
        }
        
        $check_stmt->close();
        
        // Generate a random username and password for new user
        $username = strtolower(str_replace(' ', '', $full_name)) . rand(100, 999);
        $password = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8);
    } else {
        // For existing user, check if email exists for other users
        $check_query = "SELECT * FROM users WHERE email = ? AND id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $errors[] = "Email already exists";
        }
        
        $check_stmt->close();
    }
    
    // If no errors, save user
    if (empty($errors)) {
        if (!empty($user_id)) {
            // Update existing user
            $query = "UPDATE users SET 
                      full_name = ?, 
                      email = ?, 
                      role = ? 
                      WHERE id = ?";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssi", $full_name, $email, $role, $user_id);
            
            if ($stmt->execute()) {
                $success_message = "User updated successfully";
            } else {
                $errors[] = "Error updating user: " . $conn->error;
            }
        } else {
            // Insert new user
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $query = "INSERT INTO users (username, password, full_name, email, role) 
                      VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssss", $username, $hashed_password, $full_name, $email, $role);
            
            if ($stmt->execute()) {
                $success_message = "User added successfully. Username: " . $username . ", Password: " . $password;
            } else {
                $errors[] = "Error adding user: " . $conn->error;
            }
        }
        
        $stmt->close();
    }
}

// Process approve request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['approve_user'])) {
    $user_id = $_POST['user_id'];
    
    // Update user approval status
    $approve_query = "UPDATE users SET admin_approved = 'Yes' WHERE id = ?";
    $stmt = $conn->prepare($approve_query);
    $stmt->bind_param("i", $user_id);
    
    if ($stmt->execute()) {
        // Redirect to refresh the page
        header("Location: users.php?approved=1");
        exit();
    } else {
        $errors[] = "Error approving user: " . $conn->error;
    }
    
    $stmt->close();
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    $user_id = $_POST['user_id'];
    
    // Prevent deleting own account
    if ($user_id == $_SESSION['user_id']) {
        $errors[] = "You cannot delete your own account";
    } else {
        // Delete user
        $delete_query = "DELETE FROM users WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $user_id);
        
        if ($stmt->execute()) {
            // Redirect to refresh the page
            header("Location: users.php?deleted=1");
            exit();
        } else {
            $errors[] = "Error deleting user: " . $conn->error;
        }
        
        $stmt->close();
    }
}

// Close connection
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">User Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-2"></i> Add New User
        </button>
    </div>
    
    <?php if (isset($_GET['deleted']) && $_GET['deleted'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            User has been deleted successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_GET['approved']) && $_GET['approved'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            User has been approved successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header bg-light">
            <form method="get" action="users.php" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="Search users..." value="<?php echo htmlspecialchars($search); ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="role" onchange="this.form.submit()">
                        <option value="">All Roles</option>
                        <option value="admin" <?php echo ($role_filter == 'admin') ? 'selected' : ''; ?>>Admin</option>
                        <option value="manager" <?php echo ($role_filter == 'manager') ? 'selected' : ''; ?>>Manager</option>
                        <option value="staff" <?php echo ($role_filter == 'staff') ? 'selected' : ''; ?>>Staff</option>
                    </select>
                </div>
                <div class="col-md-5 text-md-end">
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-sync-alt me-2"></i> Reset
                    </a>
                </div>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>
                                <a href="users.php?sort_by=username&sort_order=<?php echo ($sort_by == 'username' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>" class="text-decoration-none text-dark">
                                    Username
                                    <?php if ($sort_by == 'username'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="users.php?sort_by=full_name&sort_order=<?php echo ($sort_by == 'full_name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>" class="text-decoration-none text-dark">
                                    Full Name
                                    <?php if ($sort_by == 'full_name'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>Email</th>
                            <th>
                                <a href="users.php?sort_by=role&sort_order=<?php echo ($sort_by == 'role' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>" class="text-decoration-none text-dark">
                                    Role
                                    <?php if ($sort_by == 'role'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="users.php?sort_by=admin_approved&sort_order=<?php echo ($sort_by == 'admin_approved' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>" class="text-decoration-none text-dark">
                                    Approved
                                    <?php if ($sort_by == 'admin_approved'): ?>
                                        <i class="fas fa-sort-<?php echo ($sort_order == 'ASC') ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($user = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <?php if ($user['role'] == 'admin'): ?>
                                            <span class="badge bg-danger">Admin</span>
                                        <?php elseif ($user['role'] == 'manager'): ?>
                                            <span class="badge bg-warning text-dark">Manager</span>
                                        <?php else: ?>
                                            <span class="badge bg-info text-dark">Staff</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($user['admin_approved']) && $user['admin_approved'] == 'Yes'): ?>
                                            <span class="badge bg-success">Yes</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">No</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary edit-user" 
                                                data-bs-toggle="modal" data-bs-target="#editUserModal"
                                                data-id="<?php echo $user['id']; ?>"
                                                data-fullname="<?php echo htmlspecialchars($user['full_name']); ?>"
                                                data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                data-role="<?php echo htmlspecialchars($user['role']); ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <?php if (!isset($user['admin_approved']) || $user['admin_approved'] != 'Yes'): ?>
                                                <button type="button" class="btn btn-sm btn-success approve-user"
                                                        data-bs-toggle="modal" data-bs-target="#approveUserModal"
                                                        data-id="<?php echo $user['id']; ?>"
                                                        data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                                        data-approved="<?php echo isset($user['admin_approved']) ? $user['admin_approved'] : 'No'; ?>">
                                                    <i class="fas fa-check"></i> Approve
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-danger delete-user"
                                                    data-bs-toggle="modal" data-bs-target="#deleteUserModal"
                                                    data-id="<?php echo $user['id']; ?>"
                                                    data-username="<?php echo htmlspecialchars($user['username']); ?>">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <p class="mb-0 text-muted">No users found</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="users.php?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                                Previous
                            </a>
                        </li>
                        
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="users.php?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="users.php?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="users.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name *</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role *</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="admin">Admin</option>
                            <option value="manager">Manager</option>
                            <option value="staff" selected>Staff</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        A username and password will be automatically generated and displayed after the user is created.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_user" class="btn btn-primary">Save User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="users.php">
                <input type="hidden" name="user_id" id="edit_user_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name *</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role *</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="admin">Admin</option>
                            <option value="manager">Manager</option>
                            <option value="staff">Staff</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Username and password cannot be changed through this form.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="save_user" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Approve User Modal -->
<div class="modal fade" id="approveUserModal" tabindex="-1" aria-labelledby="approveUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="users.php">
                <input type="hidden" name="user_id" id="approve_user_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveUserModalLabel">Confirm Approval</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to approve the user: <strong id="approve_user_name"></strong>?</p>
                    <p>This will grant the user access to the system.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="approve_user" class="btn btn-success">Approve User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="users.php">
                <input type="hidden" name="user_id" id="delete_user_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteUserModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the user: <strong id="delete_user_name"></strong>?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_user" class="btn btn-danger">Delete User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Edit user modal data
document.addEventListener('DOMContentLoaded', function() {
    const editButtons = document.querySelectorAll('.edit-user');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const fullname = this.getAttribute('data-fullname');
            const email = this.getAttribute('data-email');
            const role = this.getAttribute('data-role');
            
            document.getElementById('edit_user_id').value = id;
            document.getElementById('edit_full_name').value = fullname;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_role').value = role;
        });
    });
    
    // Approve user modal data
    const approveButtons = document.querySelectorAll('.approve-user');
    approveButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            const approved = this.getAttribute('data-approved');
            
            // If already approved, disable the approve button
            if (approved === 'Yes') {
                alert('This user is already approved.');
                return false;
            }
            
            document.getElementById('approve_user_id').value = id;
            document.getElementById('approve_user_name').textContent = username;
        });
    });
    
    // Delete user modal data
    const deleteButtons = document.querySelectorAll('.delete-user');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            
            document.getElementById('delete_user_id').value = id;
            document.getElementById('delete_user_name').textContent = username;
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>