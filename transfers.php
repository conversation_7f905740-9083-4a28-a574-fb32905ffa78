<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$status_filter = '';
$date_from = '';
$date_to = '';
$sort_by = 'created_at';
$sort_order = 'DESC';
$page = 1;
$items_per_page = 10;

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['status'])) {
    $status_filter = $_GET['status'];
}

if (isset($_GET['date_from'])) {
    $date_from = $_GET['date_from'];
}

if (isset($_GET['date_to'])) {
    $date_to = $_GET['date_to'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Create transfers table if it doesn't exist
$create_transfers_table = "CREATE TABLE IF NOT EXISTS transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_number VARCHAR(50) NOT NULL UNIQUE,
    source_branch VARCHAR(100) NOT NULL,
    destination_branch VARCHAR(100) NOT NULL,
    status ENUM('pending', 'in-transit', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
)";

$conn->query($create_transfers_table);

// Create transfer items table if it doesn't exist
$create_transfer_items_table = "CREATE TABLE IF NOT EXISTS transfer_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
)";

$conn->query($create_transfer_items_table);

// Process form submission for creating a new transfer
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_transfer'])) {
    $source_branch = $_POST['source_branch'];
    $destination_branch = $_POST['destination_branch'];
    $notes = trim($_POST['notes']);
    $products = isset($_POST['products']) ? $_POST['products'] : [];
    $quantities = isset($_POST['quantities']) ? $_POST['quantities'] : [];

    // Validate form data
    $errors = [];

    if (empty($source_branch)) {
        $errors[] = "Source branch is required";
    }

    if (empty($destination_branch)) {
        $errors[] = "Destination branch is required";
    }

    if ($source_branch === $destination_branch) {
        $errors[] = "Source and destination branches cannot be the same";
    }

    if (empty($products)) {
        $errors[] = "At least one product must be selected";
    }

    // Validate product quantities
    $valid_products = [];
    if (!empty($products)) {
        foreach ($products as $index => $product_id) {
            if (isset($quantities[$index]) && $quantities[$index] > 0) {
                // Check if product exists and has enough stock
                $product_query = "SELECT id, name, quantity FROM products WHERE id = ?";
                $stmt = $conn->prepare($product_query);
                $stmt->bind_param("i", $product_id);
                $stmt->execute();
                $product_result = $stmt->get_result();

                if ($product_result->num_rows > 0) {
                    $product = $product_result->fetch_assoc();
                    if ($product['quantity'] >= $quantities[$index]) {
                        $valid_products[] = [
                            'id' => $product_id,
                            'quantity' => $quantities[$index]
                        ];
                    } else {
                        $errors[] = "Not enough stock for product '{$product['name']}'. Available: {$product['quantity']}";
                    }
                } else {
                    $errors[] = "Product with ID $product_id not found";
                }

                $stmt->close();
            }
        }
    }

    if (empty($valid_products)) {
        $errors[] = "No valid products selected for transfer";
    }

    // If no errors, create transfer
    if (empty($errors)) {
        // Generate transfer number
        $transfer_number = 'TRF-' . date('Ymd') . '-' . rand(1000, 9999);

        // Begin transaction
        $conn->begin_transaction();

        try {
            // Insert transfer
            $transfer_query = "INSERT INTO transfers (transfer_number, source_branch, destination_branch, notes, created_by)
                             VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($transfer_query);
            $stmt->bind_param("ssssi", $transfer_number, $source_branch, $destination_branch, $notes, $_SESSION['user_id']);
            $stmt->execute();
            $transfer_id = $stmt->insert_id;
            $stmt->close();

            // Insert transfer items and update product quantities
            foreach ($valid_products as $product) {
                // Insert transfer item
                $item_query = "INSERT INTO transfer_items (transfer_id, product_id, quantity) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($item_query);
                $stmt->bind_param("iii", $transfer_id, $product['id'], $product['quantity']);
                $stmt->execute();
                $stmt->close();

                // Update product quantity (reduce from source)
                // Update product quantity (reduce from source)
                $update_query = "UPDATE products SET quantity = quantity - ? WHERE id = ? AND branch_name = ?";
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("iis", $product['quantity'], $product['id'], $source_branch);
                $stmt->execute();
                $stmt->close();
            }

            // Commit transaction
            $conn->commit();
            $success_message = "Transfer created successfully with transfer number: $transfer_number";
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errors[] = "Error creating transfer: " . $e->getMessage();
        }
    }
}

// Handle AJAX request for loading products by branch
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['load_products'])) {
    $branch_name = $_POST['source_branch'];

    // Get products for the selected branch
    $products_query = "SELECT p.*, c.name as category_name
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.quantity > 0 AND p.branch_name = ?
                    ORDER BY p.name ASC";
    $stmt = $conn->prepare($products_query);
    $stmt->bind_param("s", $branch_name);
    $stmt->execute();
    $products_result = $stmt->get_result();

    // Output product rows HTML
    while ($product = $products_result->fetch_assoc()) {
        echo '<tr>
            <td>' . htmlspecialchars($product['name']) . '</td>
            <td>' . htmlspecialchars($product['category_name'] ?? 'Uncategorized') . '</td>
            <td>';
        if ($product['quantity'] <= 0) {
            echo '<span class="badge bg-danger">Out of Stock</span>';
        } elseif ($product['quantity'] <= $product['reorder_level']) {
            echo '<span class="badge bg-warning text-dark">Low Stock (' . $product['quantity'] . ')</span>';
        } else {
            echo '<span class="badge bg-success">' . $product['quantity'] . '</span>';
        }
        echo '</td>
            <td>
                <div class="input-group input-group-sm">
                    <input type="number" class="form-control form-control-sm product-quantity" value="1" min="1" max="' . $product['quantity'] . '" ' . ($product['quantity'] <= 0 ? 'disabled' : '') . '>
                    <button type="button" class="btn btn-sm btn-primary add-product-btn"
                            data-id="' . $product['id'] . '"
                            data-name="' . htmlspecialchars($product['name']) . '"
                            data-max="' . $product['quantity'] . '"
                            ' . ($product['quantity'] <= 0 ? 'disabled' : '') . '>
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </td>
        </tr>';
    }

    // If no products found
    if ($products_result->num_rows == 0) {
        echo '<tr><td colspan="4" class="text-center">No products found for this branch.</td></tr>';
    }

    $stmt->close();
    exit; // Stop further execution since this is an AJAX response
}

// Process update status request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $transfer_id = $_POST['transfer_id'];
    $new_status = $_POST['status'];
    $old_status = '';

    // First, get the transfer information (separate from items)
    $transfer_info_query = "SELECT * FROM transfers WHERE id = ?";
    $stmt = $conn->prepare($transfer_info_query);
    $stmt->bind_param("i", $transfer_id);
    $stmt->execute();
    $transfer_info_result = $stmt->get_result();

    if ($transfer_info_result->num_rows > 0) {
        $transfer_info = $transfer_info_result->fetch_assoc();
        $old_status = $transfer_info['status'];
        $destination_branch = $transfer_info['destination_branch'];
        $source_branch = $transfer_info['source_branch'];

        // Now get the transfer items
        $transfer_items_query = "SELECT product_id, quantity FROM transfer_items WHERE transfer_id = ?";
        $stmt = $conn->prepare($transfer_items_query);
        $stmt->bind_param("i", $transfer_id);
        $stmt->execute();
        $transfer_items_result = $stmt->get_result();

        $transfer_items = [];
        while ($item = $transfer_items_result->fetch_assoc()) {
            $transfer_items[] = [
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity']
            ];
        }

        // Begin transaction
        $conn->begin_transaction();

        try {
            // Update transfer status
            $update_query = "UPDATE transfers SET status = ? WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("si", $new_status, $transfer_id);
            $stmt->execute();

            // If status is changed to 'completed', update destination branch inventory
            if ($new_status === 'completed' && $old_status !== 'completed') {
                foreach ($transfer_items as $item) {
                    // Check if product exists in destination branch
                    $check_query = "SELECT id FROM products WHERE id = ? AND branch_name = ?";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->bind_param("is", $item['product_id'], $destination_branch);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();

                    if ($check_result->num_rows > 0) {
                        // Update existing product quantity in destination branch
                        $update_dest_query = "UPDATE products SET quantity = quantity + ? WHERE id = ? AND branch_name = ?";
                        $update_dest_stmt = $conn->prepare($update_dest_query);
                        $update_dest_stmt->bind_param("iis", $item['quantity'], $item['product_id'], $destination_branch);
                        $update_dest_stmt->execute();
                        $update_dest_stmt->close();
                    } else {
                        // Get product details from source branch
                        $product_query = "SELECT * FROM products WHERE id = ?";
                        $product_stmt = $conn->prepare($product_query);
                        $product_stmt->bind_param("i", $item['product_id']);
                        $product_stmt->execute();
                        $product_result = $product_stmt->get_result();

                        if ($product_result->num_rows > 0) {
                            $product = $product_result->fetch_assoc();

                            // Create new product entry for destination branch
                            $new_product_query = "INSERT INTO products (name, description, category_id, brand_id, sku, quantity, unit_price, reorder_level, image_url, branch_name)
                                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                            $new_product_stmt = $conn->prepare($new_product_query);
                            // Create unique SKU for destination branch
                            $new_sku = $product['sku'] . '-' . substr($destination_branch, 0, 3);

                            $new_product_stmt->bind_param("ssiisddiss",
                                $product['name'],
                                $product['description'],
                                $product['category_id'],
                                $product['brand_id'],
                                $new_sku,
                                $item['quantity'],
                                $product['unit_price'],
                                $product['reorder_level'],
                                $product['image_url'],
                                $destination_branch
                            );
                            $new_product_stmt->execute();
                            $new_product_stmt->close();
                        }
                        $product_stmt->close();
                    }
                    $check_stmt->close();
                }
                $success_message = "Transfer status updated to completed. Products have been added to {$destination_branch} inventory.";
            } elseif ($new_status === 'cancelled' && $old_status !== 'cancelled' && $old_status !== 'completed') {
                // If status is changed to 'cancelled', return products to source branch

                foreach ($transfer_items as $item) {
                    // Return quantity to source branch
                    $return_query = "UPDATE products SET quantity = quantity + ? WHERE id = ? AND branch_name = ?";
                    $return_stmt = $conn->prepare($return_query);
                    $return_stmt->bind_param("iis", $item['quantity'], $item['product_id'], $source_branch);
                    $return_stmt->execute();
                    $return_stmt->close();
                }
                $success_message = "Transfer cancelled. Products have been returned to {$source_branch} inventory.";
            } else {
                $success_message = "Transfer status updated successfully.";
            }

            // Commit transaction
            $conn->commit();
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error_message = "Error updating transfer status: " . $e->getMessage();
        }
    } else {
        $error_message = "Transfer not found.";
    }

    $stmt->close();
}

// Build query for transfers
$query = "SELECT t.*, u.username FROM transfers t
          LEFT JOIN users u ON t.created_by = u.id
          WHERE t.source_branch = '" . $conn->real_escape_string($branch_name) . "' OR t.destination_branch = '" . $conn->real_escape_string($branch_name) . "'";

$count_query = "SELECT COUNT(*) as total FROM transfers t
                LEFT JOIN users u ON t.created_by = u.id
                WHERE t.source_branch = '" . $conn->real_escape_string($branch_name) . "' OR t.destination_branch = '" . $conn->real_escape_string($branch_name) . "'";

// Add search condition
if (!empty($search)) {
    $search_term = "%{$search}%";
    $query .= " AND (t.transfer_number LIKE '%" . $conn->real_escape_string($search) . "%'
               OR t.source_branch LIKE '%" . $conn->real_escape_string($search) . "%'
               OR t.destination_branch LIKE '%" . $conn->real_escape_string($search) . "%')";
    $count_query .= " AND (t.transfer_number LIKE '%" . $conn->real_escape_string($search) . "%'
                    OR t.source_branch LIKE '%" . $conn->real_escape_string($search) . "%'
                    OR t.destination_branch LIKE '%" . $conn->real_escape_string($search) . "%')";
}

// Add status filter
if (!empty($status_filter)) {
    $query .= " AND t.status = '" . $conn->real_escape_string($status_filter) . "'";
    $count_query .= " AND t.status = '" . $conn->real_escape_string($status_filter) . "'";
}

// Add date range filter
if (!empty($date_from)) {
    $query .= " AND DATE(t.created_at) >= '" . $conn->real_escape_string($date_from) . "'";
    $count_query .= " AND DATE(t.created_at) >= '" . $conn->real_escape_string($date_from) . "'";
}

if (!empty($date_to)) {
    $query .= " AND DATE(t.created_at) <= '" . $conn->real_escape_string($date_to) . "'";
    $count_query .= " AND DATE(t.created_at) <= '" . $conn->real_escape_string($date_to) . "'";
}

// Get total count for pagination
$count_result = $conn->query($count_query);
$total_items = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_items / $items_per_page);

// Add sorting
$query .= " ORDER BY t." . $conn->real_escape_string($sort_by) . " " . $conn->real_escape_string($sort_order);

// Add pagination
$offset = ($page - 1) * $items_per_page;
$query .= " LIMIT $items_per_page OFFSET $offset";

// Execute query
$result = $conn->query($query);

// Get all products for the product selection
// Default to the branch in session if available
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// If source_branch is set in POST, use that instead
if (isset($_POST['source_branch']) && !empty($_POST['source_branch'])) {
    $branch_name = $_POST['source_branch'];
}

$products_query = "SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.quantity > 0 AND p.branch_name = ?
                ORDER BY p.name ASC";
$stmt = $conn->prepare($products_query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$products_result = $stmt->get_result();
$stmt->close();

// Close connection
closeDB($conn);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Transfer Stocks</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTransferModal">
            <i class="fas fa-plus me-2"></i> Create New Transfer
        </button>
    </div>
    
    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            Transfer has been created successfully.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Transfer #, Branch...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="in-transit" <?php echo $status_filter === 'in-transit' ? 'selected' : ''; ?>>In Transit</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">Filter</button>
                    <a href="transfers.php" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Use event delegation on a static parent element
    document.body.addEventListener('click', function(event) {
        const target = event.target;

        // For viewing transfer details
        if (target.closest('.btn-info[data-bs-target="viewTransferModal"]')) {
            const button = target.closest('.btn-info');
            const transferId = button.getAttribute('data-id');
            const modal = new bootstrap.Modal(document.getElementById('viewTransferModal'));
            
            // Fetch and display data
            const transferDetails = document.getElementById('transfer-details');
            const loadingSpinner = document.querySelector('#viewTransferModal .spinner-border').parentNode;

            loadingSpinner.classList.remove('d-none');
            transferDetails.classList.add('d-none');

            fetch(`get_transfer_details.php?id=${transferId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) throw new Error(data.error);
                    
                    document.getElementById('transfer-number').textContent = data.transfer.transfer_number;
                    document.getElementById('transfer-status').innerHTML = `<span class="badge bg-primary">${data.transfer.status}</span>`;
                    document.getElementById('source-branch').textContent = data.transfer.source_branch;
                    document.getElementById('destination-branch').textContent = data.transfer.destination_branch;
                    document.getElementById('created-by').textContent = data.transfer.created_by_name || 'N/A';
                    document.getElementById('created-date').textContent = new Date(data.transfer.created_at).toLocaleString();
                    document.getElementById('approved-by').textContent = data.transfer.approved_by_name || 'N/A';
                    document.getElementById('approved-date').textContent = data.transfer.approved_at ? new Date(data.transfer.approved_at).toLocaleString() : 'N/A';
                    document.getElementById('transfer-notes').textContent = data.transfer.notes || 'No notes available';

                    const itemsContainer = document.getElementById('transfer-items');
                    itemsContainer.innerHTML = '';
                    let totalValue = 0;
                    data.items.forEach(item => {
                        const row = document.createElement('tr');
                        const total = item.quantity * item.unit_price;
                        totalValue += total;
                        row.innerHTML = `
                            <td>${item.product_name}</td>
                            <td>${item.sku}</td>
                            <td class="text-end">${item.quantity}</td>
                            <td class="text-end">${new Intl.NumberFormat('en-PH', { style: 'currency', currency: 'PHP' }).format(item.unit_price)}</td>
                            <td class="text-end">${new Intl.NumberFormat('en-PH', { style: 'currency', currency: 'PHP' }).format(total)}</td>
                        `;
                        itemsContainer.appendChild(row);
                    });
                    document.getElementById('total-value').textContent = new Intl.NumberFormat('en-PH', { style: 'currency', currency: 'PHP' }).format(totalValue);
                    
                    loadingSpinner.classList.add('d-none');
                    transferDetails.classList.remove('d-none');
                })
                .catch(error => {
                    transferDetails.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                    loadingSpinner.classList.add('d-none');
                    transferDetails.classList.remove('d-none');
                });

            modal.show();
        }

        // For updating status
        if (target.closest('.btn-primary[data-bs-target="#updateStatusModal"]')) {
            const button = target.closest('.btn-primary');
            document.getElementById('status_transfer_id').value = button.getAttribute('data-id');
            document.getElementById('status_transfer_number').textContent = button.getAttribute('data-number');
            const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
            modal.show();
        }

        // For printing receipt
        if (target.closest('.print-transfer-btn')) {
            const button = target.closest('.print-transfer-btn');
            const transferId = button.getAttribute('data-id');
            showTransferReceipt(transferId);
        }
    });

    function showTransferReceipt(transferId) {
        const modal = new bootstrap.Modal(document.getElementById('transferReceiptModal'), { backdrop: 'static', keyboard: false });
        const receiptContent = document.getElementById('transferReceiptContent');
        
        receiptContent.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Loading receipt...</p></div>';
        modal.show();

        fetch(`get_transfer_receipt.php?id=${transferId}`)
            .then(response => response.text())
            .then(html => { receiptContent.innerHTML = html; })
            .catch(error => {
                receiptContent.innerHTML = `<div class="alert alert-danger m-3">Error: ${error.message}</div>`;
            });
    }
});
</script>

    <!-- Transfers List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Stock Transfers</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Transfer #</th>
                            <th>Source</th>
                            <th>Destination</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($transfer = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($transfer['transfer_number']); ?></td>
                                    <td><?php echo htmlspecialchars($transfer['source_branch']); ?></td>
                                    <td><?php echo htmlspecialchars($transfer['destination_branch']); ?></td>
                                    <td>
                                        <?php if ($transfer['status'] == 'pending'): ?>
                                            <span class="badge bg-warning text-dark">Pending</span>
                                        <?php elseif ($transfer['status'] == 'in-transit'): ?>
                                            <span class="badge bg-info">In Transit</span>
                                        <?php elseif ($transfer['status'] == 'completed'): ?>
                                            <span class="badge bg-success">Completed</span>
                                        <?php elseif ($transfer['status'] == 'cancelled'): ?>
                                            <span class="badge bg-danger">Cancelled</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($transfer['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($transfer['username'] ?? 'Unknown'); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#viewTransferModal" data-id="<?php echo $transfer['id']; ?>" data-number="<?php echo htmlspecialchars($transfer['transfer_number']); ?>">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($transfer['status'] != 'completed' && $transfer['status'] != 'cancelled'): ?>
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-id="<?php echo $transfer['id']; ?>" data-number="<?php echo htmlspecialchars($transfer['transfer_number']); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-secondary print-transfer-btn" data-id="<?php echo $transfer['id']; ?>">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center">No transfers found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>&sort_by=<?php echo urlencode($sort_by); ?>&sort_order=<?php echo urlencode($sort_order); ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create Transfer Modal -->
 
<div class="modal fade" id="createTransferModal" tabindex="-1" aria-labelledby="createTransferModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form method="post" action="transfers.php" id="transferForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="createTransferModalLabel">Create New Stock Transfer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="source_branch" class="form-label">Source Branch *</label>
                            <select class="form-select" id="source_branch" name="source_branch" required>
                                <option value="">Select Source Branch</option>
                            </select>
                            <div class="form-text text-muted" id="source-branch-status">Loading branches...</div>
                        </div>
                        <div class="col-md-6">
                            <label for="destination_branch" class="form-label">Destination Branch *</label>
                            <select class="form-select" id="destination_branch" name="destination_branch" required>
                                <option value="">Select Destination Branch</option>
                            </select>
                            <div class="form-text text-muted" id="destination-branch-status">Select source branch first</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">Select Products from Source Branch</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <input type="text" id="product-search" class="form-control" placeholder="Search products...">
                                    </div>

                                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                        <table class="table table-sm table-hover" id="products-table">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Category</th>
                                                    <th>Stock</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($product = $products_result->fetch_assoc()): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></td>
                                                    <td>
                                                        <?php if ($product['quantity'] <= 0): ?>
                                                            <span class="badge bg-danger">Out of Stock</span>
                                                        <?php elseif ($product['quantity'] <= $product['reorder_level']): ?>
                                                            <span class="badge bg-warning text-dark">Low Stock (<?php echo $product['quantity']; ?>)</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success"><?php echo $product['quantity']; ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="input-group input-group-sm">
                                                            <input type="number" class="form-control form-control-sm product-quantity" value="1" min="1" max="<?php echo $product['quantity']; ?>" <?php echo ($product['quantity'] <= 0) ? 'disabled' : ''; ?>>
                                                            <button type="button" class="btn btn-sm btn-primary add-product-btn"
                                                                    data-id="<?php echo $product['id']; ?>"
                                                                    data-name="<?php echo htmlspecialchars($product['name']); ?>"
                                                                    data-max="<?php echo $product['quantity']; ?>"
                                                                    <?php echo ($product['quantity'] <= 0) ? 'disabled' : ''; ?>>
                                                                <i class="fas fa-plus"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Selected Products</h6>
                                </div>
                                <div class="card-body">
                                    <div id="no-products-selected" class="text-center py-3">
                                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                        <p>No products selected yet.</p>
                                        <p class="text-muted">Select products from the list on the left.</p>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-sm" id="selected-products-table">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Quantity</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Selected products will be added here dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="submit-transfer" name="create_transfer" disabled>Create Transfer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Transfer Modal -->

<div class="modal fade" id="viewTransferModal" tabindex="-1" aria-labelledby="viewTransferModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewTransferModalLabel">Transfer Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading transfer details...</p>
                </div>

                <div id="transfer-details" class="d-none">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Transfer Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="40%">Transfer Number:</th>
                                    <td id="transfer-number"></td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td id="transfer-status"></td>
                                </tr>
                                <tr>
                                    <th>Source Branch:</th>
                                    <td id="source-branch"></td>
                                </tr>
                                <tr>
                                    <th>Destination Branch:</th>
                                    <td id="destination-branch"></td>
                                </tr>
                                <tr>
                                    <th>Created By:</th>
                                    <td id="created-by"></td>
                                </tr>
                                <tr>
                                    <th>Created Date:</th>
                                    <td id="created-date"></td>
                                </tr>
                                <tr>
                                    <th>Approved By:</th>
                                    <td id="approved-by"></td>
                                </tr>
                                <tr>
                                    <th>Approved Date:</th>
                                    <td id="approved-date"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Notes</h6>
                            <div id="transfer-notes" class="p-3 bg-light rounded"></div>
                        </div>
                    </div>

                    <h6 class="text-muted mb-3">Transfer Items</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>Product Name</th>
                                    <th>SKU</th>
                                    <th class="text-end">Quantity</th>
                                    <th class="text-end">Unit Price</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody id="transfer-items">
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">Total Value:</th>
                                    <th class="text-end" id="total-value"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="transfers.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Transfer Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="transfer_id" id="status_transfer_id">

                    <p>Update status for transfer: <strong id="status_transfer_number"></strong></p>

                    <div class="mb-3">
                        <label for="status" class="form-label">New Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="pending">Pending</option>
                            <option value="in-transit">In Transit</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" name="update_status">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Receipt Modal -->
<div class="modal fade" id="transferReceiptModal" tabindex="-1" aria-labelledby="transferReceiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content receipt-floating-box">
            <div class="modal-header">
                <h5 class="modal-title" id="transferReceiptModalLabel">Transfer Receipt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0" style="overflow: hidden;">
                <div id="transferReceiptContent" class="transfer-receipt-container">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading receipt...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printTransferReceipt()">Print Receipt</button>
            </div>
        </div>
    </div>
</div>

<!-- Add these styles -->
<style>
    /* Transfer Receipt Styles */
    .receipt-floating-box {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        border: none;
        overflow: hidden;
    }

    .transfer-receipt-container {
        width: 8.5in;
        height: 11in;
        margin: 0 auto;
        padding: 0.5in;
        background-color: #fff;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .company-header {
        text-align: center;
        margin-bottom: 0.5in;
        padding-bottom: 0.3in;
        border-bottom: 2px solid #3498db;
    }

    .company-logo {
        max-width: 2in;
        margin-bottom: 0.2in;
    }

    .receipt-title {
        color: #2c3e50;
        font-size: 24pt;
        font-weight: bold;
        margin: 0.2in 0;
        text-transform: uppercase;
        letter-spacing: 2px;
    }

    .receipt-number {
        color: #7f8c8d;
        font-size: 14pt;
        font-weight: bold;
        margin-bottom: 0.2in;
    }

    .info-section {
        margin-bottom: 0.4in;
        display: flex;
        flex-wrap: wrap;
    }

    .info-group {
        width: 50%;
        margin-bottom: 0.3in;
    }

    .info-label {
        color: #7f8c8d;
        font-size: 12pt;
        font-weight: bold;
        margin-bottom: 0.1in;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .info-value {
        color: #2c3e50;
        font-size: 12pt;
        margin-bottom: 0.1in;
        font-weight: 500;
    }

    .items-table {
        width: 100%;
        margin: 0.3in 0;
        border-collapse: collapse;
        font-size: 11pt;
    }

    .items-table th {
        background: #f8f9fa;
        padding: 0.15in;
        text-align: left;
        border-bottom: 2px solid #dee2e6;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .items-table td {
        padding: 0.15in;
        border-bottom: 1px solid #dee2e6;
    }

    .items-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .footer {
        margin-top: 0.5in;
        padding-top: 0.3in;
        border-top: 2px solid #3498db;
        text-align: center;
        color: #7f8c8d;
        font-size: 10pt;
        position: absolute;
        bottom: 0.5in;
        left: 0.5in;
        right: 0.5in;
    }

    .status-badge {
        display: inline-block;
        padding: 0.1in 0.2in;
        border-radius: 0.2in;
        font-weight: bold;
        font-size: 11pt;
        text-transform: uppercase;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-in-transit {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .notes-section {
        margin-top: 0.3in;
        padding: 0.2in;
        background-color: #f8f9fa;
        border-radius: 0.2in;
        font-size: 11pt;
        border: 1px solid #dee2e6;
    }

    .notes-label {
        font-weight: bold;
        margin-bottom: 0.1in;
        color: #7f8c8d;
    }

    .notes-content {
        color: #2c3e50;
    }

    /* Modal backdrop styles */
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }

    /* Ensure the modal doesn't disrupt the page layout */
    .modal {
        padding-right: 0 !important;
    }

    .modal-open {
        overflow: auto !important;
        padding-right: 0 !important;
    }

    /* Prevent body scroll when modal is open */
    body.modal-open {
        position: fixed;
        width: 100%;
    }

    /* Fix for modal causing layout shift */
    .modal-dialog {
        margin: 1.75rem auto;
    }

    /* Force footer to bottom of page */
    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .container-fluid {
        flex: 1 0 auto;
        display: flex;
        flex-direction: column;
    }

    .row {
        flex: 1 0 auto;
    }

    main {
        flex: 1 0 auto;
        padding-bottom: 2rem;
    }

    .footer {
        flex-shrink: 0;
        margin-top: auto !important;
    }

    /* Ensure content doesn't overlap footer */
    .container-fluid.py-4 {
        padding-bottom: 4rem !important;
    }

    @media print {
        @page {
            size: 8.5in 11in;
            margin: 0;
        }

        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            overflow: hidden;
        }

        .transfer-receipt-container {
            box-shadow: none;
            width: 8.5in;
            height: 11in;
            padding: 0.5in;
            margin: 0;
            overflow: hidden;
        }

        .modal-content {
            border: none;
            box-shadow: none;
            overflow: hidden;
        }

        .modal-header, .modal-footer {
            display: none;
        }

        .modal-body {
            padding: 0;
            overflow: hidden;
        }

        /* Hide footer on print */
        .footer {
            display: none !important;
        }
    }
</style>

<script>
    // Function to show transfer receipt
    function showTransferReceipt(transferId) {
        // Create modal instance with static backdrop
        const modal = new bootstrap.Modal(document.getElementById('transferReceiptModal'), {
            backdrop: 'static',
            keyboard: false
        });

        const receiptContent = document.getElementById('transferReceiptContent');
        const modalElement = document.getElementById('transferReceiptModal');

        // Show loading spinner
        receiptContent.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading receipt...</p>
            </div>
        `;

        // Show modal
        modal.show();

        // Fetch receipt content
        fetch('get_transfer_receipt.php?id=' + transferId)
            .then(response => response.text())
            .then(html => {
                receiptContent.innerHTML = html;
            })
            .catch(error => {
                receiptContent.innerHTML = `
                    <div class="alert alert-danger m-3">
                        Error loading receipt: ${error.message}
                    </div>
                `;
            });

        // Add event listener for modal close
        modalElement.addEventListener('hidden.bs.modal', function () {
            // Clean up the modal content
            receiptContent.innerHTML = '';
            // Remove the modal backdrop
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            // Remove modal-open class from body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });
    }

    // Function to print transfer receipt
    function printTransferReceipt() {
        // Get the receipt content
        const receiptContent = document.getElementById('transferReceiptContent').innerHTML;

        // Create a hidden iframe for printing
        const printFrame = document.createElement('iframe');
        printFrame.style.display = 'none';
        printFrame.name = 'print_frame';
        document.body.appendChild(printFrame);

        // Create the HTML content for the print frame
        const printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Transfer Receipt</title>
                <style>
                    @page {
                        size: 8.5in 11in;
                        margin: 0;
                    }

                    body {
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                        overflow: hidden;
                    }

                    .transfer-receipt-container {
                        width: 8.5in;
                        height: 11in;
                        margin: 0 auto;
                        padding: 0.5in;
                        background-color: #fff;
                        position: relative;
                    }

                    .company-header {
                        text-align: center;
                        margin-bottom: 0.5in;
                        padding-bottom: 0.3in;
                        border-bottom: 2px solid #3498db;
                    }

                    .company-logo {
                        max-width: 2in;
                        margin-bottom: 0.2in;
                    }

                    .receipt-title {
                        color: #2c3e50;
                        font-size: 24pt;
                        font-weight: bold;
                        margin: 0.2in 0;
                        text-transform: uppercase;
                        letter-spacing: 2px;
                    }

                    .receipt-number {
                        color: #7f8c8d;
                        font-size: 14pt;
                        font-weight: bold;
                        margin-bottom: 0.2in;
                    }

                    .info-section {
                        margin-bottom: 0.4in;
                        display: flex;
                        flex-wrap: wrap;
                    }

                    .info-group {
                        width: 50%;
                        margin-bottom: 0.3in;
                    }

                    .info-label {
                        color: #7f8c8d;
                        font-size: 12pt;
                        font-weight: bold;
                        margin-bottom: 0.1in;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }

                    .info-value {
                        color: #2c3e50;
                        font-size: 12pt;
                        margin-bottom: 0.1in;
                        font-weight: 500;
                    }

                    .items-table {
                        width: 100%;
                        margin: 0.3in 0;
                        border-collapse: collapse;
                        font-size: 11pt;
                    }

                    .items-table th {
                        background: #f8f9fa;
                        padding: 0.15in;
                        text-align: left;
                        border-bottom: 2px solid #dee2e6;
                        font-weight: bold;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }

                    .items-table td {
                        padding: 0.15in;
                        border-bottom: 1px solid #dee2e6;
                    }

                    .items-table tr:nth-child(even) {
                        background-color: #f9f9f9;
                    }

                    .footer {
                        margin-top: 0.5in;
                        padding-top: 0.3in;
                        border-top: 2px solid #3498db;
                        text-align: center;
                        color: #7f8c8d;
                        font-size: 10pt;
                        position: absolute;
                        bottom: 0.5in;
                        left: 0.5in;
                        right: 0.5in;
                    }

                    .status-badge {
                        display: inline-block;
                        padding: 0.1in 0.2in;
                        border-radius: 0.2in;
                        font-weight: bold;
                        font-size: 11pt;
                        text-transform: uppercase;
                    }

                    .status-pending {
                        background-color: #fff3cd;
                        color: #856404;
                    }

                    .status-in-transit {
                        background-color: #cce5ff;
                        color: #004085;
                    }

                    .status-completed {
                        background-color: #d4edda;
                        color: #155724;
                    }

                    .status-cancelled {
                        background-color: #f8d7da;
                        color: #721c24;
                    }

                    .notes-section {
                        margin-top: 0.3in;
                        padding: 0.2in;
                        background-color: #f8f9fa;
                        border-radius: 0.2in;
                        font-size: 11pt;
                        border: 1px solid #dee2e6;
                    }

                    .notes-label {
                        font-weight: bold;
                        margin-bottom: 0.1in;
                        color: #7f8c8d;
                    }

                    .notes-content {
                        color: #2c3e50;
                    }
                </style>
            </head>
            <body>
                ${receiptContent}
            </body>
            </html>
        `;



        // Write the content to the iframe
        printFrame.contentWindow.document.open();
        printFrame.contentWindow.document.write(printContent);
        printFrame.contentWindow.document.close();

        // Wait for the iframe to load, then print
        printFrame.onload = function() {
            printFrame.contentWindow.focus();
            printFrame.contentWindow.print();

            // Remove the iframe after printing
            setTimeout(function() {
                document.body.removeChild(printFrame);
            }, 1000);
        };
    }

    // Add event listeners for print buttons
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.print-transfer-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transferId = this.getAttribute('data-id');
                showTransferReceipt(transferId);
            });
        });
    });

    // Function to load branches dynamically
    function loadBranchesForModal() {
        const sourceBranchSelect = document.getElementById('source_branch');
        const destinationBranchSelect = document.getElementById('destination_branch');
        const sourceStatus = document.getElementById('source-branch-status');
        const destStatus = document.getElementById('destination-branch-status');

        // Show loading state
        sourceBranchSelect.innerHTML = '<option value="">Loading branches...</option>';
        sourceBranchSelect.disabled = true;
        sourceStatus.textContent = 'Loading branches...';
        sourceStatus.className = 'form-text text-muted';

        // Fetch branches
        fetch('ajax/get_branches.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear and populate source branch
                    sourceBranchSelect.innerHTML = '<option value="">Select Source Branch</option>';

                    if (data.branches && data.branches.length > 0) {
                        data.branches.forEach(branch => {
                            const option = document.createElement('option');
                            option.value = branch.name;
                            option.textContent = branch.name;
                            // Select current user's branch if available
                            if (branch.name === '<?php echo htmlspecialchars($_SESSION['branch_name'] ?? ''); ?>') {
                                option.selected = true;
                            }
                            sourceBranchSelect.appendChild(option);
                        });

                        sourceStatus.textContent = `${data.total_count} branch(es) available`;
                        sourceStatus.className = 'form-text text-success';

                        // If a branch is pre-selected, trigger change event
                        if (sourceBranchSelect.value) {
                            sourceBranchSelect.dispatchEvent(new Event('change'));
                        }
                    } else {
                        sourceBranchSelect.innerHTML = '<option value="">No branches available</option>';
                        sourceStatus.textContent = 'No branches available';
                        sourceStatus.className = 'form-text text-warning';
                    }

                    sourceBranchSelect.disabled = false;
                } else {
                    sourceBranchSelect.innerHTML = '<option value="">Error loading branches</option>';
                    sourceStatus.textContent = `Error: ${data.message || 'Failed to load branches'}`;
                    sourceStatus.className = 'form-text text-danger';
                    sourceBranchSelect.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error loading branches:', error);
                sourceBranchSelect.innerHTML = '<option value="">Error loading branches</option>';
                sourceStatus.textContent = 'Network error: Failed to load branches';
                sourceStatus.className = 'form-text text-danger';
                sourceBranchSelect.disabled = false;
            });
    }

    // --- Transfer Modal Logic ---
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const productSearch = document.getElementById('product-search');
        const productsTable = document.getElementById('products-table');
        const selectedProductsTable = document.getElementById('selected-products-table').querySelector('tbody');
        const noProductsSelected = document.getElementById('no-products-selected');
        const submitTransferBtn = document.getElementById('submit-transfer');
        const transferForm = document.getElementById('transferForm');
        const sourceBranchSelect = document.getElementById('source_branch');
        const destinationBranchSelect = document.getElementById('destination_branch');
        const createTransferModal = document.getElementById('createTransferModal');

        // Store selected products as {id, name, quantity, max}
        let selectedProducts = [];

        // Load branches when modal opens
        if (createTransferModal) {
            createTransferModal.addEventListener('shown.bs.modal', function() {
                loadBranchesForModal();
            });
        }

        // Helper: Render selected products table
        function renderSelectedProducts() {
            selectedProductsTable.innerHTML = '';
            if (selectedProducts.length === 0) {
                noProductsSelected.style.display = '';
                submitTransferBtn.disabled = true;
                return;
            }
            noProductsSelected.style.display = 'none';
            submitTransferBtn.disabled = false;
            selectedProducts.forEach((prod, idx) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${prod.name}<input type="hidden" name="products[]" value="${prod.id}"></td>
                    <td>
                        <input type="number" name="quantities[]" class="form-control form-control-sm selected-quantity" value="${prod.quantity}" min="1" max="${prod.max}" data-idx="${idx}">
                    </td>
                    <td><button type="button" class="btn btn-danger btn-sm remove-product-btn" data-idx="${idx}"><i class="fas fa-trash"></i></button></td>
                `;
                selectedProductsTable.appendChild(tr);
            });
        }

        // Add product button
        productsTable.addEventListener('click', function(e) {
            if (e.target.closest('.add-product-btn')) {
                const btn = e.target.closest('.add-product-btn');
                const id = btn.getAttribute('data-id');
                const name = btn.getAttribute('data-name');
                const max = parseInt(btn.getAttribute('data-max'));
                const qtyInput = btn.parentElement.querySelector('.product-quantity');
                const quantity = parseInt(qtyInput.value);
                if (!id || !name || isNaN(quantity) || quantity < 1) return;
                if (selectedProducts.some(p => p.id === id)) return; // Prevent duplicate
                selectedProducts.push({id, name, quantity, max});
                renderSelectedProducts();
            }
        });

        // Remove product button
        selectedProductsTable.addEventListener('click', function(e) {
            if (e.target.closest('.remove-product-btn')) {
                const idx = parseInt(e.target.closest('.remove-product-btn').getAttribute('data-idx'));
                selectedProducts.splice(idx, 1);
                renderSelectedProducts();
            }
        });

        // Update quantity in selected
        selectedProductsTable.addEventListener('input', function(e) {
            if (e.target.classList.contains('selected-quantity')) {
                const idx = parseInt(e.target.getAttribute('data-idx'));
                let val = parseInt(e.target.value);
                if (isNaN(val) || val < 1) val = 1;
                if (val > selectedProducts[idx].max) val = selectedProducts[idx].max;
                selectedProducts[idx].quantity = val;
                e.target.value = val;
            }
        });

        // Filter products by search
        if (productSearch) {
            productSearch.addEventListener('input', function() {
                const filter = productSearch.value.toLowerCase();
                Array.from(productsTable.querySelectorAll('tbody tr')).forEach(row => {
                    const name = row.children[0].textContent.toLowerCase();
                    row.style.display = name.includes(filter) ? '' : 'none';
                });
            });
        }

        // Change source branch: reload products and update destination branches
        if (sourceBranchSelect) {
            sourceBranchSelect.addEventListener('change', function() {
                const branch = this.value;
                const destStatus = document.getElementById('destination-branch-status');

                if (branch) {
                    // Load products for selected branch
                    fetch('transfers.php', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                        body: 'load_products=1&source_branch=' + encodeURIComponent(branch)
                    })
                    .then(res => res.text())
                    .then(html => {
                        productsTable.querySelector('tbody').innerHTML = html;
                    });

                    // Load destination branches (excluding source branch)
                    destinationBranchSelect.innerHTML = '<option value="">Loading branches...</option>';
                    destinationBranchSelect.disabled = true;
                    destStatus.textContent = 'Loading available destinations...';
                    destStatus.className = 'form-text text-muted';

                    fetch(`ajax/get_branches.php?current_branch=${encodeURIComponent(branch)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                destinationBranchSelect.innerHTML = '<option value="">Select Destination Branch</option>';

                                if (data.branches && data.branches.length > 0) {
                                    data.branches.forEach(destBranch => {
                                        const option = document.createElement('option');
                                        option.value = destBranch.name;
                                        option.textContent = destBranch.name;
                                        destinationBranchSelect.appendChild(option);
                                    });

                                    destStatus.textContent = `${data.total_count} destination(s) available`;
                                    destStatus.className = 'form-text text-success';
                                } else {
                                    destinationBranchSelect.innerHTML = '<option value="">No other branches available</option>';
                                    destStatus.textContent = 'No other branches available';
                                    destStatus.className = 'form-text text-warning';
                                }

                                destinationBranchSelect.disabled = false;
                            } else {
                                destinationBranchSelect.innerHTML = '<option value="">Error loading branches</option>';
                                destStatus.textContent = `Error: ${data.message || 'Failed to load branches'}`;
                                destStatus.className = 'form-text text-danger';
                                destinationBranchSelect.disabled = false;
                            }
                        })
                        .catch(error => {
                            console.error('Error loading destination branches:', error);
                            destinationBranchSelect.innerHTML = '<option value="">Error loading branches</option>';
                            destStatus.textContent = 'Network error: Failed to load branches';
                            destStatus.className = 'form-text text-danger';
                            destinationBranchSelect.disabled = false;
                        });
                } else {
                    // Clear destination branches if no source selected
                    destinationBranchSelect.innerHTML = '<option value="">Select Destination Branch</option>';
                    destinationBranchSelect.disabled = true;
                    destStatus.textContent = 'Select source branch first';
                    destStatus.className = 'form-text text-muted';
                }

                // Clear selected products
                selectedProducts = [];
                renderSelectedProducts();
            });
        }

        // On modal close, reset form
        if (createTransferModal) {
            createTransferModal.addEventListener('hidden.bs.modal', function() {
                selectedProducts = [];
                renderSelectedProducts();
                transferForm.reset();
                productsTable.querySelector('tbody').querySelectorAll('tr').forEach(row => row.style.display = '');
            });
        }
    });
</script>


