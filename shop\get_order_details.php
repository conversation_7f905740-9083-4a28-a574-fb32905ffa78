<?php
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Get order ID
$order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($order_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid order ID']);
    exit;
}

// Get order details
$order_query = "SELECT o.*, u.username 
                FROM orders o 
                LEFT JOIN users u ON o.created_by = u.id 
                WHERE o.id = ?";
$stmt = $conn->prepare($order_query);
$stmt->bind_param("i", $order_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Order not found']);
    $stmt->close();
    closeDB($conn);
    exit;
}

$order = $result->fetch_assoc();
$stmt->close();

// Get order items
$items_query = "SELECT oi.*, p.name as product_name, p.sku, c.name as category_name
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE oi.order_id = ?";
$stmt = $conn->prepare($items_query);
$stmt->bind_param("i", $order_id);
$stmt->execute();
$items_result = $stmt->get_result();
$items = [];
while ($item = $items_result->fetch_assoc()) {
    $items[] = $item;
}
$stmt->close();

// Close database connection
closeDB($conn);

// Return order details and items
echo json_encode([
    'success' => true,
    'order' => $order,
    'items' => $items
]);
?>
