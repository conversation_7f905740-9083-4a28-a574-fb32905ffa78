/* Print-specific styles */
@media print {
    /* Hide elements that shouldn't be printed */
    #sidebar,
    .btn,
    nav,
    .modal-header,
    .modal-footer,
    form,
    .pagination,
    .d-flex.justify-content-between.align-items-center.mb-4 .btn,
    .card-header button {
        display: none !important;
    }
    
    /* Hide Tuition Fees Section */
    .tuition-fees-table,
    h3:contains("Tuition"),
    div.mt-4.mb-2,
    div.d-flex.flex-wrap,
    .flex-grow-1.me-2,
    div.mt-3.mb-2,
    div.alert-success {
        display: none !important;
    }
    
    /* Hide Tuition Fees Section when printing */
    .tuition-fees-table,
    h3:contains("Tuition"),
    div.mt-4.mb-2,
    div.d-flex.flex-wrap,
    .flex-grow-1.me-2,
    div.alert-success,
    tr th:contains("Tuition"),
    tr td:contains("Tuition") {
        display: none !important;
    }

    /* Receipt-like styling */
    .company-name {
        font-size: 20pt !important;
        font-weight: bold !important;
        text-transform: uppercase !important;
        margin-bottom: 4px !important;
        letter-spacing: 1px !important;
    }

    .store-info {
        font-size: 10pt !important;
        margin-bottom: 2px !important;
        line-height: 1.2 !important;
    }

    .report-title {
        font-size: 14pt !important;
        font-weight: bold !important;
        text-transform: uppercase !important;
        margin: 12px 0 6px !important;
        letter-spacing: 0.5px !important;
    }

    .report-date {
        font-size: 10pt !important;
        font-style: normal !important;
        margin-bottom: 4px !important;
    }

    .branch-name {
        font-size: 11pt !important;
        font-weight: bold !important;
        margin-top: 4px !important;
        text-transform: uppercase !important;
    }
    
    /* Make the main content take full width */
    main {
        width: 100% !important;
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    /* Ensure the Sales Transactions section is displayed in fullscreen */
    .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    /* Uniform font styling for the entire report */
    body {
        font-family: Arial, Helvetica, sans-serif !important;
        font-size: 10pt !important;
        line-height: 1.3 !important;
        color: #000 !important;
    }
    
    /* Heading styles */
    h1, h2, h3, h4, h5, h6 {
        font-family: Arial, Helvetica, sans-serif !important;
        margin-top: 0 !important;
        page-break-after: avoid !important;
        text-align: center !important;
    }
    
    h1 { /* Musar Music Corporation */
        font-size: 18pt !important;
        font-weight: bold !important;
        margin-bottom: 4px !important;
        text-align: center !important;
    }
    
    h2 { /* Branch name */
        font-size: 16pt !important;
        font-weight: bold !important;
        margin-bottom: 4px !important;
    }
    
    h3 { /* Report Date */
        font-size: 14pt !important;
        font-weight: normal !important;
        margin-bottom: 12px !important;
    }
    
    h4 { /* Section Headers */
        font-size: 13pt !important;
        font-weight: bold !important;
        margin-top: 12px !important;
        margin-bottom: 4px !important;
        text-align: left !important;
    }
    
    h5 { /* Invoice Number */
        font-size: 12pt !important;
        font-weight: bold !important;
        margin-top: 8px !important;
        margin-bottom: 4px !important;
        text-align: left !important;
    }
    
    /* Customer details styling */
    .customer-details {
        font-size: 9pt !important;
        margin: 2px 0 !important;
        line-height: 1.4 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    
    /* Adjust table styles for better printing */
    .table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-bottom: 8px !important;
        border: 1px solid #000 !important;
    }
    
    .table th,
    .table td {
        padding: 4px 8px !important;
        border: 1px solid #000 !important;
        text-align: left !important;
        font-size: 9pt !important;
        line-height: 1.3 !important;
    }
    
    .table th {
        background-color: #f2f2f2 !important;
        font-weight: bold !important;
        text-transform: uppercase !important;
        font-size: 8.5pt !important;
    }

    .table td {
        vertical-align: middle !important;
    }

    /* Right align all amount columns */
    .table td:nth-child(2),
    .table td:nth-child(3),
    .table td:nth-child(4),
    td[data-amount],
    td[data-price],
    td[data-total],
    td[data-quantity],
    td[data-number],
    td.number,
    td.amount,
    td.price,
    td.quantity,
    .total-section,
    .payment-distribution-section td:last-child,
    .cash-fund-summary-section td:last-child,
    .expenses-section td:last-child,
    .numeric-cell {
        text-align: right !important;
    }
    
    /* Tuition Fees Table Styling */
    .tuition-fees-table {
        margin-top: 12px !important;
        margin-bottom: 8px !important;
        page-break-inside: avoid !important;
        border: 1px solid #000 !important;
        padding: 8px !important;
        background-color: #fff !important;
    }
    
    .tuition-fees-table th:nth-child(1)::before { content: "Student Name" !important; }
    .tuition-fees-table th:nth-child(2)::before { content: "Teacher" !important; }
    .tuition-fees-table th:nth-child(3)::before { content: "Lesson" !important; }
    .tuition-fees-table th:nth-child(4)::before { content: "Amount" !important; }
    .tuition-fees-table th:nth-child(5)::before { content: "OR Number" !important; }
    
    .tuition-fees-table th span { display: none !important; }
    
    /* Total sections styling */
    .total-section {
        font-weight: bold !important;
        background-color: #f9f9f9 !important;
        border-top: 2px solid #000 !important;
        padding: 6px 8px !important;
        margin-top: 8px !important;
    }
    
    /* Customer info styling */
    p {
        margin: 2px 0 !important;
        font-size: 9pt !important;
    }
    
    /* Ensure proper page breaks */
    .card {
        page-break-inside: avoid !important;
        margin-bottom: 10px !important;
        border: none !important;
    }
    
    /* Remove box shadows and borders for cleaner print */
    .card,
    .table-container {
        box-shadow: none !important;
        border: none !important;
    }
    
    /* Section headers styling - blue background with white text */
    h3, .card-header, thead, th {
        background-color: #1a4b8e !important;
        color: white !important;
        font-weight: bold !important;
        padding: 8px !important;
        text-transform: uppercase !important;
    }
    
    /* Alert styling for totals */
    .alert {
        padding: 6px 8px !important;
        margin-bottom: 8px !important;
        border: 1px solid #000 !important;
        background-color: #f9f9f9 !important;
        font-weight: bold !important;
        font-size: 9pt !important;
    }
    
    /* Right-align all numerical values */
    td:nth-child(2), td:nth-child(3), td:nth-child(4), td:nth-child(5),
    th:nth-child(2), th:nth-child(3), th:nth-child(4), th:nth-child(5) {
        text-align: right !important;
    }
    
    /* Invoice totals styling */
    .invoice-total {
        font-weight: bold !important;
        background-color: #f0f0f0 !important;
    }
    
    /* Cash transactions styling */
    .cash-transactions {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
    }
    
    .alert-primary {
        background-color: #f9f9f9 !important;
        border-style: dashed !important;
    }
    
    .alert-success {
        background-color: #f9f9f9 !important;
        border-width: 2px !important;
        font-size: 10pt !important;
    }
    
    /* Sales report content container */
    .sales-report-content {
        margin-top: 15px !important;
        padding: 0 10px !important;
    }

    /* Layout for Report Sections */
    .report-sections-wrapper {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px !important;
        margin: 20px 0 !important;
        width: 100% !important;
    }

    .expenses-section {
        grid-column: span 2 !important;
    }
    
    /* Main report sections styling */
    #printArea h3 {
        width: 100% !important;
        margin-top: 15px !important;
        margin-bottom: 5px !important;
        text-align: left !important;
    }
    
    /* RETAIL, SCHOOL FEES, EXPENSES section headers */
    #printArea h3 {
        background-color: #1a4b8e !important;
        color: white !important;
        padding: 8px !important;
        font-size: 14pt !important;
        text-transform: uppercase !important;
    }

    /* Combined Payment Distribution and Cash Fund Summary */
    .payment-distribution-section,
    .cash-fund-summary-section {
        border: 1px solid #000 !important;
        min-width: 0 !important;
        width: 100% !important;
        break-inside: avoid !important;
    }
    
    /* Financial Summary and School Fees side-by-side layout */
    .flex-container {
        display: flex !important;
        justify-content: space-between !important;
        width: 100% !important;
    }
    
    /* Invoice grouping styling */
    .invoice-group {
        margin-bottom: 15px !important;
        border-bottom: 1px solid #ddd !important;
        padding-bottom: 10px !important;
    }

    .payment-distribution-section table,
    .cash-fund-summary-section table {
        margin-bottom: 0 !important;
        border: none !important;
    }

    .payment-distribution-section th,
    .cash-fund-summary-section th {
        background-color: #fff !important;
        font-weight: bold !important;
        text-transform: none !important;
        font-size: 9pt !important;
        border-bottom: 1px solid #000 !important;
    }

    .payment-distribution-section td,
    .cash-fund-summary-section td {
        font-size: 9pt !important;
        padding: 4px 8px !important;
    }

    .payment-distribution-section td:not(:first-child),
    .cash-fund-summary-section td:last-child {
        text-align: right !important;
    }

    .payment-distribution-section tr:last-child,
    .cash-fund-summary-section tr:last-child {
        font-weight: bold !important;
    }

    .payment-distribution-section h4,
    .cash-fund-summary-section h4 {
        font-size: 11pt !important;
        margin: 0 !important;
        padding: 8px !important;
        background-color: #f2f2f2 !important;
        border-bottom: 1px solid #000 !important;
    }

    /* Print area container */
    #printArea {
        max-width: 800px !important;
        margin: 0 auto !important;
        background-color: #fff !important;
    }
    
    /* Set page margins */
    @page {
        margin: 1cm !important;
    }
}

.alert-primary,
.alert-success,
.alert .total,
.total-row td,
.total-amount,
.grand-total,
.subtotal,
.total-value,
.summary-total,
.payment-distribution-section tr:last-child td,
.cash-fund-summary-section tr:last-child td,
.payment-distribution-section td:not(:first-child),
.cash-fund-summary-section td:last-child {
    text-align: right !important;
}

.total-label {
    text-align: left !important;
    font-weight: bold !important;
}