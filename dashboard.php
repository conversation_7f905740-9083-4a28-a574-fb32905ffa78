<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Get today's date
$today = date('Y-m-d');

// Get today's sales total
$sales_query = "SELECT COUNT(*) as order_count, SUM(total_amount) as total_sales 
               FROM orders 
               WHERE DATE(created_at) = ? AND branch_name = ?";
$stmt = $conn->prepare($sales_query);
$stmt->bind_param('ss', $today, $branch_name);
$stmt->execute();
$sales_result = $stmt->get_result()->fetch_assoc();

// Get inventory statistics
$inventory_query = "SELECT 
    COUNT(*) as total_products,
    SUM(CASE WHEN quantity <= reorder_level THEN 1 ELSE 0 END) as low_stock,
    SUM(CASE WHEN quantity = 0 THEN 1 ELSE 0 END) as out_of_stock,
    SUM(quantity * unit_price) as total_value
FROM products
WHERE branch_name = ?";
$stmt = $conn->prepare($inventory_query);
$stmt->bind_param('s', $branch_name);
$stmt->execute();
$inventory_result = $stmt->get_result()->fetch_assoc();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Today's Sales</p>
                                <h5 class="font-weight-bolder" id="totalSales">
                                    <?php echo number_format($sales_result['total_sales'], 2); ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow-primary text-center rounded-circle">
                                <i class="fas fa-shopping-cart opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Total Products</p>
                                <h5 class="font-weight-bolder" id="totalProducts">
                                    <?php echo $inventory_result['total_products']; ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow-success text-center rounded-circle">
                                <i class="fas fa-box opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Low Stock Items</p>
                                <h5 class="font-weight-bolder text-warning" id="lowStockItems">
                                    <?php echo $inventory_result['low_stock']; ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow-warning text-center rounded-circle">
                                <i class="fas fa-exclamation-triangle opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Out of Stock</p>
                                <h5 class="font-weight-bolder text-danger" id="outOfStockItems">
                                    <?php echo $inventory_result['out_of_stock']; ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-danger shadow-danger text-center rounded-circle">
                                <i class="fas fa-times opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Sales Overview</h6>
                </div>
                <div class="card-body p-3">
                    <div class="chart">
                        <canvas id="salesChart" class="chart-canvas" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Recent Activities</h6>
                </div>
                <div class="card-body p-3">
                    <div id="recentActivities" class="timeline timeline-one-side">
                        <!-- Activities will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Low Stock Alert</h6>
                </div>
                <div class="card-body p-3">
                    <div id="lowStockList" class="table-responsive">
                        <!-- Low stock items will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Include dashboard.js -->
<script src="assets/js/dashboard.js"></script>

<?php require_once 'includes/footer.php'; ?>