<?php
require_once 'config/database.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Order ID is required']);
    exit;
}

$order_id = $_GET['id'];

try {
    // Get order details
    $order_query = "SELECT o.*, u.username 
                    FROM orders o 
                    LEFT JOIN users u ON o.user_id = u.id 
                    WHERE o.id = ?";
    $stmt = $conn->prepare($order_query);
    $stmt->bind_param('i', $order_id);
    $stmt->execute();
    $order_result = $stmt->get_result();
    
    if ($order_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Order not found']);
        exit;
    }
    
    $order = $order_result->fetch_assoc();
    
    // Get order items
    $items_query = "SELECT oi.*, p.name as product_name, p.sku, c.name as category_name 
                    FROM order_items oi 
                    LEFT JOIN products p ON oi.product_id = p.id 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    WHERE oi.order_id = ?";
    $stmt = $conn->prepare($items_query);
    $stmt->bind_param('i', $order_id);
    $stmt->execute();
    $items_result = $stmt->get_result();
    
    $items = [];
    while ($item = $items_result->fetch_assoc()) {
        $items[] = $item;
    }
    
    echo json_encode([
        'success' => true, 
        'order' => $order, 
        'items' => $items
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}