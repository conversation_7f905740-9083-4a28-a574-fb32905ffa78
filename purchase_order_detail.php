<?php
require_once 'includes/header.php';
require_once 'config/database.php';
require_once 'includes/mail_functions.php';  // Add this line

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Check if PO number is provided
if (!isset($_GET['po_number'])) {
    header('Location: purchase_order.php');
    exit();
}

$conn = connectDB();
$po_number = $conn->real_escape_string($_GET['po_number']);

// Get purchase order header information
$query = "SELECT 
            po.po_number,
            s.name AS supplier_name,
            po.created_at,
            po.expected_delivery_date,
            po.ordering_branch,
            po.status,
            po.delivery_address,
            po.notes
        FROM 
            u476900858_inventor.purchase_orders AS po
        INNER JOIN 
            u476900858_inventor.suppliers AS s
            ON po.supplier_id = s.id
        WHERE 
            po.po_number = '$po_number'
        LIMIT 1";

$result = $conn->query($query);

if (!$result || $result->num_rows === 0) {
    echo "<div class='alert alert-danger'>Purchase order not found.</div>";
    exit();
}

$po = $result->fetch_assoc();

// Handle status update if form submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = $conn->real_escape_string($_POST['status']);
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Update PO status
        $update_query = "UPDATE u476900858_inventor.purchase_orders 
                        SET status = '$new_status' 
                        WHERE po_number = '$po_number'";
        
        if (!$conn->query($update_query)) {
            throw new Exception("Failed to update PO status");
        }

        // If status is complete, update product quantities
        if ($new_status === 'complete') {
            // Get all items for this PO
            $items_query = "SELECT sku, quantity FROM u476900858_inventor.purchase_orders 
                          WHERE po_number = '$po_number'";
            $items_result = $conn->query($items_query);
            
            if (!$items_result) {
                throw new Exception("Failed to fetch PO items");
            }

            while ($item = $items_result->fetch_assoc()) {
                // Update product quantity
                $update_product = "UPDATE u476900858_inventor.products 
                                 SET quantity = quantity + {$item['quantity']}
                                 WHERE sku = '{$item['sku']}'";
                if (!$conn->query($update_product)) {
                    throw new Exception("Failed to update product quantity for SKU: {$item['sku']}");
                }
            }
        }
        
        // If everything is successful, commit the transaction
        $conn->commit();
        $po['status'] = $new_status;
        
        // Show success message
        echo "<div class='alert alert-success'>Order status updated successfully" . 
             ($new_status === 'complete' ? " and product quantities have been updated" : "") . 
             ".</div>";
        
    } catch (Exception $e) {
        // Rollback on error
        $conn->rollback();
        echo "<div class='alert alert-danger'>Error updating order: " . $e->getMessage() . "</div>";
    }
}

// Get purchase order items
$items_query = "SELECT 
                    po.*,
                    p.name AS product_name,
                    p.sku
                FROM 
                    u476900858_inventor.purchase_orders po
                INNER JOIN 
                    u476900858_inventor.products p
                    ON po.sku = p.sku
                WHERE 
                    po.po_number = '$po_number'";

$items_result = $conn->query($items_query);

if (!$items_result) {
    echo "<div class='alert alert-danger'>Error fetching order items: " . $conn->error . "</div>";
    exit();
}
?>

<!-- Add Bootstrap Icons and custom styles -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.125);
    padding: 1rem;
}

.badge {
    font-size: 0.875rem;
    padding: 0.5em 0.75em;
}

.table th {
    background-color: #f8f9fa;
}

.info-box {
    background-color: #fff;
    padding: 1.25rem;
    border-radius: 0.375rem;
    height: 100%;
    border: 1px solid rgba(0,0,0,.125);
}

.print-header {
    display: none;
}

@media print {
    .no-print {
        display: none !important;
    }
    .print-header {
        display: block;
        text-align: center;
        margin-bottom: 20px;
    }
    .card {
        box-shadow: none;
        border: none;
    }
    .badge {
        border: 1px solid #000;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- Print Header -->
    <div class="print-header">
        <h3>Purchase Order Details</h3>
        <p>PO Number: <?php echo htmlspecialchars($po_number); ?></p>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Purchase Order Details - <?php echo htmlspecialchars($po_number); ?></h5>
                    <div class="no-print">
                        <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#emailModal">
                            <i class="bi bi-envelope"></i> Send Email
                        </button>
                        <button onclick="window.print()" class="btn btn-secondary me-2">
                            <i class="bi bi-printer"></i> Print Preview
                        </button>
                        <a href="purchase_order.php" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-box">
                                <h6 class="text-primary mb-3">Supplier Information</h6>
                                <p class="mb-2"><i class="bi bi-building me-2"></i><strong>Name:</strong> <?php echo htmlspecialchars($po['supplier_name']); ?></p>
                                <p class="mb-2"><i class="bi bi-shop me-2"></i><strong>Branch:</strong> <?php echo htmlspecialchars($po['ordering_branch']); ?></p>
                                <p class="mb-2"><i class="bi bi-geo-alt me-2"></i><strong>Delivery Address:</strong> <?php echo nl2br(htmlspecialchars($po['delivery_address'])); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <h6 class="text-primary mb-3">Order Information</h6>
                                <p class="mb-2"><i class="bi bi-calendar-event me-2"></i><strong>Date Created:</strong> <?php echo date('M d, Y', strtotime($po['created_at'])); ?></p>
                                <p class="mb-2"><i class="bi bi-calendar-check me-2"></i><strong>Expected Delivery:</strong> <?php echo date('M d, Y', strtotime($po['expected_delivery_date'])); ?></p>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-flag me-2"></i><strong>Status:</strong>
                                    <div class="ms-2 d-flex align-items-center">
                                        <span class="badge bg-<?php echo getStatusBadgeClass($po['status']); ?> me-2">
                                            <?php echo ucfirst($po['status']); ?>
                                        </span>
                                        <form method="POST" class="d-inline-block no-print">
                                            <select name="status" class="form-select form-select-sm" style="width: 140px" onchange="this.form.submit()">
                                                <option value="pending" <?php echo $po['status'] == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                <option value="complete" <?php echo $po['status'] == 'complete' ? 'selected' : ''; ?>>Complete</option>
                                                <option value="cancelled" <?php echo $po['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th class="text-end">Quantity</th>
                                    <th class="text-end">Unit Price</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $grand_total = 0;
                                if ($items_result->num_rows > 0) {
                                    while ($item = $items_result->fetch_assoc()): 
                                        $total = $item['quantity'] * $item['unit_price'];
                                        $grand_total += $total;
                                ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['sku']); ?></td>
                                    <td class="text-end"><?php echo number_format($item['quantity']); ?></td>
                                    <td class="text-end">₱<?php echo number_format($item['unit_price'], 2); ?></td>
                                    <td class="text-end">₱<?php echo number_format($total, 2); ?></td>
                                </tr>
                                <?php 
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="5" class="text-center">No items found for this purchase order</td></tr>';
                                }
                                ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>Grand Total:</strong></td>
                                    <td class="text-end"><strong>₱<?php echo number_format($grand_total, 2); ?></strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <?php if (!empty($po['notes'])): ?>
                    <div class="mt-4">
                        <div class="info-box">
                            <h6 class="text-primary mb-3"><i class="bi bi-journal-text me-2"></i>Notes</h6>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($po['notes'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'draft': return 'secondary';
        case 'pending': return 'warning';
        case 'approved': return 'success';
        case 'sent': return 'info';
        case 'received': return 'primary';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

closeDB($conn);
require_once 'includes/footer.php';
?>

<!-- Email Modal -->
<div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">Send Purchase Order Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="emailForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="email_to" class="form-label">To:</label>
                        <input type="email" class="form-control" id="email_to" name="email_to" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_subject" class="form-label">Subject:</label>
                        <input type="text" class="form-control" id="email_subject" name="email_subject" 
                               value="Purchase Order #<?php echo htmlspecialchars($po_number); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_message" class="form-label">Message:</label>
                        <textarea class="form-control" id="email_message" name="email_message" rows="4" required>Dear <?php echo htmlspecialchars($po['supplier_name']); ?>,

Please find the details of our Purchase Order <?php echo htmlspecialchars($po_number); ?>.

Order Details:
- PO Number: <?php echo htmlspecialchars($po_number); ?>
- Order Date: <?php echo date('M d, Y', strtotime($po['created_at'])); ?>
- Expected Delivery: <?php echo date('M d, Y', strtotime($po['expected_delivery_date'])); ?>
- Delivery Address: <?php echo htmlspecialchars($po['delivery_address']); ?>

Total Amount: ₱<?php echo number_format($grand_total, 2); ?>

Please confirm receipt of this purchase order.

Best regards,
<?php echo htmlspecialchars($po['ordering_branch']); ?></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" id="sendEmailBtn" class="btn btn-primary">
                        <i class="bi bi-send"></i> Send Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status change handling
    const statusSelect = document.querySelector('select[name="status"]');
    const statusForm = statusSelect.closest('form');

    statusSelect.addEventListener('change', function(e) {
        if (this.value === 'complete') {
            if (!confirm('Are you sure you want to mark this PO as complete?\nThis will update the product quantities in inventory.')) {
                e.preventDefault();
                this.value = '<?php echo $po['status']; ?>';
                return false;
            }
        }
        statusForm.submit();
    });

    // Email form handling
    const sendEmailBtn = document.getElementById('sendEmailBtn');
    const emailForm = document.getElementById('emailForm');

    sendEmailBtn.addEventListener('click', function() {
        const formData = new FormData(emailForm);
        
        // Show loading state
        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';

        fetch('send_po_email.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email sent successfully!');
                const modal = bootstrap.Modal.getInstance(document.getElementById('emailModal'));
                modal.hide();
            } else {
                alert('Failed to send email: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error sending email: ' + error.message);
        })
        .finally(() => {
            // Reset button state
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-send"></i> Send Email';
        });
    });

    // Print preview functionality
    window.addEventListener('beforeprint', function() {
        document.title = 'Purchase Order - ' + <?php echo json_encode($po_number); ?>;
    });
});
</script>