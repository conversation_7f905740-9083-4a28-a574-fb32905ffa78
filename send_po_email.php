<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get form data
$to = filter_input(INPUT_POST, 'email_to', FILTER_SANITIZE_EMAIL);
$subject = filter_input(INPUT_POST, 'email_subject', FILTER_SANITIZE_STRING);
$message = filter_input(INPUT_POST, 'email_message', FILTER_SANITIZE_STRING);

// Validate email
if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid email address']);
    exit();
}

// Set email headers
$headers = "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: text/html; charset=UTF-8\r\n";
$headers .= "From: " . COMPANY_EMAIL . "\r\n";
$headers .= "Reply-To: " . COMPANY_EMAIL . "\r\n";
$headers .= "X-Mailer: PHP/" . phpversion();

// Convert message to HTML format
$htmlMessage = nl2br($message);

try {
    // Send email
    if (mail($to, $subject, $htmlMessage, $headers)) {
        // Log email sent in database if needed
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Email sent successfully']);
    } else {
        throw new Exception('Failed to send email');
    }
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>