<?php
require_once 'config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Get database connection
$conn = connectDB();

// Initialize response array
$response = [
    'success' => true,
    'payment_groups' => [],
    'tuition_payment_groups' => [],
    'expenses' => [],
    'summary' => [],
    'grand_total' => 0,
    'tuition_total' => 0,
    'expenses_total' => 0,
    'cash_fund' => 0
];

// Get parameters
$branch_name = isset($_GET['branch_name']) ? $_GET['branch_name'] : (isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch');
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$payment_filter = isset($_GET['payment']) ? $_GET['payment'] : '';

// Build base query for fetching orders
$query = "SELECT o.id, o.order_number, o.invoice_number, o.customer_name, o.customer_email, o.customer_phone, o.payment_type, 
          o.total_amount, o.created_at, o.status 
          FROM orders o 
          WHERE o.branch_name = ? AND o.status != 'cancelled'";

// Add date range condition if provided
if (!empty($date_from)) {
    $query .= " AND DATE(o.created_at) >= ?";
}

if (!empty($date_to)) {
    $query .= " AND DATE(o.created_at) <= ?";
}

// Add payment type filter if provided
if (!empty($payment_filter)) {
    $query .= " AND o.payment_type = ?";
}

// Order by payment type and invoice number
$query .= " ORDER BY o.payment_type ASC, o.invoice_number ASC, o.created_at ASC";

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameters
$bind_types = "s"; // branch_name
$bind_params = array(&$bind_types, &$branch_name);

if (!empty($date_from)) {
    $bind_types .= "s"; // date_from
    $bind_params[] = &$date_from;
}

if (!empty($date_to)) {
    $bind_types .= "s"; // date_to
    $bind_params[] = &$date_to;
}

if (!empty($payment_filter)) {
    $bind_types .= "s"; // payment_type
    $bind_params[] = &$payment_filter;
}

// Call bind_param with dynamic parameters
if (!call_user_func_array(array($stmt, 'bind_param'), $bind_params)) {
    echo json_encode(['success' => false, 'message' => 'Failed to bind parameters: ' . $stmt->error]);
    closeDB($conn);
    exit();
}

// Execute query
if (!$stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Failed to execute query: ' . $stmt->error]);
    closeDB($conn);
    exit();
}
$result = $stmt->get_result();

// Group orders by payment type
$payment_groups = [];
$tuition_payment_groups = [];
$grand_total = 0;
$tuition_total = 0;

while ($order = $result->fetch_assoc()) {
    $payment_type = $order['payment_type'];
    $order_id = $order['id'];
    
    // Get order items excluding 'Tuition' products
    $items_query = "SELECT oi.*, p.name as product_name, p.sku, c.name as category_name, oi.unit_price as actual_price 
                   FROM order_items oi 
                   LEFT JOIN products p ON oi.product_id = p.id 
                   LEFT JOIN categories c ON p.category_id = c.id 
                   WHERE oi.order_id = ? AND p.name NOT LIKE '%Tuition%'";
    
    $items_stmt = $conn->prepare($items_query);
    $items_stmt->bind_param("i", $order_id);
    if (!$items_stmt->execute()) {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch order items: ' . $items_stmt->error]);
        closeDB($conn);
        exit();
    }
    $items_result = $items_stmt->get_result();
    
    $items = [];
    while ($item = $items_result->fetch_assoc()) {
        $items[] = $item;
    }
    $items_stmt->close();
    
    // Get Tuition items separately
    $tuition_query = "SELECT oi.*, p.name as product_name, p.sku, c.name as category_name, oi.unit_price as actual_price 
                    FROM order_items oi 
                    LEFT JOIN products p ON oi.product_id = p.id 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    WHERE oi.order_id = ? AND p.name LIKE '%Tuition%'";
    
    $tuition_stmt = $conn->prepare($tuition_query);
    $tuition_stmt->bind_param("i", $order_id);
    if (!$tuition_stmt->execute()) {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch tuition items: ' . $tuition_stmt->error]);
        closeDB($conn);
        exit();
    }
    $tuition_result = $tuition_stmt->get_result();
    
    $tuition_items = [];
    $tuition_amount = 0;
    while ($item = $tuition_result->fetch_assoc()) {
        $tuition_items[] = $item;
        $tuition_amount += floatval($item['total_price']);
    }
    $tuition_stmt->close();
    
    // Process regular items
    if (count($items) > 0) {
        // Add order to payment group
        if (!isset($payment_groups[$payment_type])) {
            $payment_groups[$payment_type] = [
                'payment_type' => $payment_type,
                'invoices' => []
            ];
        }
        
        $order['items'] = $items;
        $payment_groups[$payment_type]['invoices'][] = $order;
        
        // Add to grand total
        $grand_total += floatval($order['total_amount']) - $tuition_amount;
    }
    
    // Process tuition items
    if (count($tuition_items) > 0) {
        // Add order to tuition payment group
        if (!isset($tuition_payment_groups[$payment_type])) {
            $tuition_payment_groups[$payment_type] = [
                'payment_type' => $payment_type,
                'invoices' => []
            ];
        }
        
        // Get customer details
        $customer_query = "SELECT customer_name, customer_email, customer_phone FROM orders WHERE id = ?";
        $customer_stmt = $conn->prepare($customer_query);
        $customer_stmt->bind_param("i", $order_id);
        if (!$customer_stmt->execute()) {
            echo json_encode(['success' => false, 'message' => 'Failed to fetch customer details: ' . $customer_stmt->error]);
            closeDB($conn);
            exit();
        }
        $customer_result = $customer_stmt->get_result();
        $customer_data = $customer_result->fetch_assoc();
        $customer_stmt->close();
        
        $tuition_order = [
            'id' => $order['id'],
            'order_number' => $order['order_number'],
            'invoice_number' => $order['invoice_number'],
            'customer_name' => $customer_data['customer_name'],
            'customer_email' => $customer_data['customer_email'] ?? 'N/A',
            'customer_phone' => $customer_data['customer_phone'] ?? 'N/A',
            'payment_type' => $order['payment_type'],
            'created_at' => $order['created_at'],
            'items' => $tuition_items
        ];
        
        $tuition_payment_groups[$payment_type]['invoices'][] = $tuition_order;
        
        // Add to tuition total
        $tuition_total += $tuition_amount;
    }
}

// Convert payment groups to indexed array for JSON
$response['payment_groups'] = array_values($payment_groups);
$response['tuition_payment_groups'] = array_values($tuition_payment_groups);
$response['grand_total'] = $grand_total;
$response['tuition_total'] = $tuition_total;

// Get expenses for the selected date range and branch
$expenses_query = "SELECT e.*, u.username 
                 FROM expenses e 
                 LEFT JOIN users u ON e.created_by = u.id 
                 WHERE e.branch_name = ? AND e.category != 'Cash Fund'";

// Add date range condition if provided
if (!empty($date_from)) {
    $expenses_query .= " AND DATE(e.expense_date) >= ?";
}

if (!empty($date_to)) {
    $expenses_query .= " AND DATE(e.expense_date) <= ?";
}

$expenses_query .= " ORDER BY e.expense_date ASC, e.id ASC";

// Prepare statement for expenses
$expenses_stmt = $conn->prepare($expenses_query);

// Bind parameters for expenses query
$exp_bind_types = "s"; // branch_name
$exp_bind_params = array(&$exp_bind_types, &$branch_name);

if (!empty($date_from)) {
    $exp_bind_types .= "s"; // date_from
    $exp_bind_params[] = &$date_from;
}

if (!empty($date_to)) {
    $exp_bind_types .= "s"; // date_to
    $exp_bind_params[] = &$date_to;
}

// Call bind_param with dynamic parameters
if (!call_user_func_array(array($expenses_stmt, 'bind_param'), $exp_bind_params)) {
    echo json_encode(['success' => false, 'message' => 'Failed to bind parameters for expenses query: ' . $expenses_stmt->error]);
    closeDB($conn);
    exit();
}

// Execute expenses query
if (!$expenses_stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Failed to execute expenses query: ' . $expenses_stmt->error]);
    closeDB($conn);
    exit();
}
$expenses_result = $expenses_stmt->get_result();

// Process expenses
$expenses = [];
$expenses_total = 0;

while ($expense = $expenses_result->fetch_assoc()) {
    $expenses[] = $expense;
    $expenses_total += floatval($expense['amount']);
}

$expenses_stmt->close();
$response['expenses'] = $expenses;
$response['expenses_total'] = $expenses_total;

// Get latest cash fund
$cash_fund_query = "SELECT * FROM cash_funds WHERE branch_name = ? ORDER BY fund_date DESC, id DESC LIMIT 1";
$cash_fund_stmt = $conn->prepare($cash_fund_query);
$cash_fund_stmt->bind_param("s", $branch_name);
$cash_fund_stmt->execute();
$cash_fund_result = $cash_fund_stmt->get_result();
$latest_cash_fund = $cash_fund_result->fetch_assoc();
$cash_fund_stmt->close();

$cash_fund = $latest_cash_fund ? floatval($latest_cash_fund['amount']) : 0;
$response['cash_fund'] = $cash_fund;

// Calculate current cash fund (cash fund + sales - expenses)
$current_cash_fund = $cash_fund + $grand_total + $tuition_total - $expenses_total;
$response['current_cash_fund'] = $current_cash_fund;

// Create summary data for payment type distribution
$summary = [];

// Add regular sales by payment type
foreach ($payment_groups as $payment_group) {
    $payment_type = $payment_group['payment_type'];
    $payment_total = 0;
    
    foreach ($payment_group['invoices'] as $invoice) {
        $payment_total += floatval($invoice['total_amount']);
    }
    
    if (!isset($summary[$payment_type])) {
        $summary[$payment_type] = [
            'payment_type' => $payment_type,
            'sales_total' => 0,
            'tuition_total' => 0
        ];
    }
    
    $summary[$payment_type]['sales_total'] = $payment_total;
}

// Add tuition sales by payment type
foreach ($tuition_payment_groups as $payment_group) {
    $payment_type = $payment_group['payment_type'];
    $payment_total = 0;
    
    foreach ($payment_group['invoices'] as $invoice) {
        foreach ($invoice['items'] as $item) {
            $payment_total += floatval($item['total_price']);
        }
    }
    
    if (!isset($summary[$payment_type])) {
        $summary[$payment_type] = [
            'payment_type' => $payment_type,
            'sales_total' => 0,
            'tuition_total' => 0
        ];
    }
    
    $summary[$payment_type]['tuition_total'] = $payment_total;
}

$response['summary'] = array_values($summary);

// Close statement and connection
$stmt->close();
closeDB($conn);

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);