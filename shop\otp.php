<?php
session_start();
require_once '../config/database.php';
require_once '../vendor/autoload.php'; // Make sure to install Twilio via Composer

use Twilio\Rest\Client;

class OTPHandler {
    private $twilio;
    private $conn;
    private $from_number;

    public function __construct() {
        // Initialize Twilio client
        $account_sid = 'YOUR_TWILIO_ACCOUNT_SID';
        $auth_token = 'YOUR_TWILIO_AUTH_TOKEN';
        $this->from_number = 'YOUR_TWILIO_PHONE_NUMBER';
        
        $this->twilio = new Client($account_sid, $auth_token);
        $this->conn = connectDB();
    }

    public function generateOTP() {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    public function sendOTP($phone_number) {
        try {
            // Generate OTP
            $otp = $this->generateOTP();
            
            // Store OTP in database with expiration
            $expiry = date('Y-m-d H:i:s', strtotime('+5 minutes'));
            $stmt = $this->conn->prepare("INSERT INTO otp_codes (phone_number, otp_code, expiry) VALUES (?, ?, ?)");
            $stmt->bind_param("sss", $phone_number, $otp, $expiry);
            $stmt->execute();

            // Send OTP via Twilio
            $message = $this->twilio->messages->create(
                $phone_number,
                [
                    'from' => $this->from_number,
                    'body' => "Your OTP code is: $otp. This code will expire in 5 minutes."
                ]
            );

            return [
                'success' => true,
                'message' => 'OTP sent successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send OTP: ' . $e->getMessage()
            ];
        }
    }

    public function verifyOTP($phone_number, $otp) {
        try {
            // Check if OTP exists and is valid
            $stmt = $this->conn->prepare("
                SELECT * FROM otp_codes 
                WHERE phone_number = ? 
                AND otp_code = ? 
                AND expiry > NOW() 
                AND is_used = 0 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->bind_param("ss", $phone_number, $otp);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                // Mark OTP as used
                $stmt = $this->conn->prepare("UPDATE otp_codes SET is_used = 1 WHERE phone_number = ? AND otp_code = ?");
                $stmt->bind_param("ss", $phone_number, $otp);
                $stmt->execute();

                return [
                    'success' => true,
                    'message' => 'OTP verified successfully'
                ];
            }

            return [
                'success' => false,
                'message' => 'Invalid or expired OTP'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error verifying OTP: ' . $e->getMessage()
            ];
        }
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otpHandler = new OTPHandler();
    $response = ['success' => false, 'message' => 'Invalid request'];

    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'send':
                if (isset($_POST['phone_number'])) {
                    $response = $otpHandler->sendOTP($_POST['phone_number']);
                }
                break;

            case 'verify':
                if (isset($_POST['phone_number']) && isset($_POST['otp'])) {
                    $response = $otpHandler->verifyOTP($_POST['phone_number'], $_POST['otp']);
                }
                break;
        }
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
?> 