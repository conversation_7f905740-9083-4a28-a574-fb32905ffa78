<?php
session_start();
require_once '../config/database.php';

// Get available shipping couriers
$conn = connectDB();
$shipping_query = "SELECT * FROM shipping ORDER BY courier_name";
$shipping_result = $conn->query($shipping_query);
$shipping_options = $shipping_result->fetch_all(MYSQLI_ASSOC);
closeDB($conn);

// Check if cart is empty
if (!isset($_SESSION['shop_cart']) || count($_SESSION['shop_cart']) === 0) {
    header('Location: cart.php');
    exit();
}

// Initialize variables
$errors = [];
$success = false;
$order_id = null;

// Calculate cart total
$cart_total = 0;
$cart_weight = 0; // Calculate total weight for shipping
foreach ($_SESSION['shop_cart'] as $item) {
    $cart_total += $item['total'];
    $cart_weight += 1; // Assuming 1kg per item, adjust as needed
}

// Calculate shipping cost if courier is selected
$shipping_cost = 0;
if (isset($_POST['shipping_courier'])) {
    foreach ($shipping_options as $option) {
        if ($option['id'] == $_POST['shipping_courier']) {
            $shipping_cost = $option['base_rate'] + ($option['rate_per_kg'] * $cart_weight);
            break;
        }
    }
}

// Add shipping cost to total
$total_with_shipping = $cart_total + $shipping_cost;

// Process checkout form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $customer_name = trim($_POST['customer_name']);
    $customer_email = trim($_POST['customer_email']);
    $customer_phone = trim($_POST['customer_phone']);
    $address = trim($_POST['address']);
    $payment_type = $_POST['payment_type'];
$shipping_courier_id = $_POST['shipping_courier'];
    
    // Validate form data
    if (empty($customer_name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($customer_email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($customer_phone)) {
        $errors[] = "Phone number is required";
    }
    
    if (empty($address)) {
        $errors[] = "Address is required";
    }
    
    // If no errors, process the order
    if (empty($errors)) {
        // Get database connection
        $conn = connectDB();
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Generate order number
            $order_number = 'ONL-' . date('Ymd') . '-' . rand(1000, 9999);
            
            // Insert order
            // Get shipping courier details
            $shipping_details = null;
            foreach ($shipping_options as $option) {
                if ($option['id'] == $shipping_courier_id) {
                    $shipping_details = $option;
                    break;
                }
            }
            
            $order_query = "INSERT INTO orders (order_number, customer_name, customer_email, customer_phone, total_amount, status, payment_type, branch_name, notes, shipping_courier, shipping_weight, shipping_cost) 
                          VALUES (?, ?, ?, ?, ?, 'pending', ?, 'Online Store', ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($order_query);
            
            // Check if prepare was successful
            if ($stmt === false) {
                // Log the error and throw an exception
                $error = "Error preparing order query: " . $conn->error;
                error_log($error);
                throw new Exception($error);
            }
            
            $stmt->bind_param("ssssdsssdd", $order_number, $customer_name, $customer_email, $customer_phone, $total_with_shipping, $payment_type, $address, $shipping_details['courier_name'], $cart_weight, $shipping_cost);
            $stmt->execute();
            
            // Get the order ID
            $order_id = $conn->insert_id;
            
            // Insert order items
            $item_query = "INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($item_query);
            
            // Check if prepare was successful
            if ($stmt === false) {
                // Log the error and throw an exception
                $error = "Error preparing order items query: " . $conn->error;
                error_log($error);
                throw new Exception($error);
            }
            
            foreach ($_SESSION['shop_cart'] as $item) {
                $product_id = $item['id'];
                $quantity = $item['quantity'];
                $unit_price = $item['price'];
                $total_price = $item['total'];
                
                $stmt->bind_param("iiddd", $order_id, $product_id, $quantity, $unit_price, $total_price);
                $stmt->execute();
                
                // Update product quantity
                $update_query = "UPDATE products SET quantity = quantity - ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_query);
                
                // Check if prepare was successful
                if ($update_stmt === false) {
                    // Log the error and throw an exception
                    $error = "Error preparing update query: " . $conn->error;
                    error_log($error);
                    throw new Exception($error);
                }
                
                $update_stmt->bind_param("ii", $quantity, $product_id);
                $update_stmt->execute();
            }
            
            // Commit transaction
            $conn->commit();
            
            // Clear cart
            $_SESSION['shop_cart'] = [];
            
            // Set success flag
            $success = true;
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errors[] = "Error processing order: " . $e->getMessage();
        }
        
        // Close connection
        closeDB($conn);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $success ? 'Order Confirmation' : 'Checkout'; ?> - Online Store</title>
    <link rel="icon" href="../franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Reduced font sizes for shop pages */
        body {
            font-size: 14px;
        }
        
        h1, .h1 { font-size: 1.8rem; }
        h2, .h2 { font-size: 1.5rem; }
        h3, .h3 { font-size: 1.3rem; }
        h4, .h4 { font-size: 1.1rem; }
        h5, .h5 { font-size: 1rem; }
        h6, .h6 { font-size: 0.9rem; }
        
        .card-title {
            font-size: 1.1rem;
        }
        
        .card-text {
            font-size: 0.9rem;
        }
        
        .product-title {
            font-size: 1rem;
        }
        
        .product-price {
            font-size: 1.1rem;
        }
        
        .product-description {
            font-size: 0.9rem;
        }
        
        .btn {
            font-size: 0.9rem;
        }
        
        .nav-link {
            font-size: 0.9rem;
        }
        
        .table {
            font-size: 0.9rem;
        }
        
        .form-control {
            font-size: 0.9rem;
        }
        
        .form-label {
            font-size: 0.9rem;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .small {
            font-size: 0.8rem;
        }
        
        .text-muted {
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../logo.png" alt="Store Logo" height="40">
                Online Store
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Shop</a>
                    </li>
                </ul>
                <?php if (!$success): ?>
                <a href="cart.php" class="btn btn-primary">
                    <i class="fas fa-shopping-cart"></i> Cart
                    <span class="badge bg-danger"><?php echo count($_SESSION['shop_cart']); ?></span>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <?php if ($success): ?>
            <!-- Order Confirmation -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                </div>
                <h1 class="mb-4">Thank You for Your Order!</h1>
                <p class="lead">Your order has been received and is being processed.</p>
                <p>Order Number: <strong><?php echo $order_number; ?></strong></p>
                <p>We've sent a confirmation email to <strong><?php echo htmlspecialchars($customer_email); ?></strong> with your order details.</p>
                <div class="mt-5">
                    <a href="index.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i> Continue Shopping
                    </a>
                </div>
            </div>
        <?php else: ?>
            <h1 class="mb-4">Checkout</h1>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <!-- Customer Information Form -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Customer Information</h5>
                        </div>
                        <div class="card-body">
                            <form action="checkout.php" method="POST">
                                <div class="mb-3">
                                    <label for="customer_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="customer_email" name="customer_email" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="customer_phone" name="customer_phone" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">Delivery Address <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="shipping_courier" class="form-label">Shipping Courier <span class="text-danger">*</span></label>
                                    <select class="form-select" id="shipping_courier" name="shipping_courier" required>
                                        <option value="">Select a courier</option>
                                        <?php foreach ($shipping_options as $option): ?>
                                            <option value="<?php echo $option['id']; ?>" 
                                                <?php echo (isset($_POST['shipping_courier']) && $_POST['shipping_courier'] == $option['id']) ? 'selected' : ''; ?>
                                                data-base-rate="<?php echo $option['base_rate']; ?>"
                                                data-rate-per-kg="<?php echo $option['rate_per_kg']; ?>">
                                                <?php echo $option['courier_name']; ?> - Base Rate: ₱<?php echo number_format($option['base_rate'], 2); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="payment_type" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                    <select class="form-select" id="payment_type" name="payment_type" required>
                                        <option value="Cash on Delivery">Cash on Delivery</option>
                                        <option value="Bank Transfer">Bank Transfer</option>
                                        <option value="GCash">GCash</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Order Notes (Optional)</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-check-circle me-2"></i> Place Order
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Qty</th>
                                            <th class="text-end">Price</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($_SESSION['shop_cart'] as $item): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['name']); ?></td>
                                                <td><?php echo $item['quantity']; ?></td>
                                                <td class="text-end">₱<?php echo number_format($item['total'], 2); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="2">Subtotal</th>
                                            <th class="text-end" data-subtotal="<?php echo $cart_total; ?>">₱<?php echo number_format($cart_total, 2); ?></th>
                                        </tr>
                                        <tr>
                                            <th colspan="2">Shipping (<span data-weight="<?php echo $cart_weight; ?>"><?php echo $cart_weight; ?>kg</span>)</th>
                                            <th class="text-end" data-shipping>₱<?php echo number_format($shipping_cost, 2); ?></th>
                                        </tr>
                                        <tr>
                                            <th colspan="2">Total</th>
                                            <th class="text-end" data-total>₱<?php echo number_format($total_with_shipping, 2); ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="d-grid gap-2 mt-3">
                                <a href="cart.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i> Back to Cart
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <?php require_once 'footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/checkout.js"></script>
</body>
</html>