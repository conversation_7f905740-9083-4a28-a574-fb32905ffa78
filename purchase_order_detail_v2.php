<?php
// Move header include down
// require_once 'includes/header.php'; // REMOVE THIS LINE
require_once 'config/database.php';
require_once 'includes/mail_functions.php'; // Uncommented this line

// Start session if not already started (Important: Must be before any output)
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Check if PO number is provided
if (!isset($_GET['po_number']) || empty($_GET['po_number'])) {
    $_SESSION['error_message'] = "Purchase Order number not specified.";
    header('Location: purchase_order_v2.php');
    exit();
}

$conn = connectDB();
$po_number = $conn->real_escape_string($_GET['po_number']);
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch'; // Get current branch

// --- Fetch Purchase Order Header ---
// We fetch one representative row for header info, assuming supplier, dates, status etc. are consistent per PO number.
$header_query = "SELECT
                    po.po_number,
                    po.supplier_id,
                    s.name AS supplier_name,
                    s.email AS supplier_email,
                    s.address AS supplier_address,
                    s.contact_person AS supplier_contact,
                    MIN(po.created_at) AS date_created,
                    MIN(po.expected_delivery_date) AS expected_delivery,
                    MIN(po.status) AS status,
                    MIN(po.notes) AS notes,
                    MIN(po.delivery_address) AS delivery_address,
                    po.ordering_branch
                 FROM
                    purchase_orders po
                 LEFT JOIN
                    suppliers s ON po.supplier_id = s.id
                 WHERE
                    po.po_number = ?
                 GROUP BY po.po_number, po.supplier_id, s.name, s.email, s.address, s.contact_person, po.ordering_branch"; // Group to get one header row

$stmt_header = $conn->prepare($header_query);
if (!$stmt_header) {
    die("Error preparing header query: " . $conn->error);
}
$stmt_header->bind_param("s", $po_number);
$stmt_header->execute();
$header_result = $stmt_header->get_result();

if ($header_result->num_rows === 0) {
    $_SESSION['error_message'] = "Purchase Order not found.";
    closeDB($conn);
    header('Location: purchase_order_v2.php');
    exit();
}

$po_header = $header_result->fetch_assoc();
$stmt_header->close();

// Authorization Check: Ensure user is viewing PO for their branch (or if admin/allowed)
// Add role check if needed: $_SESSION['role'] === 'admin'
if ($po_header['ordering_branch'] !== $branch_name) {
     $_SESSION['error_message'] = "You do not have permission to view this Purchase Order.";
     closeDB($conn);
     header('Location: purchase_order_v2.php');
     exit();
}


// --- Fetch Purchase Order Items ---
// Replaced query based on user request, adjusted for context
$items_query = "SELECT
                   po.id AS item_id,
                   p.name AS product_name,
                   po.sku, -- Assuming SKU is in purchase_orders table now based on join
                   po.quantity,
                   po.unit_price,
                   (po.quantity * po.unit_price) AS item_total -- Changed alias back from Total
                FROM
                   purchase_orders po -- Using alias po
                INNER JOIN
                   products p ON po.sku = p.sku -- Using alias p and joining on SKU
                WHERE
                   po.po_number = ?"; // Kept the essential PO Number filter

$stmt_items = $conn->prepare($items_query);
if (!$stmt_items) {
    die("Error preparing items query: " . $conn->error);
}
$stmt_items->bind_param("s", $po_number);
$stmt_items->execute();
$items_result = $stmt_items->get_result();

$po_items = [];
$grand_total = 0;
while ($item = $items_result->fetch_assoc()) {
    $po_items[] = $item;
    // Ensure this uses the correct alias for the calculated total
    $grand_total += $item['item_total'];
}
$stmt_items->close();

// --- Handle Status Updates (Example) ---
// THIS ENTIRE BLOCK STAYS HERE (BEFORE header.php is included)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Re-establish connection if closed or use the existing $conn if still open
    // For simplicity, assume $conn is available or reconnect if necessary.
    // If $conn was closed before this block, you need: $conn = connectDB();

    // --- Handle Generic Status Update ---
    if (isset($_POST['update_status'])) {
        $new_status = $conn->real_escape_string($_POST['new_status']);
        // Add validation for allowed status transitions if needed

        // Update status for all items in the PO
        $update_query = "UPDATE purchase_orders SET status = ? WHERE po_number = ?";
        $stmt_update = $conn->prepare($update_query);
        if ($stmt_update) {
            $stmt_update->bind_param("ss", $new_status, $po_number);
            if ($stmt_update->execute()) {
                $_SESSION['success_message'] = "Purchase Order status updated successfully.";
                // Refresh data - This happens on redirect anyway
                // $po_header['status'] = $new_status;
            } else {
                $_SESSION['error_message'] = "Error updating status: " . $stmt_update->error;
            }
            $stmt_update->close();
        } else {
             $_SESSION['error_message'] = "Error preparing status update: " . $conn->error;
        }
    }
    // --- Handle Mark as Completed ---
    elseif (isset($_POST['mark_completed'])) {
        // Check current status before proceeding
        if ($po_header['status'] === 'completed' || $po_header['status'] === 'cancelled') {
             $_SESSION['error_message'] = "PO is already completed or cancelled.";
        } else {
            $conn->begin_transaction(); // Start transaction
            $success = true;
            $updated_product_count = 0; // Initialize counter

            // 1. Update PO status to 'completed'
            $update_po_status_query = "UPDATE purchase_orders SET status = 'completed' WHERE po_number = ?";
            $stmt_po_status = $conn->prepare($update_po_status_query);
            if ($stmt_po_status) {
                $stmt_po_status->bind_param("s", $po_number);
                if (!$stmt_po_status->execute()) {
                    $success = false;
                    $_SESSION['error_message'] = "Error updating PO status: " . $stmt_po_status->error;
                }
                $stmt_po_status->close();
            } else {
                $success = false;
                $_SESSION['error_message'] = "Error preparing PO status update: " . $conn->error;
            }

            // 2. Update product quantities if PO status update was successful
            if ($success) {
                // Fetch items again within transaction to be safe, or use $po_items if fetched correctly before
                // Assuming $po_items is already populated correctly from the top of the script
                $update_product_query = "UPDATE products SET quantity = quantity + ? WHERE sku = ? AND branch_name = ?";
                $stmt_product = $conn->prepare($update_product_query);

                if ($stmt_product) {
                    foreach ($po_items as $item) {
                        $item_quantity = $item['quantity'];
                        $item_sku = $item['sku'];
                        // Use the branch name from the PO header
                        $item_branch = $po_header['ordering_branch'];

                        $stmt_product->bind_param("iss", $item_quantity, $item_sku, $item_branch);
                        if (!$stmt_product->execute()) {
                            $success = false;
                            $_SESSION['error_message'] = "Error updating quantity for SKU " . htmlspecialchars($item_sku) . ": " . $stmt_product->error;
                            break; // Stop updating on first error
                        } else {
                            $updated_product_count++; // Increment counter on successful update
                        }
                    }
                    $stmt_product->close();
                } else {
                     $success = false;
                     $_SESSION['error_message'] = "Error preparing product quantity update: " . $conn->error;
                }
            }

            // 3. Commit or Rollback Transaction
            if ($success) {
                $conn->commit();
                // Updated success message to include the count
                $_SESSION['success_message'] = "Purchase Order marked as completed. " . $updated_product_count . " product(s) inventory updated.";
            } else {
                $conn->rollback();
                // Error message already set above
                $updated_product_count = 0; // Reset count on failure
            }
        }
    }

    // Redirect after any POST action to avoid form resubmission
    // Ensure connection is closed if you re-opened it within this block
    // closeDB($conn); // Close if re-opened
    header("Location: purchase_order_detail_v2.php?po_number=" . urlencode($po_number)); // This header call is now safe
    exit();
}


closeDB($conn); // Close the initial connection opened at the top
// NOW include the header, after all PHP logic and potential redirects
require_once 'includes/header.php'; // ADD THIS LINE HERE
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Purchase Order Details: <?php echo htmlspecialchars($po_header['po_number']); ?></h5>
                    <div>
                        <a href="purchase_order_v2.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i> Back to List
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="window.print();">
                            <i class="fas fa-print me-1"></i> Print PO
                        </button>
                        <?php
                            $supplier_email = $po_header['supplier_email'] ?? null;
                            $po_num_encoded = urlencode($po_header['po_number']);
                            $disabled_attr = '';
                            $tooltip_attr = '';
                            if (empty($supplier_email) || !filter_var($supplier_email, FILTER_VALIDATE_EMAIL)) {
                                $disabled_attr = 'disabled';
                                $tooltip_attr = 'data-bs-toggle="tooltip" data-bs-placement="top" title="Supplier email is missing or invalid."';
                            }
                        ?>
                        <a href="send_po_to_supplier.php?po_number=<?php echo $po_num_encoded; ?>"
                           class="btn btn-sm btn-outline-success ms-2 <?php echo $disabled_attr ? 'disabled' : ''; ?>"
                           <?php echo $tooltip_attr; ?>
                           <?php if (!$disabled_attr): ?>
                           onclick="return confirm('Are you sure you want to send this PO (<?php echo htmlspecialchars($po_header['po_number']); ?>) to the supplier: <?php echo htmlspecialchars($supplier_email); ?>?');"
                           <?php endif; ?>>
                            <i class="fas fa-paper-plane me-1"></i> Send to Supplier
                        </a>
                        <!-- Add Mark as Completed Button Form -->
                        <?php if ($po_header['status'] !== 'completed' && $po_header['status'] !== 'cancelled'): ?>
                        <form method="POST" action="purchase_order_detail_v2.php?po_number=<?php echo urlencode($po_number); ?>" style="display: inline;">
                            <input type="hidden" name="mark_completed" value="1">
                            <button type="submit" class="btn btn-sm btn-success ms-2" onclick="return confirm('Are you sure you want to mark this PO as completed? This will update inventory levels.');">
                                <i class="fas fa-check-circle me-1"></i> Mark as Completed
                            </button>
                        </form>
                        <?php endif; ?>
                        <!-- Add Send Email Button if needed -->
                        <!-- <button type="button" class="btn btn-sm btn-outline-success ms-2" data-bs-toggle="modal" data-bs-target="#emailPOModal">
                            <i class="fas fa-envelope me-1"></i> Send Email
                        </button> -->
                    </div>
                </div>
                <div class="card-body">
                    <!-- PO Header Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Supplier Information</h6>
                            <p class="mb-1"><strong>Name:</strong> <?php echo htmlspecialchars($po_header['supplier_name']); ?></p>
                            <p class="mb-1"><strong>Email:</strong> <?php echo htmlspecialchars($po_header['supplier_email'] ?? 'N/A'); ?></p>
                            <p class="mb-1"><strong>Contact:</strong> <?php echo htmlspecialchars($po_header['supplier_contact'] ?? 'N/A'); ?></p>
                            <p class="mb-1"><strong>Address:</strong> <?php echo nl2br(htmlspecialchars($po_header['supplier_address'] ?? 'N/A')); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Order Information</h6>
                            <p class="mb-1"><strong>PO Number:</strong> <?php echo htmlspecialchars($po_header['po_number']); ?></p>
                            <p class="mb-1"><strong>Date Created:</strong> <?php echo date('Y-m-d', strtotime($po_header['date_created'])); ?></p>
                            <p class="mb-1"><strong>Expected Delivery:</strong> <?php echo !empty($po_header['expected_delivery']) ? date('Y-m-d', strtotime($po_header['expected_delivery'])) : 'N/A'; ?></p>
                            <p class="mb-1"><strong>Ordering Branch:</strong> <?php echo htmlspecialchars($po_header['ordering_branch']); ?></p>
                            <p class="mb-1"><strong>Delivery Address:</strong> <?php echo nl2br(htmlspecialchars($po_header['delivery_address'] ?? 'N/A')); ?></p>
                            <p class="mb-1"><strong>Status:</strong>
                                <span class="badge bg-<?php
                                    switch ($po_header['status']) {
                                        case 'draft': echo 'secondary'; break;
                                        case 'pending': echo 'warning'; break;
                                        case 'approved': echo 'info'; break;
                                        case 'sent': echo 'primary'; break;
                                        case 'complete':
                                        case 'completed': echo 'success'; break;
                                        case 'cancelled': echo 'danger'; break;
                                        default: echo 'light';
                                    }
                                ?>"><?php echo ucfirst(htmlspecialchars($po_header['status'])); ?></span>
                            </p>
                        </div>
                    </div>
                     <div class="row mb-3">
                        <div class="col-12">
                             <h6>Notes</h6>
                             <p><?php echo !empty($po_header['notes']) ? nl2br(htmlspecialchars($po_header['notes'])) : '<em>No notes provided.</em>'; ?></p>
                        </div>
                    </div>

                    <!-- PO Items Table -->
                    <h6>Order Items</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>SKU</th>
                                    <th>Product Name</th>
                                    <th class="text-end">Quantity</th>
                                    <th class="text-end">Unit Price</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($po_items)): ?>
                                    <?php $item_count = 1; ?>
                                    <?php foreach ($po_items as $item): ?>
                                    <tr>
                                        <td><?php echo $item_count++; ?></td>
                                        <td><?php echo htmlspecialchars($item['sku'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name'] ?? 'N/A'); ?></td>
                                        <td class="text-end"><?php echo number_format($item['quantity']); ?></td>
                                        <td class="text-end">₱<?php echo number_format($item['unit_price'], 2); ?></td>
                                        <td class="text-end">₱<?php echo number_format($item['item_total'], 2); ?></td>  <?php // Ensure this matches the alias used in the query ('item_total') ?>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center">No items found for this purchase order.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-light">
                                    <td colspan="5" class="text-end"><strong>Grand Total:</strong></td>
                                    <td class="text-end"><strong>₱<?php echo number_format($grand_total, 2); ?></strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-4 d-flex justify-content-end">
                       
                         <?php if ($po_header['status'] !== 'cancelled' && $po_header['status'] !== 'completed'): ?>
                            <form method="POST" action="purchase_order_detail_v2.php?po_number=<?php echo urlencode($po_number); ?>">
                                <input type="hidden" name="new_status" value="cancelled">
                                <button type="submit" name="update_status" class="btn btn-danger" onclick="return confirm('Are you sure you want to cancel this Purchase Order?');">
                                    <i class="fas fa-times-circle me-1"></i> Cancel PO
                                </button>
                            </form>
                        <?php endif; ?>
                        <!-- Add more actions like 'Approve', 'Send' based on status -->
                    </div>

                </div> <!-- End card-body -->
            </div> <!-- End card -->
        </div> <!-- End col-12 -->
    </div> <!-- End row -->
</div> <!-- End container-fluid -->

<!-- Add Email Modal if needed -->
<!--
<div class="modal fade" id="emailPOModal" tabindex="-1" aria-labelledby="emailPOModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="send_po_email_v2.php" method="POST"> // You'll need to create this script
                <div class="modal-header">
                    <h5 class="modal-title" id="emailPOModalLabel">Send PO Email</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="po_number" value="<?php echo htmlspecialchars($po_header['po_number']); ?>">
                    <div class="mb-3">
                        <label for="email_to" class="form-label">To:</label>
                        <input type="email" class="form-control" id="email_to" name="email_to" value="<?php echo htmlspecialchars($po_header['supplier_email'] ?? ''); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_subject" class="form-label">Subject:</label>
                        <input type="text" class="form-control" id="email_subject" name="email_subject" value="Purchase Order: <?php echo htmlspecialchars($po_header['po_number']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_message" class="form-label">Message:</label>
                        <textarea class="form-control" id="email_message" name="email_message" rows="5" required>Please find attached Purchase Order <?php echo htmlspecialchars($po_header['po_number']); ?>.</textarea>
                    </div>
                     <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" id="attach_pdf" name="attach_pdf" checked>
                        <label class="form-check-label" for="attach_pdf">
                            Attach PO PDF (Requires PDF generation logic)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Send Email</button>
                </div>
            </form>
        </div>
    </div>
</div>
-->

<?php require_once 'includes/footer.php'; ?>

<!-- Add this script at the end for Bootstrap tooltips -->
<script>
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })
</script>