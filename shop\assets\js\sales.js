/**
 * Sales page JavaScript functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Make product cards clickable
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        // Add hover effect
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.03)';
            this.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(2deg) rotateY(2deg)';
            this.style.boxShadow = '';
        });
        
        // Add click event to add product to cart
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on form elements
            if (e.target.closest('.product-form') || 
                e.target.tagName === 'INPUT' || 
                e.target.tagName === 'BUTTON') {
                return;
            }
            
            const productId = this.getAttribute('data-product-id');
            const productQuantity = parseInt(this.getAttribute('data-product-quantity'));
            
            // Check if product is in stock
            if (productQuantity <= 0) {
                alert('This product is out of stock.');
                return;
            }
            
            // Find the form within this card
            const form = this.querySelector('.product-form');
            if (form) {
                // Submit the form to add product to cart
                form.submit();
            }
        });
    });
    
    // Prevent form submission from bubbling to card click
    const productForms = document.querySelectorAll('.product-form');
    productForms.forEach(form => {
        form.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
});