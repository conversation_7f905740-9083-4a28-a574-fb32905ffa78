<?php

// Add these at the top of your PHP file
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'includes/header.php';
require_once 'connection.php';

// Get selected branch from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';

// Get total revenue (from completed orders) for selected branch
$query = "SELECT SUM(total_amount) as total_revenue 
          FROM orders 
          WHERE status = 'completed' AND branch_name = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$result = $stmt->get_result();
$total_revenue = $result->fetch_assoc()['total_revenue'] ?: 0;
$stmt->close();

// Get total orders for selected branch
$query = "SELECT COUNT(*) as total_orders 
          FROM orders 
          WHERE branch_name = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$result = $stmt->get_result();
$total_orders = $result->fetch_assoc()['total_orders'];
$stmt->close();

// Get total expenses for selected branch
$query = "SELECT SUM(amount) as total_expenses 
          FROM expenses 
          WHERE branch_name = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$result = $stmt->get_result();
$total_expenses = $result->fetch_assoc()['total_expenses'] ?: 0;
$stmt->close();

// Get low stock products for selected branch
$query = "SELECT p.*, c.name as category_name, b.name as brand_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          LEFT JOIN brands b ON p.brand_id = b.id
          WHERE p.quantity <= p.reorder_level 
          AND p.branch_name = ?
          ORDER BY p.quantity ASC
          LIMIT 10";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$low_stock_result = $stmt->get_result();
$stmt->close();

// Get revenue data for the last 6 months
$revenue_data = [];
$labels = [];
for ($i = 5; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $labels[] = date('M Y', strtotime("-$i months"));
    
    $query = "SELECT SUM(total_amount) as monthly_revenue 
              FROM orders 
              WHERE status = 'completed' 
              AND branch_name = ?              
              AND DATE_FORMAT(created_at, '%Y-%m') = ?";
              
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $branch_name, $month);
    $stmt->execute();
    $result = $stmt->get_result();
    $revenue_data[] = $result->fetch_assoc()['monthly_revenue'] ?: 0;
    $stmt->close();
}

// Get product categories data - completely rewritten to avoid collation issues
$query = "SELECT c.name as category, category_count.count 
          FROM categories c 
          JOIN (
              SELECT category_id, COUNT(*) as count
              FROM products 
              WHERE branch_name = ?
              GROUP BY category_id
          ) as category_count ON c.id = category_count.category_id
          ORDER BY category_count.count DESC 
          LIMIT 5";

$stmt = $conn->prepare($query);
$stmt->bind_param("s", $branch_name);
$stmt->execute();
$categories_result = $stmt->get_result();
$stmt->close();

$category_labels = [];
$category_data = [];
while ($row = $categories_result->fetch_assoc()) {
    $category_labels[] = $row['category'];
    $category_data[] = $row['count'];
}
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="h3 mb-0"><?php echo htmlspecialchars($branch_name); ?> Dashboard</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Total Revenue</p>
                                <h5 class="font-weight-bolder mb-0">
                                    ₱<?php echo number_format($total_revenue, 2); ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="fas fa-peso-sign text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Total Orders</p>
                                <h5 class="font-weight-bolder mb-0">
                                    <?php echo number_format($total_orders); ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                <i class="fas fa-shopping-cart text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Total Expenses</p>
                                <h5 class="font-weight-bolder mb-0">
                                    ₱<?php echo number_format($total_expenses, 2); ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="fas fa-money-bill-wave text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-uppercase font-weight-bold">Low Stock Alerts</p>
                                <h5 class="font-weight-bolder mb-0">
                                    <?php echo $low_stock_result->num_rows; ?>
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                <i class="fas fa-exclamation-triangle text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Revenue Overview</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="revenue-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Product Categories</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="categories-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Low Stock Products</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Product</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Category</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Brand</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Quantity</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Reorder Level</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($low_stock_result->num_rows > 0): ?>
                                    <?php while ($product = $low_stock_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex px-3 py-1">
                                                    <div class="d-flex flex-column justify-content-center">
                                                        <h6 class="mb-0 text-sm"><?php echo htmlspecialchars($product['name']); ?></h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <p class="text-sm font-weight-bold mb-0"><?php echo htmlspecialchars($product['category_name']); ?></p>
                                            </td>
                                            <td>
                                                <p class="text-sm font-weight-bold mb-0"><?php echo htmlspecialchars($product['brand_name']); ?></p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <span class="text-secondary text-sm font-weight-bold"><?php echo $product['quantity']; ?></span>
                                            </td>
                                            <td class="align-middle text-center">
                                                <span class="text-secondary text-sm font-weight-bold"><?php echo $product['reorder_level']; ?></span>
                                            </td>
                                            <td class="align-middle text-center">
                                                <?php if ($product['quantity'] == 0): ?>
                                                    <span class="badge badge-sm bg-gradient-danger">Out of Stock</span>
                                                <?php else: ?>
                                                    <span class="badge badge-sm bg-gradient-warning">Low Stock</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center">No low stock products</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js Scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Revenue Chart
    const revenueCtx = document.getElementById('revenue-chart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($labels); ?>,
            datasets: [{
                label: 'Revenue',
                data: <?php echo json_encode($revenue_data); ?>,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₱' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Categories Chart
    const categoriesCtx = document.getElementById('categories-chart').getContext('2d');
    const categoriesChart = new Chart(categoriesCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($category_labels); ?>,
            datasets: [{
                data: <?php echo json_encode($category_data); ?>,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
</script>

<?php require_once 'includes/footer.php'; ?>
