/* Main Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #212529;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #adb5bd;
    padding: 0.75rem 1rem;
    margin-bottom: 0.2rem;
    border-radius: 0.25rem;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: #0d6efd;
}

.sidebar .nav-link i {
    margin-right: 4px;
    color: #adb5bd;
}

.sidebar .nav-link.active i {
    color: #fff;
}

/* Dashboard Cards */
.card-dashboard {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease;
}

.card-dashboard:hover {
    transform: translateY(-5px);
}

.card-dashboard .card-body {
    padding: 1.5rem;
}

.card-dashboard .icon-shape {
    width: 48px;
    height: 48px;
    background-color: rgba(13, 110, 253, 0.1);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-dashboard .icon-shape i {
    font-size: 1.5rem;
    color: #0d6efd;
}

.card-dashboard.card-revenue .icon-shape {
    background-color: rgba(25, 135, 84, 0.1);
}

.card-dashboard.card-revenue .icon-shape i {
    color: #198754;
}

.card-dashboard.card-orders .icon-shape {
    background-color: rgba(13, 110, 253, 0.1);
}

.card-dashboard.card-orders .icon-shape i {
    color: #0d6efd;
}

.card-dashboard.card-expenses .icon-shape {
    background-color: rgba(220, 53, 69, 0.1);
}

.card-dashboard.card-expenses .icon-shape i {
    color: #dc3545;
}

.card-dashboard.card-alerts .icon-shape {
    background-color: rgba(255, 193, 7, 0.1);
}

.card-dashboard.card-alerts .icon-shape i {
    color: #ffc107;
}

/* Tables */
.table-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
}

.table-container .table {
    margin-bottom: 0;
}

.table-container .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* Alerts */
.alert-low-stock {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #664d03;
}

/* Mobile Responsive */
@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: -100%;
        z-index: 1000;
        width: 80% !important;
        max-width: 300px;
        transition: all 0.3s;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    main {
        width: 100% !important;
        margin-left: 0 !important;
    }
}

/* Charts */
.chart-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Forms */
.form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-container .form-label {
    font-weight: 500;
}

/* Status badges */
.badge-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge-processing {
    background-color: #0dcaf0;
    color: #212529;
}

.badge-completed {
    background-color: #198754;
    color: #fff;
}

.badge-cancelled {
    background-color: #dc3545;
    color: #fff;
}