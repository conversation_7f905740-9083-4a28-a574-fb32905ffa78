<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

// Get database connection
$conn = connectDB();

// Initialize variables
$search = '';
$status_filter = '';
$sort_by = 'created_at';
$sort_order = 'DESC';
$page = 1;
$items_per_page = 20;

// Process search and filters
if (isset($_GET['search'])) {
    $search = trim($_GET['search']);
}

if (isset($_GET['status'])) {
    $status_filter = $_GET['status'];
}

if (isset($_GET['sort_by']) && isset($_GET['sort_order'])) {
    $sort_by = $_GET['sort_by'];
    $sort_order = $_GET['sort_order'];
}

if (isset($_GET['page'])) {
    $page = (int)$_GET['page'];
    if ($page < 1) $page = 1;
}

// Build query for orders
$query = "SELECT * FROM orders WHERE branch_name = 'Online Store'";

// Add search condition
if (!empty($search)) {
    $query .= " AND (order_number LIKE ? OR customer_name LIKE ? OR customer_email LIKE ? OR customer_phone LIKE ?)";
}

// Add status filter
if (!empty($status_filter)) {
    $query .= " AND status = ?";
}

// Add sorting
$query .= " ORDER BY " . $sort_by . " " . $sort_order;

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameters
$bind_types = '';
$bind_params = [];

if (!empty($search)) {
    $search_param = "%$search%";
    $bind_types .= 'ssss';
    $bind_params[] = $search_param;
    $bind_params[] = $search_param;
    $bind_params[] = $search_param;
    $bind_params[] = $search_param;
}

if (!empty($status_filter)) {
    $bind_types .= 's';
    $bind_params[] = $status_filter;
}

if (!empty($bind_params)) {
    $stmt->bind_param($bind_types, ...$bind_params);
}

// Execute query
$stmt->execute();
$result = $stmt->get_result();

// Get total count for pagination
$total_items = $result->num_rows;
$total_pages = ceil($total_items / $items_per_page);

// Apply pagination to results
$offset = ($page - 1) * $items_per_page;
$orders = [];
$count = 0;

while ($row = $result->fetch_assoc()) {
    if ($count >= $offset && count($orders) < $items_per_page) {
        $orders[] = $row;
    }
    $count++;
}

// Process order status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $order_id = (int)$_POST['order_id'];
    $new_status = $_POST['status'];
    
    // Validate status
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (in_array($new_status, $valid_statuses)) {
        $update_query = "UPDATE orders SET status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("si", $new_status, $order_id);
        
        if ($update_stmt->execute()) {
            // Set success message
            $_SESSION['admin_message'] = [
                'type' => 'success',
                'text' => 'Order status updated successfully.'
            ];
        } else {
            // Set error message
            $_SESSION['admin_message'] = [
                'type' => 'danger',
                'text' => 'Error updating order status.'
            ];
        }
        
        // Redirect to avoid resubmission
        header("Location: orders.php");
        exit();
    }
}

// Close connection
closeDB($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Store Orders - Admin</title>
    <link rel="icon" href="../franzsys.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Reduced font sizes for shop pages */
        body {
            font-size: 14px;
        }
        
        h1, .h1 { font-size: 1.8rem; }
        h2, .h2 { font-size: 1.5rem; }
        h3, .h3 { font-size: 1.3rem; }
        h4, .h4 { font-size: 1.1rem; }
        h5, .h5 { font-size: 1rem; }
        h6, .h6 { font-size: 0.9rem; }
        
        .card-title {
            font-size: 1.1rem;
        }
        
        .card-text {
            font-size: 0.9rem;
        }
        
        .product-title {
            font-size: 1rem;
        }
        
        .product-price {
            font-size: 1.1rem;
        }
        
        .product-description {
            font-size: 0.9rem;
        }
        
        .btn {
            font-size: 0.9rem;
        }
        
        .nav-link {
            font-size: 0.9rem;
        }
        
        .table {
            font-size: 0.9rem;
        }
        
        .form-control {
            font-size: 0.9rem;
        }
        
        .form-label {
            font-size: 0.9rem;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .small {
            font-size: 0.8rem;
        }
        
        .text-muted {
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../logo.png" alt="Store Logo" height="40">
                Online Store - Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Shop</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="orders.php">Orders</a>
                    </li>
                </ul>
                <a href="../index.php" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i> Back to Admin
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <h1 class="mb-4">Online Store Orders</h1>
        
        <?php if (isset($_SESSION['admin_message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['admin_message']['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['admin_message']['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['admin_message']); ?>
        <?php endif; ?>
        
        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-md-8">
                <form action="orders.php" method="GET" class="d-flex">
                    <select name="status" class="form-select me-2" style="width: auto;">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>Pending</option>
                        <option value="processing" <?php echo ($status_filter == 'processing') ? 'selected' : ''; ?>>Processing</option>
                        <option value="completed" <?php echo ($status_filter == 'completed') ? 'selected' : ''; ?>>Completed</option>
                        <option value="cancelled" <?php echo ($status_filter == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                    <button type="submit" class="btn btn-primary">Filter</button>
                </form>
            </div>
            <div class="col-md-4">
                <form action="orders.php" method="GET" class="d-flex">
                    <?php if (!empty($status_filter)): ?>
                        <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                    <?php endif; ?>
                    <input type="text" name="search" class="form-control me-2" placeholder="Search orders..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-primary">Search</button>
                </form>
            </div>
        </div>
        
        <!-- Orders Table -->
        <?php if (count($orders) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                <td>
                                    <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong><br>
                                    <small><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                </td>
                                <td><?php echo date('M d, Y h:i A', strtotime($order['created_at'])); ?></td>
                                <td>₱<?php echo number_format($order['total_amount'], 2); ?></td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo ($order['status'] == 'completed') ? 'success' : 
                                            (($order['status'] == 'processing') ? 'primary' : 
                                                (($order['status'] == 'cancelled') ? 'danger' : 'warning')); 
                                    ?>">
                                        <?php echo ucfirst($order['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info view-order" data-bs-toggle="modal" data-bs-target="#orderModal" data-order-id="<?php echo $order['id']; ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary update-status" data-bs-toggle="modal" data-bs-target="#statusModal" data-order-id="<?php echo $order['id']; ?>" data-order-status="<?php echo $order['status']; ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page - 1); ?><?php echo (!empty($search)) ? '&search=' . urlencode($search) : ''; ?><?php echo (!empty($status_filter)) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo (!empty($search)) ? '&search=' . urlencode($search) : ''; ?><?php echo (!empty($status_filter)) ? '&status=' . urlencode($status_filter) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page + 1); ?><?php echo (!empty($search)) ? '&search=' . urlencode($search) : ''; ?><?php echo (!empty($status_filter)) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h3>No Orders Found</h3>
                <p>There are no online orders matching your criteria.</p>
                <?php if (!empty($search) || !empty($status_filter)): ?>
                    <a href="orders.php" class="btn btn-primary mt-3">View All Orders</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderModal" tabindex="-1" aria-labelledby="orderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderModalLabel">Order Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="orderDetails">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading order details...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusModalLabel">Update Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="updateStatusForm" action="orders.php" method="POST">
                        <input type="hidden" name="order_id" id="statusOrderId">
                        <input type="hidden" name="update_status" value="1">
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Update Status</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Admin Panel</h5>
                    <p>Manage your online store orders and inventory.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../index.php" class="text-white">Back to Main System</a>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> Online Store. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Update status modal
        document.querySelectorAll('.update-status').forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const orderStatus = this.getAttribute('data-order-status');
                
                document.getElementById('statusOrderId').value = orderId;
                document.getElementById('status').value = orderStatus;
            });
        });
        
        // View order details
        document.querySelectorAll('.view-order').forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const detailsContainer = document.getElementById('orderDetails');
                
                // Show loading
                detailsContainer.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Loading order details...</p>
                    </div>
                `;
                
                // Fetch order details
                fetch('get_order_details.php?id=' + orderId)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Format order details
                            let html = `
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h6>Order Information</h6>
                                        <p><strong>Order Number:</strong> ${data.order.order_number}</p>
                                        <p><strong>Date:</strong> ${new Date(data.order.created_at).toLocaleString()}</p>
                                        <p><strong>Status:</strong> <span class="badge bg-${getStatusColor(data.order.status)}">${capitalizeFirstLetter(data.order.status)}</span></p>
                                        <p><strong>Payment Method:</strong> ${data.order.payment_type}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Customer Information</h6>
                                        <p><strong>Name:</strong> ${data.order.customer_name}</p>
                                        <p><strong>Email:</strong> ${data.order.customer_email}</p>
                                        <p><strong>Phone:</strong> ${data.order.customer_phone}</p>
                                        <p><strong>Address:</strong> ${data.order.notes}</p>
                                    </div>
                                </div>
                                
                                <h6>Order Items</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Price</th>
                                                <th>Quantity</th>
                                                <th class="text-end">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                            `;
                            
                            data.items.forEach(item => {
                                html += `
                                    <tr>
                                        <td>${item.product_name}</td>
                                        <td>₱${parseFloat(item.unit_price).toFixed(2)}</td>
                                        <td>${item.quantity}</td>
                                        <td class="text-end">₱${parseFloat(item.total_price).toFixed(2)}</td>
                                    </tr>
                                `;
                            });
                            
                            html += `
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="3" class="text-end">Total:</th>
                                                <th class="text-end">₱${parseFloat(data.order.total_amount).toFixed(2)}</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            `;
                            
                            detailsContainer.innerHTML = html;
                        } else {
                            detailsContainer.innerHTML = `<div class="alert alert-danger">Error loading order details.</div>`;
                        }
                    })
                    .catch(error => {
                        detailsContainer.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                    });
            });
        });
        
        // Helper functions
        function getStatusColor(status) {
            switch(status) {
                case 'completed': return 'success';
                case 'processing': return 'primary';
                case 'cancelled': return 'danger';
                default: return 'warning';
            }
        }
        
        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }
    </script>
</body>
</html>