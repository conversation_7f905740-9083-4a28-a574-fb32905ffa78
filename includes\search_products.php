<?php
// Only start session if one doesn't already exist
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([]);
    exit();
}

// Get database connection
$conn = connectDB();

// Get search term and supplier ID
$term = isset($_GET['term']) ? trim($_GET['term']) : '';
$supplier_id = isset($_GET['supplier_id']) ? intval($_GET['supplier_id']) : 0;

if (empty($term)) {
    echo json_encode([]);
    exit();
}

// Prepare SQL
$sql = "SELECT p.id, p.name, p.sku, p.unit_price, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE (p.name LIKE ? OR p.sku LIKE ?)";

$search_param = "%{$term}%";
$params = [$search_param, $search_param];
$types = "ss";

// Add supplier filter if provided
if ($supplier_id > 0) {
    $sql .= " AND (p.supplier_id = ? OR p.supplier_id IS NULL)";
    $params[] = $supplier_id;
    $types .= "i";
}

$sql .= " ORDER BY p.name ASC LIMIT 20";

// Execute query
$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

// Fetch products
$products = [];
while ($row = $result->fetch_assoc()) {
    $products[] = [
        'id' => $row['id'],
        'name' => htmlspecialchars($row['name']),
        'sku' => htmlspecialchars($row['sku']),
        'unit_price' => number_format((float)$row['unit_price'], 2, '.', ''),
        'category_name' => htmlspecialchars($row['category_name'] ?? '')
    ];
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($products); 