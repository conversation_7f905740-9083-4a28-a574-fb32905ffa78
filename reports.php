<?php
require_once 'includes/header.php';
require_once 'config/database.php';

// Get database connection
$conn = connectDB();

// Get branch name from session
$branch_name = isset($_SESSION['branch_name']) ? $_SESSION['branch_name'] : 'Main Branch';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Reports</h1>
    </div>
    
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Select a Report</h5>
                    <p class="card-text">Choose one of the available reports below to view detailed information.</p>
                </div>
            </div>
        </div>
    </div>
    
    <style>
        .report-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            height: 100%;
            position: relative;
            overflow: hidden;
            border-left: 5px solid;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        
        .report-card .card-body {
            padding: 1.5rem;
        }
        
        .report-card .icon-bg {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 5rem;
            opacity: 0.1;
            transform: rotate(10deg);
        }
        
        .inventory-report {
            background-color: #f8f9fa;
            border-color: #28a745;
        }
        
        .sales-report {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .expenses-report {
            background-color: #f8f9fa;
            border-color: #dc3545;
        }
    </style>
    
    <div class="row">
        <!-- Inventory Report Card -->
        <div class="col-md-3 mb-4">
            <a href="inventory_report.php" class="text-decoration-none">
                <div class="card report-card inventory-report">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            <i class="fas fa-boxes me-2"></i>
                            Inventory Report
                        </h5>
                        <p class="card-text">View current stock levels, inventory valuation, and product movement analysis.</p>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-success">View Report</button>
                        </div>
                        <div class="icon-bg">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Daily Sales Report Card -->
        <div class="col-md-3 mb-4">
            <a href="sales_list.php" class="text-decoration-none">
                <div class="card report-card sales-report">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-chart-line me-2"></i>
                            Daily Sales Report
                        </h5>
                        <p class="card-text">Track daily sales performance, payment methods, and transaction details.</p>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary">View Report</button>
                        </div>
                        <div class="icon-bg">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Purchase Orders Report Card -->
        <div class="col-md-3 mb-4">
            <a href="purchase_order_report.php" class="text-decoration-none">
                <div class="card report-card" style="border-color: #6f42c1; background-color: #f8f9fa;">
                    <div class="card-body">
                        <h5 class="card-title text-purple" style="color: #6f42c1;">
                            <i class="fas fa-shopping-cart me-2"></i>
                            Purchase Orders Report
                        </h5>
                        <p class="card-text">View purchase order history, supplier analysis, and procurement trends.</p>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-purple" style="border-color: #6f42c1; color: #6f42c1;">View Report</button>
                        </div>
                        <div class="icon-bg">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Expenses Report Card -->
        <div class="col-md-3 mb-4">
            <a href="expenses.php" class="text-decoration-none">
                <div class="card report-card expenses-report">
                    <div class="card-body">
                        <h5 class="card-title text-danger">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Expenses Report
                        </h5>
                        <p class="card-text">Monitor all expenses, categorize spending, and analyze cost trends.</p>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-danger">View Report</button>
                        </div>
                        <div class="icon-bg">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Customer Ledger Report Card -->
        <div class="col-md-3 mb-4">
            <a href="customer_ledger.php" class="text-decoration-none">
                <div class="card report-card" style="border-color: #fd7e14; background-color: #f8f9fa;">
                    <div class="card-body">
                        <h5 class="card-title" style="color: #fd7e14;">
                            <i class="fas fa-users me-2"></i>
                            Customer Ledger
                        </h5>
                        <p class="card-text">Track customer transactions, payment history, and outstanding balances.</p>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline" style="border-color: #fd7e14; color: #fd7e14;">View Report</button>
                        </div>
                        <div class="icon-bg">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

<?php
// Close database connection
$conn->close();

// Include footer
require_once 'includes/footer.php';
?>
